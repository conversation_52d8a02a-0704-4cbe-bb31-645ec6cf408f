{"id": "doc_demo_has_1751560180", "timestamp": "2025-07-04T00:29:40.550132", "document": {"title": "Ewandata系统演示文档", "content": "这是Ewandata知识管理系统的演示文档。\n\n系统主要功能包括：\n1. 多格式文档处理 - 支持PDF、DOCX、Markdown等格式\n2. AI内容分析 - 自动摘要、关键词提取、主题分类\n3. 语义搜索 - 基于向量数据库的智能搜索\n4. 项目跟踪 - 自动发现和分析项目关联性\n5. GitHub集成 - 自动同步和版本控制\n\n系统采用本地部署，充分利用RTX 4070 12GB GPU的计算能力。", "file_path": "demo_document.md", "file_hash": "demo_hash_12345", "type": "markdown", "size": 1024, "tags": ["演示", "知识管理", "AI"], "keywords": ["Ewandata", "文档处理", "语义搜索", "GPU"], "summary": "Ewandata知识管理系统功能演示文档", "importance_score": 0.9}}