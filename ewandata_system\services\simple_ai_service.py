"""
简化的AI服务
使用基础的transformers功能
"""

import logging
from typing import List, Dict, Any
import re

logger = logging.getLogger(__name__)

class SimpleAIService:
    """简化的AI服务类"""
    
    def __init__(self):
        self.is_initialized = False
        self.model = None
        
    def initialize_model(self):
        """初始化简单模型"""
        try:
            from transformers import pipeline
            
            # 使用情感分析作为基础功能
            self.model = pipeline('sentiment-analysis')
            self.is_initialized = True
            logger.info("✅ 简化AI模型初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 简化AI模型初始化失败: {e}")
            return False
    
    def extract_keywords(self, content: str, max_keywords: int = 10) -> List[str]:
        """简单的关键词提取（基于规则）"""
        if not content:
            return []
        
        # 简单的关键词提取逻辑
        import re
        
        # 移除标点符号，转换为小写
        clean_content = re.sub(r'[^\w\s]', ' ', content.lower())
        words = clean_content.split()
        
        # 过滤停用词
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '着', '过',
                     'the', 'is', 'at', 'which', 'on', 'and', 'or', 'but', 'in', 'with'}
        
        # 统计词频
        word_freq = {}
        for word in words:
            if len(word) > 2 and word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in keywords[:max_keywords]]
    
    def generate_summary(self, content: str, max_length: int = 200) -> str:
        """简单的摘要生成（提取前几句）"""
        if not content:
            return ""
        
        # 简单的摘要：取前两句话
        sentences = re.split(r'[.!?。！？]', content)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        summary = '. '.join(sentences[:2])
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
    
    def classify_content(self, content: str) -> Dict[str, Any]:
        """简单的内容分类"""
        if not content:
            return {"category": "空文档", "topics": [], "importance": 1, "tags": []}
        
        # 基于关键词的简单分类
        content_lower = content.lower()
        
        category = "其他"
        topics = []
        tags = []
        
        # 技术文档
        if any(word in content_lower for word in ['python', 'java', 'javascript', 'api', '代码', '算法']):
            category = "技术文档"
            topics.append("编程")
            tags.append("技术")
        
        # 项目管理
        elif any(word in content_lower for word in ['项目', '计划', '任务', '目标', '进度']):
            category = "项目管理"
            topics.append("管理")
            tags.append("项目")
        
        # 学习笔记
        elif any(word in content_lower for word in ['学习', '笔记', '总结', '知识', '概念']):
            category = "学习笔记"
            topics.append("学习")
            tags.append("笔记")
        
        # AI相关
        if any(word in content_lower for word in ['ai', '人工智能', '机器学习', '深度学习', 'ml', 'dl']):
            topics.append("人工智能")
            tags.append("AI")
        
        # 计算重要性（基于长度和关键词密度）
        importance = min(10, max(1, len(content) // 100 + len(topics)))
        
        return {
            "category": category,
            "topics": topics,
            "importance": importance,
            "tags": tags
        }
    
    def enhance_document_data(self, content: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """增强文档数据"""
        if not content or len(content.strip()) < 10:
            return {
                "keywords": [],
                "summary": "",
                "classification": {"category": "空文档", "topics": [], "importance": 1, "tags": []},
                "ai_enhanced": False
            }
        
        try:
            # 提取关键词
            keywords = self.extract_keywords(content)
            
            # 生成摘要
            summary = self.generate_summary(content)
            
            # 内容分类
            classification = self.classify_content(content)
            
            return {
                "keywords": keywords,
                "summary": summary,
                "classification": classification,
                "ai_enhanced": True,
                "ai_type": "simple_rule_based"
            }
            
        except Exception as e:
            logger.error(f"简化AI处理失败: {e}")
            return {
                "keywords": [],
                "summary": f"处理失败: {str(e)}",
                "classification": {"category": "处理失败", "topics": [], "importance": 1, "tags": []},
                "ai_enhanced": False
            }


# 全局实例
_simple_ai_service = None

def get_simple_ai_service() -> SimpleAIService:
    """获取简化AI服务实例"""
    global _simple_ai_service
    if _simple_ai_service is None:
        _simple_ai_service = SimpleAIService()
    return _simple_ai_service
