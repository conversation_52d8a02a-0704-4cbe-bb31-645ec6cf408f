{"id": "doc_995d32f8_1751560197", "timestamp": "2025-07-04T00:29:57.622908", "document": {"file_name": "Ewandata实施方案.md", "file_path": "E:\\Ewandata\\Ewandata实施方案.md", "file_extension": ".md", "size": 31682, "size_mb": 0.030214309692382812, "created_time": "2025-07-03T23:25:11.423637", "modified_time": "2025-07-03T23:28:36.000846", "file_hash": "995d32f83294db2ee4c992eb3c7c762e", "mime_type": "text/markdown", "type": "markdown", "content": "# Ewandata 本地知识管理系统实施方案\n\n## 项目概述\n\n基于您的需求，我为您设计了一个完整的本地知识管理系统\"Ewandata\"。该系统将充分利用您的RTX 4070 12GB GPU，实现完全本地化的AI驱动知识管理。\n\n## 系统架构设计\n\n### 核心组件架构\n```\nEwandata系统架构：\n├── 数据输入层\n│   ├── 临时记文件夹监控 (C:\\Users\\<USER>\\Desktop\\临时记)\n│   ├── 多格式文档处理器\n│   └── 网页内容抓取器\n├── AI处理层\n│   ├── Microsoft Phi-3-mini (主要推理引擎)\n│   ├── 文本嵌入模型 (语义理解)\n│   └── OCR引擎 (图片文字识别)\n├── 知识存储层\n│   ├── 向量数据库 (ChromaDB)\n│   ├── 关系数据库 (SQLite)\n│   └── 文件系统 (Markdown + JSON)\n├── 应用服务层\n│   ├── FastAPI后端服务\n│   ├── 项目跟踪服务\n│   └── GitHub同步服务\n└── 用户界面层\n    ├── Web管理界面\n    ├── Obsidian集成\n    └── API接口\n```\n\n### 技术栈选择\n\n#### 推荐配置 (RTX 4070 12GB优化)\n- **主要AI模型**: Microsoft/Phi-3-mini-4k-instruct (3.8B参数)\n- **嵌入模型**: sentence-transformers/all-MiniLM-L6-v2\n- **后端框架**: Python + FastAPI\n- **向量数据库**: ChromaDB (本地部署)\n- **前端界面**: Streamlit (快速开发) + React (完整版)\n- **文档处理**: LangChain + PyPDF2 + python-docx\n- **OCR引擎**: PaddleOCR (支持中文)\n\n#### GPU内存分配策略\n```\nRTX 4070 12GB 内存分配：\n├── Phi-3-mini模型: 4-5GB\n├── 嵌入模型: 1-2GB\n├── 向量数据库缓存: 2-3GB\n├── 系统缓存: 2GB\n└── 预留空间: 1-2GB\n```\n\n## 详细实施计划\n\n### 第一阶段：环境搭建与基础配置 (1-2周)\n\n#### 1.1 开发环境准备\n```bash\n# 创建Python虚拟环境\npython -m venv ewandata_env\newandata_env\\Scripts\\activate\n\n# 安装核心依赖\npip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\npip install transformers accelerate\npip install fastapi uvicorn\npip install chromadb\npip install langchain\npip install streamlit\n```\n\n#### 1.2 AI模型下载与配置\n```python\n# models/model_manager.py\nfrom transformers import AutoTokenizer, AutoModelForCausalLM\nimport torch\n\nclass ModelManager:\n    def __init__(self):\n        self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n        self.load_models()\n\n    def load_models(self):\n        # 加载Phi-3-mini模型\n        self.tokenizer = AutoTokenizer.from_pretrained(\n            \"microsoft/Phi-3-mini-4k-instruct\"\n        )\n        self.model = AutoModelForCausalLM.from_pretrained(\n            \"microsoft/Phi-3-mini-4k-instruct\",\n            torch_dtype=torch.float16,\n            device_map=\"auto\"\n        )\n```\n\n#### 1.3 项目结构创建\n```\nE:\\Ewandata\\\n├── ewandata_system\\           # 系统核心代码\n│   ├── __init__.py\n│   ├── config\\               # 配置文件\n│   ├── models\\               # AI模型管理\n│   ├── processors\\           # 文档处理器\n│   ├── storage\\              # 存储管理\n│   ├── api\\                  # API接口\n│   └── utils\\                # 工具函数\n├── data\\                     # 数据存储\n│   ├── raw\\                  # 原始数据\n│   ├── processed\\            # 处理后数据\n│   ├── knowledge_base\\       # 知识库\n│   └── vectors\\              # 向量数据\n├── temp\\                     # 临时文件\n├── logs\\                     # 日志文件\n├── tests\\                    # 测试文件\n├── docs\\                     # 文档\n├── requirements.txt          # 依赖列表\n└── main.py                   # 主程序入口\n```\n\n### 第二阶段：核心功能开发 (2-3周)\n\n#### 2.1 文档处理引擎开发\n\n```python\n# processors/document_processor.py\nimport os\nimport mimetypes\nfrom pathlib import Path\nfrom typing import Dict, List, Any\nimport PyPDF2\nimport docx\nfrom PIL import Image\nimport paddleocr\n\nclass DocumentProcessor:\n    def __init__(self):\n        self.ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')\n        self.supported_formats = {\n            '.pdf': self._process_pdf,\n            '.docx': self._process_docx,\n            '.txt': self._process_text,\n            '.md': self._process_markdown,\n            '.png': self._process_image,\n            '.jpg': self._process_image,\n            '.jpeg': self._process_image\n        }\n\n    def process_file(self, file_path: str) -> Dict[str, Any]:\n        \"\"\"处理单个文件\"\"\"\n        file_ext = Path(file_path).suffix.lower()\n        if file_ext in self.supported_formats:\n            return self.supported_formats[file_ext](file_path)\n        else:\n            return {\"error\": f\"不支持的文件格式: {file_ext}\"}\n\n    def _process_pdf(self, file_path: str) -> Dict[str, Any]:\n        \"\"\"处理PDF文件\"\"\"\n        with open(file_path, 'rb') as file:\n            reader = PyPDF2.PdfReader(file)\n            text = \"\"\n            for page in reader.pages:\n                text += page.extract_text()\n        return {\n            \"type\": \"pdf\",\n            \"content\": text,\n            \"pages\": len(reader.pages),\n            \"metadata\": {\"file_path\": file_path}\n        }\n\n    def _process_image(self, file_path: str) -> Dict[str, Any]:\n        \"\"\"处理图片文件（OCR）\"\"\"\n        result = self.ocr.ocr(file_path, cls=True)\n        text = \"\"\n        for line in result:\n            for word_info in line:\n                text += word_info[1][0] + \" \"\n        return {\n            \"type\": \"image\",\n            \"content\": text,\n            \"metadata\": {\"file_path\": file_path, \"ocr_confidence\": \"high\"}\n        }\n```\n\n#### 2.2 AI分析引擎实现\n\n```python\n# models/ai_analyzer.py\nfrom transformers import pipeline\nimport torch\nfrom sentence_transformers import SentenceTransformer\n\nclass AIAnalyzer:\n    def __init__(self, model_manager):\n        self.model_manager = model_manager\n        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')\n        self.summarizer = pipeline(\"summarization\",\n                                 model=\"microsoft/DialoGPT-medium\",\n                                 device=0 if torch.cuda.is_available() else -1)\n\n    def analyze_content(self, content: str) -> Dict[str, Any]:\n        \"\"\"全面分析内容\"\"\"\n        return {\n            \"summary\": self._generate_summary(content),\n            \"keywords\": self._extract_keywords(content),\n            \"topics\": self._classify_topics(content),\n            \"importance_score\": self._calculate_importance(content),\n            \"embeddings\": self._generate_embeddings(content)\n        }\n\n    def _generate_summary(self, content: str) -> str:\n        \"\"\"生成内容摘要\"\"\"\n        if len(content) > 1000:\n            # 使用Phi-3生成摘要\n            prompt = f\"请为以下内容生成简洁的摘要：\\n{content[:1000]}...\"\n            return self._query_phi3(prompt)\n        return content\n\n    def _extract_keywords(self, content: str) -> List[str]:\n        \"\"\"提取关键词\"\"\"\n        prompt = f\"从以下内容中提取5-10个关键词：\\n{content[:500]}\"\n        keywords_text = self._query_phi3(prompt)\n        return [kw.strip() for kw in keywords_text.split(',')]\n\n    def _query_phi3(self, prompt: str) -> str:\n        \"\"\"查询Phi-3模型\"\"\"\n        inputs = self.model_manager.tokenizer(prompt, return_tensors=\"pt\")\n        with torch.no_grad():\n            outputs = self.model_manager.model.generate(\n                inputs.input_ids,\n                max_length=200,\n                temperature=0.7,\n                do_sample=True\n            )\n        return self.model_manager.tokenizer.decode(outputs[0], skip_special_tokens=True)\n```\n\n#### 2.3 知识库存储系统\n\n```python\n# storage/knowledge_base.py\nimport chromadb\nimport json\nimport sqlite3\nfrom datetime import datetime\nfrom typing import Dict, List, Any\n\nclass KnowledgeBase:\n    def __init__(self, db_path: str):\n        self.db_path = db_path\n        self.chroma_client = chromadb.PersistentClient(path=f\"{db_path}/vectors\")\n        self.collection = self.chroma_client.get_or_create_collection(\"ewandata\")\n        self.init_sqlite()\n\n    def init_sqlite(self):\n        \"\"\"初始化SQLite数据库\"\"\"\n        self.conn = sqlite3.connect(f\"{self.db_path}/metadata.db\")\n        self.conn.execute('''\n            CREATE TABLE IF NOT EXISTS documents (\n                id TEXT PRIMARY KEY,\n                title TEXT,\n                content TEXT,\n                file_path TEXT,\n                created_at TIMESTAMP,\n                updated_at TIMESTAMP,\n                tags TEXT,\n                importance_score REAL\n            )\n        ''')\n        self.conn.commit()\n\n    def store_document(self, doc_data: Dict[str, Any]) -> str:\n        \"\"\"存储文档到知识库\"\"\"\n        doc_id = self._generate_doc_id()\n\n        # 存储到向量数据库\n        self.collection.add(\n            documents=[doc_data['content']],\n            metadatas=[{\n                \"title\": doc_data.get('title', ''),\n                \"file_path\": doc_data.get('file_path', ''),\n                \"tags\": ','.join(doc_data.get('tags', []))\n            }],\n            ids=[doc_id]\n        )\n\n        # 存储到SQLite\n        self.conn.execute('''\n            INSERT INTO documents\n            (id, title, content, file_path, created_at, updated_at, tags, importance_score)\n            VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n        ''', (\n            doc_id,\n            doc_data.get('title', ''),\n            doc_data['content'],\n            doc_data.get('file_path', ''),\n            datetime.now(),\n            datetime.now(),\n            ','.join(doc_data.get('tags', [])),\n            doc_data.get('importance_score', 0.5)\n        ))\n        self.conn.commit()\n\n        # 生成Markdown文件\n        self._save_as_markdown(doc_id, doc_data)\n\n        # 生成JSON文件\n        self._save_as_json(doc_id, doc_data)\n\n        return doc_id\n\n    def search_similar(self, query: str, n_results: int = 5) -> List[Dict]:\n        \"\"\"语义搜索相似内容\"\"\"\n        results = self.collection.query(\n            query_texts=[query],\n            n_results=n_results\n        )\n        return results\n```\n\n### 第三阶段：高级功能集成 (2-3周)\n\n#### 3.1 项目跟踪系统\n\n```python\n# services/project_tracker.py\nimport os\nimport git\nfrom pathlib import Path\nfrom watchdog.observers import Observer\nfrom watchdog.events import FileSystemEventHandler\nfrom typing import Dict, List, Set\n\nclass ProjectTracker:\n    def __init__(self, monitor_path: str = \"E:\\\\\"):\n        self.monitor_path = monitor_path\n        self.projects = {}\n        self.project_patterns = {\n            'python': ['requirements.txt', 'setup.py', 'pyproject.toml'],\n            'nodejs': ['package.json', 'yarn.lock', 'npm-shrinkwrap.json'],\n            'java': ['pom.xml', 'build.gradle'],\n            'csharp': ['*.csproj', '*.sln'],\n            'web': ['index.html', 'webpack.config.js'],\n            'general': ['README.md', '.git']\n        }\n\n    def scan_projects(self) -> Dict[str, Any]:\n        \"\"\"扫描E盘所有项目\"\"\"\n        projects = {}\n        for root, dirs, files in os.walk(self.monitor_path):\n            if self._is_project_directory(root, files):\n                project_info = self._analyze_project(root, files)\n                projects[root] = project_info\n        return projects\n\n    def _is_project_directory(self, path: str, files: List[str]) -> bool:\n        \"\"\"判断是否为项目目录\"\"\"\n        for pattern_type, patterns in self.project_patterns.items():\n            for pattern in patterns:\n                if any(pattern in f for f in files):\n                    return True\n        return False\n\n    def _analyze_project(self, path: str, files: List[str]) -> Dict[str, Any]:\n        \"\"\"分析项目详情\"\"\"\n        project_info = {\n            'path': path,\n            'name': os.path.basename(path),\n            'type': self._detect_project_type(files),\n            'last_modified': self._get_last_modified(path),\n            'size': self._get_directory_size(path),\n            'git_info': self._get_git_info(path),\n            'dependencies': self._analyze_dependencies(path, files),\n            'structure': self._analyze_structure(path)\n        }\n        return project_info\n\n    def find_related_projects(self, project_path: str) -> List[Dict[str, Any]]:\n        \"\"\"找到相关项目\"\"\"\n        current_project = self.projects.get(project_path)\n        if not current_project:\n            return []\n\n        related = []\n        for path, project in self.projects.items():\n            if path != project_path:\n                similarity = self._calculate_similarity(current_project, project)\n                if similarity > 0.3:  # 相似度阈值\n                    related.append({\n                        'project': project,\n                        'similarity': similarity,\n                        'relation_type': self._determine_relation_type(current_project, project)\n                    })\n\n        return sorted(related, key=lambda x: x['similarity'], reverse=True)\n```\n\n#### 3.2 GitHub集成与同步\n\n```python\n# services/github_sync.py\nimport git\nimport os\nimport shutil\nfrom datetime import datetime\nfrom typing import Dict, List\nimport schedule\nimport time\n\nclass GitHubSync:\n    def __init__(self, repo_path: str, remote_url: str):\n        self.repo_path = repo_path\n        self.remote_url = remote_url\n        self.repo = git.Repo(repo_path)\n        self.setup_sync_schedule()\n\n    def setup_sync_schedule(self):\n        \"\"\"设置同步计划\"\"\"\n        # 每天晚上11点自动同步\n        schedule.every().day.at(\"23:00\").do(self.auto_sync)\n        # 每次有新内容时触发同步\n        self.setup_file_watcher()\n\n    def auto_sync(self):\n        \"\"\"自动同步到GitHub\"\"\"\n        try:\n            # 添加所有变更\n            self.repo.git.add('.')\n\n            # 检查是否有变更\n            if self.repo.is_dirty():\n                # 提交变更\n                commit_message = f\"Auto sync: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\"\n                self.repo.index.commit(commit_message)\n\n                # 推送到远程\n                origin = self.repo.remote('origin')\n                origin.push()\n\n                print(f\"成功同步到GitHub: {commit_message}\")\n\n                # 清理本地临时文件\n                self.cleanup_temp_files()\n            else:\n                print(\"没有变更需要同步\")\n\n        except Exception as e:\n            print(f\"同步失败: {str(e)}\")\n\n    def cleanup_temp_files(self):\n        \"\"\"清理本地临时文件\"\"\"\n        temp_dirs = ['temp', 'cache', '__pycache__']\n        for temp_dir in temp_dirs:\n            temp_path = os.path.join(self.repo_path, temp_dir)\n            if os.path.exists(temp_path):\n                shutil.rmtree(temp_path)\n                print(f\"清理临时目录: {temp_path}\")\n\n    def sync_knowledge_base(self, knowledge_base_path: str):\n        \"\"\"同步知识库内容\"\"\"\n        # 只同步处理后的Markdown和JSON文件\n        sync_patterns = ['*.md', '*.json']\n\n        for pattern in sync_patterns:\n            files = Path(knowledge_base_path).glob(f\"**/{pattern}\")\n            for file in files:\n                # 复制到Git仓库\n                relative_path = file.relative_to(knowledge_base_path)\n                target_path = Path(self.repo_path) / relative_path\n                target_path.parent.mkdir(parents=True, exist_ok=True)\n                shutil.copy2(file, target_path)\n```\n\n#### 3.3 临时记文件夹监控\n\n```python\n# services/temp_folder_monitor.py\nfrom watchdog.observers import Observer\nfrom watchdog.events import FileSystemEventHandler\nimport time\nimport asyncio\n\nclass TempFolderHandler(FileSystemEventHandler):\n    def __init__(self, processor_queue):\n        self.processor_queue = processor_queue\n\n    def on_created(self, event):\n        if not event.is_directory:\n            print(f\"新文件检测到: {event.src_path}\")\n            # 添加到处理队列\n            self.processor_queue.put(event.src_path)\n\n    def on_modified(self, event):\n        if not event.is_directory:\n            print(f\"文件修改检测到: {event.src_path}\")\n            self.processor_queue.put(event.src_path)\n\nclass TempFolderMonitor:\n    def __init__(self, temp_folder_path: str, document_processor, ai_analyzer, knowledge_base):\n        self.temp_folder_path = temp_folder_path\n        self.document_processor = document_processor\n        self.ai_analyzer = ai_analyzer\n        self.knowledge_base = knowledge_base\n        self.observer = Observer()\n        self.processor_queue = asyncio.Queue()\n\n    def start_monitoring(self):\n        \"\"\"开始监控临时记文件夹\"\"\"\n        if not os.path.exists(self.temp_folder_path):\n            os.makedirs(self.temp_folder_path)\n            print(f\"创建临时记文件夹: {self.temp_folder_path}\")\n\n        event_handler = TempFolderHandler(self.processor_queue)\n        self.observer.schedule(event_handler, self.temp_folder_path, recursive=True)\n        self.observer.start()\n\n        # 启动处理协程\n        asyncio.create_task(self.process_files())\n\n        print(f\"开始监控文件夹: {self.temp_folder_path}\")\n\n    async def process_files(self):\n        \"\"\"异步处理文件队列\"\"\"\n        while True:\n            try:\n                file_path = await self.processor_queue.get()\n                await self.process_single_file(file_path)\n            except Exception as e:\n                print(f\"处理文件时出错: {str(e)}\")\n\n    async def process_single_file(self, file_path: str):\n        \"\"\"处理单个文件\"\"\"\n        try:\n            # 1. 文档处理\n            doc_data = self.document_processor.process_file(file_path)\n\n            # 2. AI分析\n            analysis = self.ai_analyzer.analyze_content(doc_data['content'])\n\n            # 3. 合并数据\n            final_data = {**doc_data, **analysis}\n\n            # 4. 存储到知识库\n            doc_id = self.knowledge_base.store_document(final_data)\n\n            print(f\"文件处理完成: {file_path} -> {doc_id}\")\n\n            # 5. 移动原文件到已处理文件夹\n            processed_folder = os.path.join(os.path.dirname(file_path), \"已处理\")\n            if not os.path.exists(processed_folder):\n                os.makedirs(processed_folder)\n\n            shutil.move(file_path, os.path.join(processed_folder, os.path.basename(file_path)))\n\n        except Exception as e:\n            print(f\"处理文件失败 {file_path}: {str(e)}\")\n```\n\n### 第四阶段：用户界面与系统集成 (1-2周)\n\n#### 4.1 Web管理界面 (Streamlit)\n\n```python\n# app/streamlit_app.py\nimport streamlit as st\nimport pandas as pd\nfrom datetime import datetime\nimport plotly.express as px\n\ndef main():\n    st.set_page_config(\n        page_title=\"Ewandata 知识管理系统\",\n        page_icon=\"🧠\",\n        layout=\"wide\"\n    )\n\n    st.title(\"🧠 Ewandata 本地知识管理系统\")\n\n    # 侧边栏导航\n    with st.sidebar:\n        st.header(\"导航菜单\")\n        page = st.selectbox(\"选择功能\", [\n            \"📊 系统概览\",\n            \"📚 知识库管理\",\n            \"🔍 智能搜索\",\n            \"📁 项目跟踪\",\n            \"⚙️ 系统设置\"\n        ])\n\n    if page == \"📊 系统概览\":\n        show_dashboard()\n    elif page == \"📚 知识库管理\":\n        show_knowledge_base()\n    elif page == \"🔍 智能搜索\":\n        show_search()\n    elif page == \"📁 项目跟踪\":\n        show_project_tracking()\n    elif page == \"⚙️ 系统设置\":\n        show_settings()\n\ndef show_dashboard():\n    \"\"\"显示系统概览仪表板\"\"\"\n    col1, col2, col3, col4 = st.columns(4)\n\n    with col1:\n        st.metric(\"文档总数\", \"1,234\", \"↗️ 12\")\n    with col2:\n        st.metric(\"项目数量\", \"45\", \"↗️ 3\")\n    with col3:\n        st.metric(\"GPU使用率\", \"65%\", \"↗️ 5%\")\n    with col4:\n        st.metric(\"存储使用\", \"2.3GB\", \"↗️ 0.1GB\")\n\n    # 最近活动\n    st.subheader(\"📈 最近活动\")\n    activity_data = pd.DataFrame({\n        '时间': ['2025-07-03 10:30', '2025-07-03 09:15', '2025-07-03 08:45'],\n        '活动': ['处理新文档', '项目扫描完成', 'GitHub同步'],\n        '状态': ['成功', '成功', '成功']\n    })\n    st.dataframe(activity_data, use_container_width=True)\n\ndef show_knowledge_base():\n    \"\"\"知识库管理界面\"\"\"\n    st.subheader(\"📚 知识库管理\")\n\n    # 文件上传\n    uploaded_file = st.file_uploader(\n        \"上传文档到知识库\",\n        type=['pdf', 'docx', 'txt', 'md', 'png', 'jpg', 'jpeg']\n    )\n\n    if uploaded_file:\n        if st.button(\"处理并添加到知识库\"):\n            # 这里调用文档处理逻辑\n            st.success(\"文档处理完成！\")\n\n    # 知识库统计\n    col1, col2 = st.columns(2)\n    with col1:\n        st.subheader(\"📊 内容分类\")\n        category_data = pd.DataFrame({\n            '分类': ['技术文档', '学习资料', '项目资料', '个人笔记'],\n            '数量': [234, 156, 89, 67]\n        })\n        fig = px.pie(category_data, values='数量', names='分类')\n        st.plotly_chart(fig, use_container_width=True)\n\n    with col2:\n        st.subheader(\"📈 月度增长\")\n        growth_data = pd.DataFrame({\n            '月份': ['1月', '2月', '3月', '4月', '5月', '6月'],\n            '新增文档': [45, 67, 89, 123, 156, 234]\n        })\n        fig = px.line(growth_data, x='月份', y='新增文档')\n        st.plotly_chart(fig, use_container_width=True)\n\ndef show_search():\n    \"\"\"智能搜索界面\"\"\"\n    st.subheader(\"🔍 智能搜索\")\n\n    search_query = st.text_input(\"输入搜索关键词或问题\")\n\n    if search_query:\n        # 这里调用搜索逻辑\n        st.write(\"搜索结果：\")\n\n        # 模拟搜索结果\n        results = [\n            {\"title\": \"Python机器学习基础\", \"similarity\": 0.95, \"type\": \"文档\"},\n            {\"title\": \"深度学习项目实践\", \"similarity\": 0.87, \"type\": \"项目\"},\n            {\"title\": \"AI模型优化技巧\", \"similarity\": 0.82, \"type\": \"笔记\"}\n        ]\n\n        for result in results:\n            with st.expander(f\"{result['title']} (相似度: {result['similarity']:.2f})\"):\n                st.write(f\"类型: {result['type']}\")\n                st.write(\"这是搜索结果的预览内容...\")\n\nif __name__ == \"__main__\":\n    main()\n```\n\n#### 4.2 FastAPI后端服务\n\n```python\n# api/main.py\nfrom fastapi import FastAPI, UploadFile, File, HTTPException\nfrom fastapi.middleware.cors import CORSMiddleware\nfrom pydantic import BaseModel\nfrom typing import List, Dict, Any\nimport uvicorn\n\napp = FastAPI(title=\"Ewandata API\", version=\"1.0.0\")\n\n# 允许跨域请求\napp.add_middleware(\n    CORSMiddleware,\n    allow_origins=[\"*\"],\n    allow_credentials=True,\n    allow_methods=[\"*\"],\n    allow_headers=[\"*\"],\n)\n\nclass SearchRequest(BaseModel):\n    query: str\n    limit: int = 10\n\nclass DocumentResponse(BaseModel):\n    id: str\n    title: str\n    content: str\n    tags: List[str]\n    created_at: str\n\*********(\"/\")\nasync def root():\n    return {\"message\": \"Ewandata API 服务运行中\"}\n\**********(\"/upload\")\nasync def upload_document(file: UploadFile = File(...)):\n    \"\"\"上传文档接口\"\"\"\n    try:\n        # 保存文件\n        file_path = f\"temp/{file.filename}\"\n        with open(file_path, \"wb\") as buffer:\n            content = await file.read()\n            buffer.write(content)\n\n        # 处理文档（这里调用文档处理逻辑）\n        # doc_id = process_document(file_path)\n\n        return {\"message\": \"文档上传成功\", \"file_id\": \"temp_id\"}\n    except Exception as e:\n        raise HTTPException(status_code=500, detail=str(e))\n\**********(\"/search\")\nasync def search_documents(request: SearchRequest):\n    \"\"\"搜索文档接口\"\"\"\n    try:\n        # 这里调用搜索逻辑\n        # results = knowledge_base.search_similar(request.query, request.limit)\n\n        # 模拟搜索结果\n        results = [\n            {\n                \"id\": \"doc1\",\n                \"title\": \"示例文档1\",\n                \"content\": \"这是搜索结果的内容...\",\n                \"tags\": [\"AI\", \"机器学习\"],\n                \"created_at\": \"2025-07-03T10:00:00\"\n            }\n        ]\n\n        return {\"results\": results, \"total\": len(results)}\n    except Exception as e:\n        raise HTTPException(status_code=500, detail=str(e))\n\*********(\"/projects\")\nasync def get_projects():\n    \"\"\"获取项目列表\"\"\"\n    # 这里调用项目跟踪逻辑\n    projects = [\n        {\n            \"id\": \"proj1\",\n            \"name\": \"AI助手项目\",\n            \"path\": \"E:\\\\Projects\\\\AI_Assistant\",\n            \"type\": \"python\",\n            \"last_modified\": \"2025-07-03T09:30:00\"\n        }\n    ]\n    return {\"projects\": projects}\n\*********(\"/system/status\")\nasync def get_system_status():\n    \"\"\"获取系统状态\"\"\"\n    return {\n        \"gpu_usage\": \"65%\",\n        \"memory_usage\": \"8.2GB/12GB\",\n        \"documents_count\": 1234,\n        \"projects_count\": 45,\n        \"last_sync\": \"2025-07-03T10:00:00\"\n    }\n\nif __name__ == \"__main__\":\n    uvicorn.run(app, host=\"0.0.0.0\", port=8000)\n```\n\n## 部署指南\n\n### 1. 环境准备\n\n#### 1.1 系统要求检查\n```bash\n# 检查Python版本\npython --version  # 需要3.9+\n\n# 检查CUDA版本\nnvidia-smi  # 确认RTX 4070可用\n\n# 检查Git版本\ngit --version  # 需要2.30+\n```\n\n#### 1.2 创建项目环境\n```bash\n# 克隆或创建项目目录\ncd E:\\Ewandata\nmkdir ewandata_system\ncd ewandata_system\n\n# 创建虚拟环境\npython -m venv venv\nvenv\\Scripts\\activate\n\n# 安装依赖\npip install -r requirements.txt\n```\n\n#### 1.3 配置文件设置\n```python\n# config/settings.py\nimport os\n\nclass Settings:\n    # 基础路径配置\n    BASE_DIR = \"E:\\\\Ewandata\"\n    TEMP_FOLDER = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\临时记\"\n\n    # AI模型配置\n    MODEL_NAME = \"microsoft/Phi-3-mini-4k-instruct\"\n    EMBEDDING_MODEL = \"all-MiniLM-L6-v2\"\n\n    # 数据库配置\n    SQLITE_DB = f\"{BASE_DIR}\\\\data\\\\metadata.db\"\n    VECTOR_DB = f\"{BASE_DIR}\\\\data\\\\vectors\"\n\n    # GitHub配置\n    GITHUB_REPO = \"https://github.com/EwanCosmos/Ewandata.git\"\n\n    # 服务配置\n    API_HOST = \"localhost\"\n    API_PORT = 8000\n    WEB_PORT = 8501\n```\n\n### 2. 启动系统\n\n#### 2.1 启动脚本\n```bash\n# start_ewandata.bat\n@echo off\necho 启动Ewandata知识管理系统...\n\ncd /d E:\\Ewandata\\ewandata_system\ncall venv\\Scripts\\activate\n\necho 启动后端API服务...\nstart \"Ewandata API\" python api/main.py\n\necho 等待API服务启动...\ntimeout /t 5\n\necho 启动Web界面...\nstart \"Ewandata Web\" streamlit run app/streamlit_app.py --server.port 8501\n\necho 启动文件监控服务...\npython services/file_monitor.py\n\necho Ewandata系统启动完成！\necho API服务: http://localhost:8000\necho Web界面: http://localhost:8501\npause\n```\n\n#### 2.2 服务管理\n```python\n# main.py - 主程序入口\nimport asyncio\nimport threading\nfrom services.temp_folder_monitor import TempFolderMonitor\nfrom services.github_sync import GitHubSync\nfrom services.project_tracker import ProjectTracker\n\nasync def main():\n    \"\"\"主程序入口\"\"\"\n    print(\"🚀 启动Ewandata知识管理系统...\")\n\n    # 初始化各个服务\n    temp_monitor = TempFolderMonitor(\n        temp_folder_path=\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\临时记\",\n        document_processor=document_processor,\n        ai_analyzer=ai_analyzer,\n        knowledge_base=knowledge_base\n    )\n\n    github_sync = GitHubSync(\n        repo_path=\"E:\\\\Ewandata\",\n        remote_url=\"https://github.com/EwanCosmos/Ewandata.git\"\n    )\n\n    project_tracker = ProjectTracker(monitor_path=\"E:\\\\\")\n\n    # 启动服务\n    temp_monitor.start_monitoring()\n    project_tracker.start_scanning()\n\n    print(\"✅ 所有服务启动完成！\")\n\n    # 保持程序运行\n    while True:\n        await asyncio.sleep(60)  # 每分钟检查一次\n\nif __name__ == \"__main__\":\n    asyncio.run(main())\n```\n\n## 预期效果与收益\n\n### 1. 知识管理效率提升\n- **自动化处理**: 节省90%的手动整理时间\n- **智能分类**: 自动标签和分类，提高检索效率5倍\n- **语义搜索**: 基于内容理解的精准搜索\n- **关联发现**: 自动发现知识点之间的隐藏联系\n\n### 2. 项目管理优化\n- **自动发现**: 实时监控E盘所有项目变化\n- **关联分析**: 识别项目间的技术栈重叠和协同机会\n- **知识复用**: 跨项目的代码和经验复用建议\n- **进度跟踪**: 项目活跃度和发展趋势分析\n\n### 3. 学习与成长加速\n- **个性化推荐**: 基于学习历史的内容推荐\n- **知识图谱**: 可视化的知识结构和学习路径\n- **盲点识别**: 发现知识体系中的薄弱环节\n- **持续优化**: 基于使用模式的系统自我优化\n\n## 风险评估与应对策略\n\n### 1. 技术风险\n- **GPU内存不足**: 采用模型量化、批处理优化、动态加载\n- **处理速度慢**: 异步处理、缓存机制、增量更新\n- **模型准确性**: 人工审核机制、用户反馈循环、模型微调\n\n### 2. 数据风险\n- **数据丢失**: 多重备份（本地+GitHub+云端）\n- **隐私泄露**: 完全本地化部署，无云端传输\n- **版本冲突**: 智能合并算法、冲突检测机制\n\n### 3. 运维风险\n- **系统崩溃**: 服务监控、自动重启、日志记录\n- **性能下降**: 定期清理、索引优化、资源监控\n\n## 后续扩展计划\n\n### 短期扩展 (3-6个月)\n- **语音输入**: 集成语音识别，支持语音笔记\n- **移动端**: 开发移动端应用，随时随地访问\n- **多语言**: 支持英文等多语言内容处理\n- **团队协作**: 支持多用户协作和权限管理\n\n### 长期规划 (6-12个月)\n- **更大模型**: GPU升级后支持更强大的模型\n- **分布式部署**: 支持多机器分布式部署\n- **API生态**: 开放API，支持第三方集成\n- **商业化**: 考虑产品化和商业应用\n\n---\n\n## 总结\n\nEwandata本地知识管理系统是一个完整的、基于AI的个人数字助手解决方案。通过充分利用您的RTX 4070 GPU和现有的知识库基础，该系统将显著提升您的知识管理效率和项目开发能力。\n\n**立即开始实施建议**：\n1. 按照第一阶段计划搭建基础环境\n2. 先实现核心的文档处理和AI分析功能\n3. 逐步添加高级功能和用户界面\n4. 根据实际使用情况调优和扩展\n\n如果您需要开始实施，我可以帮助您创建具体的代码文件和配置脚本。", "encoding": "utf-8", "line_count": 980, "char_count": 25923, "word_count": 2157, "headers": [{"level": 1, "text": "Ewandata 本地知识管理系统实施方案"}, {"level": 2, "text": "项目概述"}, {"level": 2, "text": "系统架构设计"}, {"level": 3, "text": "核心组件架构"}, {"level": 3, "text": "技术栈选择"}, {"level": 4, "text": "推荐配置 (RTX 4070 12GB优化)"}, {"level": 4, "text": "GPU内存分配策略"}, {"level": 2, "text": "详细实施计划"}, {"level": 3, "text": "第一阶段：环境搭建与基础配置 (1-2周)"}, {"level": 4, "text": "1.1 开发环境准备"}, {"level": 1, "text": "创建Python虚拟环境"}, {"level": 1, "text": "安装核心依赖"}, {"level": 4, "text": "1.2 AI模型下载与配置"}, {"level": 1, "text": "models/model_manager.py"}, {"level": 1, "text": "加载Phi-3-mini模型"}, {"level": 4, "text": "1.3 项目结构创建"}, {"level": 3, "text": "第二阶段：核心功能开发 (2-3周)"}, {"level": 4, "text": "2.1 文档处理引擎开发"}, {"level": 1, "text": "processors/document_processor.py"}, {"level": 4, "text": "2.2 AI分析引擎实现"}, {"level": 1, "text": "models/ai_analyzer.py"}, {"level": 1, "text": "使用Phi-3生成摘要"}, {"level": 4, "text": "2.3 知识库存储系统"}, {"level": 1, "text": "storage/knowledge_base.py"}, {"level": 1, "text": "存储到向量数据库"}, {"level": 1, "text": "存储到SQLite"}, {"level": 1, "text": "生成Markdown文件"}, {"level": 1, "text": "生成JSON文件"}, {"level": 3, "text": "第三阶段：高级功能集成 (2-3周)"}, {"level": 4, "text": "3.1 项目跟踪系统"}, {"level": 1, "text": "services/project_tracker.py"}, {"level": 4, "text": "3.2 GitHub集成与同步"}, {"level": 1, "text": "services/github_sync.py"}, {"level": 1, "text": "每天晚上11点自动同步"}, {"level": 1, "text": "每次有新内容时触发同步"}, {"level": 1, "text": "添加所有变更"}, {"level": 1, "text": "检查是否有变更"}, {"level": 1, "text": "提交变更"}, {"level": 1, "text": "推送到远程"}, {"level": 1, "text": "清理本地临时文件"}, {"level": 1, "text": "只同步处理后的Markdown和JSON文件"}, {"level": 1, "text": "复制到Git仓库"}, {"level": 4, "text": "3.3 临时记文件夹监控"}, {"level": 1, "text": "services/temp_folder_monitor.py"}, {"level": 1, "text": "添加到处理队列"}, {"level": 1, "text": "启动处理协程"}, {"level": 1, "text": "1. 文档处理"}, {"level": 1, "text": "2. <PERSON>分析"}, {"level": 1, "text": "3. 合并数据"}, {"level": 1, "text": "4. 存储到知识库"}, {"level": 1, "text": "5. 移动原文件到已处理文件夹"}, {"level": 3, "text": "第四阶段：用户界面与系统集成 (1-2周)"}, {"level": 4, "text": "4.1 Web管理界面 (Streamlit)"}, {"level": 1, "text": "app/streamlit_app.py"}, {"level": 1, "text": "侧边栏导航"}, {"level": 1, "text": "最近活动"}, {"level": 1, "text": "文件上传"}, {"level": 1, "text": "这里调用文档处理逻辑"}, {"level": 1, "text": "知识库统计"}, {"level": 1, "text": "这里调用搜索逻辑"}, {"level": 1, "text": "模拟搜索结果"}, {"level": 4, "text": "4.2 FastAPI后端服务"}, {"level": 1, "text": "api/main.py"}, {"level": 1, "text": "允许跨域请求"}, {"level": 1, "text": "保存文件"}, {"level": 1, "text": "处理文档（这里调用文档处理逻辑）"}, {"level": 1, "text": "doc_id = process_document(file_path)"}, {"level": 1, "text": "这里调用搜索逻辑"}, {"level": 1, "text": "results = knowledge_base.search_similar(request.query, request.limit)"}, {"level": 1, "text": "模拟搜索结果"}, {"level": 1, "text": "这里调用项目跟踪逻辑"}, {"level": 2, "text": "部署指南"}, {"level": 3, "text": "1. 环境准备"}, {"level": 4, "text": "1.1 系统要求检查"}, {"level": 1, "text": "检查Python版本"}, {"level": 1, "text": "检查CUDA版本"}, {"level": 1, "text": "检查Git版本"}, {"level": 4, "text": "1.2 创建项目环境"}, {"level": 1, "text": "克隆或创建项目目录"}, {"level": 1, "text": "创建虚拟环境"}, {"level": 1, "text": "安装依赖"}, {"level": 4, "text": "1.3 配置文件设置"}, {"level": 1, "text": "config/settings.py"}, {"level": 1, "text": "基础路径配置"}, {"level": 1, "text": "AI模型配置"}, {"level": 1, "text": "数据库配置"}, {"level": 1, "text": "GitHub配置"}, {"level": 1, "text": "服务配置"}, {"level": 3, "text": "2. 启动系统"}, {"level": 4, "text": "2.1 启动脚本"}, {"level": 1, "text": "start_ewandata.bat"}, {"level": 4, "text": "2.2 服务管理"}, {"level": 1, "text": "main.py - 主程序入口"}, {"level": 1, "text": "初始化各个服务"}, {"level": 1, "text": "启动服务"}, {"level": 1, "text": "保持程序运行"}, {"level": 2, "text": "预期效果与收益"}, {"level": 3, "text": "1. 知识管理效率提升"}, {"level": 3, "text": "2. 项目管理优化"}, {"level": 3, "text": "3. 学习与成长加速"}, {"level": 2, "text": "风险评估与应对策略"}, {"level": 3, "text": "1. 技术风险"}, {"level": 3, "text": "2. 数据风险"}, {"level": 3, "text": "3. 运维风险"}, {"level": 2, "text": "后续扩展计划"}, {"level": 3, "text": "短期扩展 (3-6个月)"}, {"level": 3, "text": "长期规划 (6-12个月)"}, {"level": 2, "text": "总结"}], "header_count": 108, "processing_time": "2025-07-04T00:29:57.614159", "processor_version": "1.0.0"}}