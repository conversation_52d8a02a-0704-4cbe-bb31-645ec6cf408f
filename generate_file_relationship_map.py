"""
生成文件关系图和处理流程可视化
"""

import json
from pathlib import Path
from datetime import datetime

def generate_file_relationship_map():
    """生成文件关系图"""
    
    # 源文件夹
    source_folder = Path(r"C:\Users\<USER>\Desktop\临时记")
    processed_folder = Path("data/processed")
    
    print("📊 Ewandata文件处理关系图")
    print("="*80)
    
    # 分析已处理的文件
    processed_files = {}
    if processed_folder.exists():
        for json_file in processed_folder.glob("*_processed.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                original_file = data.get('original_file', '')
                file_name = Path(original_file).name if original_file else json_file.stem.replace('_processed', '')
                
                processed_files[file_name] = {
                    'json_file': json_file.name,
                    'md_file': json_file.name.replace('.json', '.md'),
                    'original_path': original_file,
                    'ai_analysis': data.get('ai_analysis', {}),
                    'processing_time': data.get('processing_time_seconds', 0),
                    'file_type': data.get('file_info', {}).get('type', 'unknown')
                }
            except Exception as e:
                print(f"读取文件失败: {json_file} - {e}")
    
    print(f"\n📁 已处理文件总数: {len(processed_files)}")
    
    # 按文件类型分组
    type_groups = {}
    for file_name, info in processed_files.items():
        file_type = info['file_type']
        if file_type not in type_groups:
            type_groups[file_type] = []
        type_groups[file_type].append((file_name, info))
    
    print(f"\n📋 文件类型分布:")
    for file_type, files in type_groups.items():
        print(f"   {file_type}: {len(files)} 个")
    
    # 生成详细的文件处理映射
    print(f"\n🔄 文件处理映射关系:")
    print("-" * 80)
    
    for i, (file_name, info) in enumerate(processed_files.items(), 1):
        print(f"\n{i}. 📄 {file_name}")
        print(f"   📍 原始位置: {info['original_path']}")
        print(f"   📊 文件类型: {info['file_type']}")
        print(f"   ⏱️ 处理耗时: {info['processing_time']:.4f}秒")
        
        # AI分析结果
        ai_analysis = info['ai_analysis']
        keywords = ai_analysis.get('keywords', [])
        classification = ai_analysis.get('classification', {})
        
        print(f"   🔑 关键词: {', '.join(keywords[:5])}{'...' if len(keywords) > 5 else ''}")
        print(f"   📂 分类: {classification.get('category', 'unknown')}")
        print(f"   🏷️ 主题: {', '.join(classification.get('topics', []))}")
        print(f"   ⭐ 重要性: {classification.get('importance', 0)}/10")
        
        # 输出文件
        print(f"   📤 输出文件:")
        print(f"      📄 JSON: data/processed/{info['json_file']}")
        print(f"      📝 MD: data/processed/{info['md_file']}")
    
    # 生成AI模型使用统计
    print(f"\n🧠 AI模型使用统计:")
    print("-" * 80)
    
    # 根据文件类型推断使用的AI模型
    model_usage = {
        '.py': 'CodeLlama-7B (代码分析)',
        '.js': 'CodeLlama-7B (代码分析)', 
        '.html': 'CodeLlama-7B (代码分析)',
        '.css': 'CodeLlama-7B (代码分析)',
        '.txt': 'Qwen2-7B (文档分析)',
        '.md': 'Qwen2-7B (文档分析)',
        '.docx': 'Qwen2-7B (文档分析)',
        '.pdf': 'Qwen2-7B (文档分析)',
        '.png': 'OCR + Qwen2-7B (图片识别)',
        '.jpg': 'OCR + Qwen2-7B (图片识别)',
    }
    
    model_stats = {}
    for file_name, info in processed_files.items():
        file_type = info['file_type']
        model = model_usage.get(file_type, 'Qwen2-7B (默认)')
        
        if model not in model_stats:
            model_stats[model] = []
        model_stats[model].append(file_name)
    
    for model, files in model_stats.items():
        print(f"   🤖 {model}: {len(files)} 个文件")
        for file_name in files[:3]:  # 显示前3个
            print(f"      - {file_name}")
        if len(files) > 3:
            print(f"      - ... 还有 {len(files) - 3} 个文件")
    
    # 生成知识关联分析
    print(f"\n🔗 知识关联分析:")
    print("-" * 80)
    
    # 分析关键词重叠
    keyword_map = {}
    for file_name, info in processed_files.items():
        keywords = info['ai_analysis'].get('keywords', [])
        for keyword in keywords:
            if keyword not in keyword_map:
                keyword_map[keyword] = []
            keyword_map[keyword].append(file_name)
    
    # 找出共同关键词
    common_keywords = {k: v for k, v in keyword_map.items() if len(v) > 1}
    
    print(f"   📊 共同关键词数量: {len(common_keywords)}")
    
    for keyword, files in sorted(common_keywords.items(), key=lambda x: len(x[1]), reverse=True)[:10]:
        print(f"   🔑 '{keyword}': {len(files)} 个文件")
        for file_name in files[:3]:
            print(f"      - {file_name}")
        if len(files) > 3:
            print(f"      - ... 还有 {len(files) - 3} 个文件")
    
    # 生成主题聚类
    print(f"\n🏷️ 主题聚类分析:")
    print("-" * 80)
    
    topic_clusters = {}
    for file_name, info in processed_files.items():
        topics = info['ai_analysis'].get('classification', {}).get('topics', [])
        for topic in topics:
            if topic not in topic_clusters:
                topic_clusters[topic] = []
            topic_clusters[topic].append(file_name)
    
    for topic, files in sorted(topic_clusters.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"   📂 {topic}: {len(files)} 个文件")
        for file_name in files[:3]:
            print(f"      - {file_name}")
        if len(files) > 3:
            print(f"      - ... 还有 {len(files) - 3} 个文件")
    
    # 生成处理流程总结
    print(f"\n📈 处理流程总结:")
    print("-" * 80)
    
    total_processing_time = sum(info['processing_time'] for info in processed_files.values())
    avg_processing_time = total_processing_time / len(processed_files) if processed_files else 0
    
    print(f"   📊 处理统计:")
    print(f"      总文件数: {len(processed_files)}")
    print(f"      总处理时间: {total_processing_time:.4f}秒")
    print(f"      平均处理时间: {avg_processing_time:.4f}秒/文件")
    print(f"      处理效率: {len(processed_files)/max(total_processing_time, 0.001):.1f} 文件/秒")
    
    # 按重要性排序
    important_files = []
    for file_name, info in processed_files.items():
        importance = info['ai_analysis'].get('classification', {}).get('importance', 0)
        if importance >= 7:
            important_files.append((file_name, importance))
    
    important_files.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n⭐ 高重要性文件 (重要性 ≥ 7):")
    for file_name, importance in important_files:
        print(f"   📄 {file_name}: {importance}/10")
    
    # 生成工作流建议
    print(f"\n🔄 n8n工作流建议:")
    print("-" * 80)
    
    print(f"   基于当前处理结果，建议配置以下工作流:")
    print(f"   1. 高重要性文件自动上传到GitHub ({len(important_files)} 个文件)")
    print(f"   2. 技术文档自动分类到技术知识库")
    print(f"   3. 项目相关文件自动关联和索引")
    print(f"   4. 定期生成知识库目录和关系图")

def main():
    """主函数"""
    generate_file_relationship_map()
    
    print(f"\n" + "="*80)
    print(f"📋 查看详细结果的方法:")
    print(f"   1. JSON数据: data/processed/[文件名]_processed.json")
    print(f"   2. 可读报告: data/processed/[文件名]_processed.md") 
    print(f"   3. 知识库索引: data/knowledge_base/knowledge_index.json")
    print(f"   4. 工作流配置: n8n_workflows/")
    print(f"   5. 系统日志: logs/ewandata.log")

if __name__ == "__main__":
    main()
