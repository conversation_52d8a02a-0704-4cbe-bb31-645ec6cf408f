"""
Ewandata系统核心功能综合测试
真诚直接，不打折扣的端到端测试

测试目标：
1. 桌面临时记文件夹自动处理能力
2. E盘GitHub项目分析和协同创新能力
"""

import os
import sys
import time
import json
import shutil
from pathlib import Path
from datetime import datetime
import subprocess

# 添加系统路径
sys.path.append('ewandata_system')

class EwandataSystemTest:
    def __init__(self):
        self.base_path = Path("E:/Ewandata")
        self.temp_folder = Path("C:/Users/<USER>/Desktop/临时记")
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {},
            "summary": {}
        }
        
    def print_header(self, title):
        print(f"\n{'='*80}")
        print(f"  {title}")
        print('='*80)
    
    def print_step(self, step, description):
        print(f"\n[步骤 {step}] {description}")
        print("-" * 60)
    
    def record_result(self, test_name, success, details=None, error=None):
        """记录测试结果"""
        self.test_results["tests"][test_name] = {
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {},
            "error": error
        }
    
    def check_system_integrity(self):
        """检查系统完整性"""
        self.print_header("🔍 系统完整性检查")
        
        # 检查核心目录
        required_dirs = [
            "ewandata_system",
            "ewandata_system/services",
            "ewandata_system/processors", 
            "ewandata_system/storage",
            "data",
            "data/knowledge_base",
            "data/processed"
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            full_path = self.base_path / dir_path
            if not full_path.exists():
                missing_dirs.append(dir_path)
                print(f"❌ 缺失目录: {dir_path}")
            else:
                print(f"✅ 目录存在: {dir_path}")
        
        # 检查核心模块
        required_modules = [
            "ewandata_system/services/file_monitor.py",
            "ewandata_system/processors/document_processor.py",
            "ewandata_system/storage/knowledge_base.py",
            "ewandata_system/services/project_tracker.py",
            "ewandata_system/services/github_sync.py"
        ]
        
        missing_modules = []
        for module_path in required_modules:
            full_path = self.base_path / module_path
            if not full_path.exists():
                missing_modules.append(module_path)
                print(f"❌ 缺失模块: {module_path}")
            else:
                print(f"✅ 模块存在: {module_path}")
        
        # 检查临时记文件夹
        if not self.temp_folder.exists():
            print(f"⚠️ 临时记文件夹不存在，创建: {self.temp_folder}")
            self.temp_folder.mkdir(parents=True, exist_ok=True)
        else:
            print(f"✅ 临时记文件夹存在: {self.temp_folder}")
        
        # 检查数据库
        db_files = [
            "data/metadata.db",
            "data/projects.db"
        ]
        
        for db_file in db_files:
            db_path = self.base_path / db_file
            if db_path.exists():
                size = db_path.stat().st_size / 1024  # KB
                print(f"✅ 数据库: {db_file} ({size:.1f} KB)")
            else:
                print(f"❌ 数据库缺失: {db_file}")
        
        integrity_ok = len(missing_dirs) == 0 and len(missing_modules) == 0
        
        self.record_result("system_integrity", integrity_ok, {
            "missing_dirs": missing_dirs,
            "missing_modules": missing_modules
        })
        
        return integrity_ok
    
    def create_test_files(self):
        """创建真实的测试文件"""
        self.print_header("📝 创建测试文件")
        
        test_files = []
        
        # 1. 创建文本文件
        self.print_step(1, "创建文本测试文件")
        txt_content = f"""Ewandata系统测试文档

创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试目的: 验证系统自动处理文本文件的能力

## 测试内容
这是一个用于测试Ewandata系统文件处理能力的文档。

### 关键信息
- 项目名称: Ewandata知识管理系统
- 技术栈: Python, FastAPI, ChromaDB
- 目标: 自动化知识管理和项目协同

### 测试要求
1. 自动检测文件变化
2. 提取关键信息和摘要
3. 生成结构化数据
4. 上传到GitHub
5. 清理原文件

这个文件应该被系统自动处理并转换为知识库条目。
"""
        
        txt_file = self.temp_folder / f"test_document_{int(time.time())}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(txt_content)
        test_files.append(txt_file)
        print(f"✅ 创建文本文件: {txt_file.name}")
        
        # 2. 创建Markdown文件
        self.print_step(2, "创建Markdown测试文件")
        md_content = f"""# 项目协同创新测试

## 项目信息
- **创建时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **测试类型**: 项目协同分析
- **目标**: 验证多项目关联分析能力

## 技术栈分析
### 当前项目使用的技术
- Python 3.9+
- FastAPI (Web框架)
- ChromaDB (向量数据库)
- SQLite (关系数据库)
- Streamlit (前端界面)

### 潜在协同项目
1. **数据分析项目**: 可以复用数据处理模块
2. **Web应用项目**: 可以共享FastAPI框架经验
3. **AI项目**: 可以复用模型推理架构

## 创新机会
- 跨项目代码复用
- 技术栈标准化
- 知识共享机制

## 预期结果
系统应该能够：
1. 识别技术栈关键词
2. 分析项目关联性
3. 提出协同建议
4. 生成创新报告
"""
        
        md_file = self.temp_folder / f"project_analysis_{int(time.time())}.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
        test_files.append(md_file)
        print(f"✅ 创建Markdown文件: {md_file.name}")
        
        # 3. 创建JSON配置文件
        self.print_step(3, "创建JSON测试文件")
        json_content = {
            "project_name": "Ewandata_Test",
            "timestamp": datetime.now().isoformat(),
            "test_config": {
                "auto_processing": True,
                "github_sync": True,
                "cleanup_original": True
            },
            "expected_outputs": [
                "knowledge_base_entry.md",
                "structured_data.json",
                "github_commit"
            ],
            "test_data": {
                "keywords": ["知识管理", "自动化", "AI", "协同创新"],
                "categories": ["系统测试", "功能验证", "端到端测试"],
                "priority": "high",
                "processing_requirements": {
                    "extract_keywords": True,
                    "generate_summary": True,
                    "create_associations": True,
                    "upload_to_github": True
                }
            }
        }
        
        json_file = self.temp_folder / f"test_config_{int(time.time())}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_content, f, ensure_ascii=False, indent=2)
        test_files.append(json_file)
        print(f"✅ 创建JSON文件: {json_file.name}")
        
        self.record_result("create_test_files", True, {
            "files_created": [f.name for f in test_files],
            "total_files": len(test_files)
        })
        
        return test_files
    
    def test_file_monitoring(self, test_files):
        """测试文件监控功能"""
        self.print_header("👁️ 文件监控测试")
        
        try:
            # 导入文件监控服务
            from services.file_monitor import FileMonitor
            
            self.print_step(1, "初始化文件监控服务")
            monitor = FileMonitor(str(self.temp_folder))
            print("✅ 文件监控服务初始化成功")
            
            self.print_step(2, "检测测试文件")
            detected_files = []
            
            for test_file in test_files:
                if test_file.exists():
                    detected_files.append(test_file)
                    print(f"✅ 检测到文件: {test_file.name}")
                else:
                    print(f"❌ 文件不存在: {test_file.name}")
            
            self.record_result("file_monitoring", len(detected_files) > 0, {
                "detected_files": len(detected_files),
                "total_test_files": len(test_files)
            })
            
            return len(detected_files) > 0
            
        except ImportError as e:
            print(f"❌ 无法导入文件监控模块: {e}")
            self.record_result("file_monitoring", False, error=str(e))
            return False
        except Exception as e:
            print(f"❌ 文件监控测试失败: {e}")
            self.record_result("file_monitoring", False, error=str(e))
            return False

    def test_document_processing(self, test_files):
        """测试文档处理功能"""
        self.print_header("📄 文档处理测试")

        try:
            from processors.document_processor import DocumentProcessor

            self.print_step(1, "初始化文档处理器")
            processor = DocumentProcessor()
            print("✅ 文档处理器初始化成功")

            processed_docs = []

            for test_file in test_files:
                if not test_file.exists():
                    continue

                self.print_step(2, f"处理文件: {test_file.name}")

                try:
                    # 处理文档
                    doc_data = processor.process_file(str(test_file))

                    if "error" in doc_data:
                        print(f"❌ 处理失败: {doc_data['error']}")
                        continue

                    print(f"✅ 文档处理成功:")
                    print(f"   标题: {doc_data.get('title', 'N/A')}")
                    print(f"   字符数: {doc_data.get('char_count', 0)}")
                    print(f"   关键词: {', '.join(doc_data.get('keywords', [])[:5])}")
                    print(f"   摘要长度: {len(doc_data.get('summary', ''))}")

                    processed_docs.append({
                        "file": test_file.name,
                        "doc_data": doc_data
                    })

                except Exception as e:
                    print(f"❌ 处理文件失败 {test_file.name}: {e}")

            success = len(processed_docs) > 0
            self.record_result("document_processing", success, {
                "processed_count": len(processed_docs),
                "total_files": len(test_files)
            })

            return success, processed_docs

        except ImportError as e:
            print(f"❌ 无法导入文档处理模块: {e}")
            self.record_result("document_processing", False, error=str(e))
            return False, []
        except Exception as e:
            print(f"❌ 文档处理测试失败: {e}")
            self.record_result("document_processing", False, error=str(e))
            return False, []

    def test_knowledge_base_storage(self, processed_docs):
        """测试知识库存储功能"""
        self.print_header("🗄️ 知识库存储测试")

        try:
            from storage.knowledge_base import KnowledgeBase

            self.print_step(1, "初始化知识库")
            kb = KnowledgeBase(str(self.base_path))
            print("✅ 知识库初始化成功")

            stored_docs = []

            for doc_info in processed_docs:
                doc_data = doc_info["doc_data"]

                self.print_step(2, f"存储文档: {doc_info['file']}")

                try:
                    # 存储到知识库
                    doc_id = kb.store_document(doc_data)
                    print(f"✅ 文档存储成功，ID: {doc_id}")

                    # 验证存储
                    retrieved_doc = kb.get_document(doc_id)
                    if retrieved_doc:
                        print("✅ 文档检索验证成功")
                        stored_docs.append({
                            "file": doc_info['file'],
                            "doc_id": doc_id,
                            "stored": True
                        })
                    else:
                        print("❌ 文档检索验证失败")

                except Exception as e:
                    print(f"❌ 存储文档失败: {e}")

            kb.close()

            success = len(stored_docs) > 0
            self.record_result("knowledge_base_storage", success, {
                "stored_count": len(stored_docs),
                "processed_count": len(processed_docs)
            })

            return success, stored_docs

        except ImportError as e:
            print(f"❌ 无法导入知识库模块: {e}")
            self.record_result("knowledge_base_storage", False, error=str(e))
            return False, []
        except Exception as e:
            print(f"❌ 知识库存储测试失败: {e}")
            self.record_result("knowledge_base_storage", False, error=str(e))
            return False, []

    def test_github_projects_scan(self):
        """测试E盘GitHub项目扫描"""
        self.print_header("🔍 E盘GitHub项目扫描测试")

        self.print_step(1, "扫描E盘GitHub项目")

        # 扫描E盘寻找Git项目
        e_drive = Path("E:/")
        github_projects = []

        print("正在扫描E盘...")
        for root_dir in e_drive.iterdir():
            if not root_dir.is_dir() or root_dir.name.startswith('.'):
                continue

            # 检查是否是Git项目
            git_dir = root_dir / ".git"
            if git_dir.exists():
                try:
                    # 获取远程仓库信息
                    os.chdir(root_dir)
                    result = subprocess.run(['git', 'remote', 'get-url', 'origin'],
                                          capture_output=True, text=True, timeout=10)

                    if result.returncode == 0 and 'github.com' in result.stdout:
                        project_info = {
                            "name": root_dir.name,
                            "path": str(root_dir),
                            "remote_url": result.stdout.strip(),
                            "is_github": True
                        }

                        # 获取项目统计信息
                        try:
                            # Python文件数量
                            py_files = list(root_dir.rglob("*.py"))
                            project_info["python_files"] = len(py_files)

                            # 检查requirements.txt
                            req_file = root_dir / "requirements.txt"
                            project_info["has_requirements"] = req_file.exists()

                            # 检查README
                            readme_files = list(root_dir.glob("README*"))
                            project_info["has_readme"] = len(readme_files) > 0

                            # 检查技术栈
                            tech_stack = []
                            if req_file.exists():
                                with open(req_file, 'r', encoding='utf-8') as f:
                                    content = f.read().lower()
                                    if 'fastapi' in content:
                                        tech_stack.append('FastAPI')
                                    if 'streamlit' in content:
                                        tech_stack.append('Streamlit')
                                    if 'django' in content:
                                        tech_stack.append('Django')
                                    if 'flask' in content:
                                        tech_stack.append('Flask')
                                    if 'pytorch' in content or 'torch' in content:
                                        tech_stack.append('PyTorch')
                                    if 'tensorflow' in content:
                                        tech_stack.append('TensorFlow')

                            project_info["tech_stack"] = tech_stack

                        except Exception as e:
                            print(f"⚠️ 获取项目统计失败 {root_dir.name}: {e}")

                        github_projects.append(project_info)
                        print(f"✅ 发现GitHub项目: {root_dir.name}")

                except Exception as e:
                    print(f"⚠️ 检查项目失败 {root_dir.name}: {e}")

        print(f"\n📊 扫描结果: 发现 {len(github_projects)} 个GitHub项目")

        for project in github_projects[:5]:  # 显示前5个
            tech_info = f" ({', '.join(project.get('tech_stack', []))})" if project.get('tech_stack') else ""
            print(f"   - {project['name']}: {project.get('python_files', 0)} Python文件{tech_info}")

        success = len(github_projects) > 0
        self.record_result("github_projects_scan", success, {
            "projects_found": len(github_projects),
            "project_names": [p['name'] for p in github_projects]
        })

        return success, github_projects

    def analyze_project_collaboration(self, github_projects):
        """分析项目协同机会"""
        self.print_header("🤝 项目协同分析")

        if len(github_projects) < 2:
            print("⚠️ 项目数量不足，无法进行协同分析")
            self.record_result("project_collaboration", False,
                             error="项目数量不足")
            return False, {}

        self.print_step(1, "技术栈重叠分析")

        # 统计技术栈
        tech_usage = {}
        for project in github_projects:
            for tech in project.get('tech_stack', []):
                if tech not in tech_usage:
                    tech_usage[tech] = []
                tech_usage[tech].append(project['name'])

        # 找出共同技术栈
        common_tech = {tech: projects for tech, projects in tech_usage.items()
                      if len(projects) > 1}

        print("技术栈重叠分析:")
        for tech, projects in common_tech.items():
            print(f"   {tech}: {', '.join(projects)}")

        self.print_step(2, "协同机会识别")

        collaboration_opportunities = []

        # 基于技术栈的协同机会
        for tech, projects in common_tech.items():
            if len(projects) >= 2:
                opportunity = {
                    "type": "技术栈协同",
                    "technology": tech,
                    "projects": projects,
                    "opportunity": f"可以在{tech}技术栈上共享代码和最佳实践"
                }
                collaboration_opportunities.append(opportunity)
                print(f"✅ 发现协同机会: {tech} - {', '.join(projects)}")

        # 基于项目类型的协同机会
        web_projects = [p for p in github_projects
                       if any(tech in p.get('tech_stack', [])
                             for tech in ['FastAPI', 'Django', 'Flask', 'Streamlit'])]

        if len(web_projects) >= 2:
            opportunity = {
                "type": "Web开发协同",
                "projects": [p['name'] for p in web_projects],
                "opportunity": "可以共享Web开发组件和UI设计"
            }
            collaboration_opportunities.append(opportunity)
            print(f"✅ Web开发协同机会: {', '.join([p['name'] for p in web_projects])}")

        ai_projects = [p for p in github_projects
                      if any(tech in p.get('tech_stack', [])
                            for tech in ['PyTorch', 'TensorFlow'])]

        if len(ai_projects) >= 2:
            opportunity = {
                "type": "AI/ML协同",
                "projects": [p['name'] for p in ai_projects],
                "opportunity": "可以共享模型和数据处理管道"
            }
            collaboration_opportunities.append(opportunity)
            print(f"✅ AI/ML协同机会: {', '.join([p['name'] for p in ai_projects])}")

        success = len(collaboration_opportunities) > 0
        self.record_result("project_collaboration", success, {
            "opportunities_found": len(collaboration_opportunities),
            "common_technologies": list(common_tech.keys()),
            "opportunities": collaboration_opportunities
        })

        return success, collaboration_opportunities

    def test_github_sync(self, stored_docs):
        """测试GitHub同步功能"""
        self.print_header("📤 GitHub同步测试")

        if not stored_docs:
            print("⚠️ 没有存储的文档，跳过GitHub同步测试")
            self.record_result("github_sync", False, error="没有文档可同步")
            return False

        try:
            self.print_step(1, "检查GitHub同步状态")

            os.chdir(self.base_path)

            # 检查Git状态
            result = subprocess.run(['git', 'status', '--porcelain'],
                                  capture_output=True, text=True, check=True)

            if result.stdout.strip():
                print(f"📝 检测到 {len(result.stdout.strip().split())} 个文件变更")

                # 添加文件
                subprocess.run(['git', 'add', 'data/'], check=True)
                print("✅ 文件已添加到Git")

                # 创建提交
                commit_msg = f"系统测试: 添加 {len(stored_docs)} 个测试文档"
                commit_result = subprocess.run(['git', 'commit', '-m', commit_msg],
                                             capture_output=True, text=True)

                if commit_result.returncode == 0:
                    print(f"✅ 提交创建成功: {commit_msg}")

                    # 推送到GitHub (可选)
                    print("📤 准备推送到GitHub...")
                    push_result = subprocess.run(['git', 'push', 'origin', 'main'],
                                               capture_output=True, text=True, timeout=30)

                    if push_result.returncode == 0:
                        print("✅ GitHub同步成功!")
                        sync_success = True
                    else:
                        print(f"⚠️ GitHub推送失败: {push_result.stderr}")
                        sync_success = False
                else:
                    print(f"⚠️ 提交失败: {commit_result.stderr}")
                    sync_success = False
            else:
                print("ℹ️ 没有变更需要同步")
                sync_success = True

            self.record_result("github_sync", sync_success, {
                "documents_synced": len(stored_docs)
            })

            return sync_success

        except Exception as e:
            print(f"❌ GitHub同步测试失败: {e}")
            self.record_result("github_sync", False, error=str(e))
            return False

    def cleanup_test_files(self, test_files):
        """清理测试文件"""
        self.print_header("🧹 清理测试文件")

        cleaned_files = []

        for test_file in test_files:
            if test_file.exists():
                try:
                    test_file.unlink()
                    cleaned_files.append(test_file.name)
                    print(f"✅ 删除文件: {test_file.name}")
                except Exception as e:
                    print(f"❌ 删除失败 {test_file.name}: {e}")

        success = len(cleaned_files) > 0
        self.record_result("cleanup_test_files", success, {
            "cleaned_files": len(cleaned_files),
            "total_files": len(test_files)
        })

        print(f"🧹 清理完成: {len(cleaned_files)}/{len(test_files)} 个文件")
        return success

    def generate_test_report(self):
        """生成测试报告"""
        self.print_header("📋 测试报告")

        total_tests = len(self.test_results["tests"])
        passed_tests = sum(1 for test in self.test_results["tests"].values()
                          if test["success"])

        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        print(f"📊 测试汇总:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        print(f"\n📝 详细结果:")
        for test_name, result in self.test_results["tests"].items():
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {test_name}")
            if result["error"]:
                print(f"      错误: {result['error']}")

        # 保存报告
        report_file = self.base_path / "system_test_report.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 详细报告已保存: {report_file}")
        except Exception as e:
            print(f"❌ 报告保存失败: {e}")

        # 更新汇总
        self.test_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": success_rate,
            "overall_success": success_rate >= 80
        }

        return success_rate >= 80

    def run_comprehensive_test(self):
        """运行全面系统测试"""
        self.print_header("🚀 Ewandata系统全面功能测试")

        print("本测试将验证以下核心功能:")
        print("1. 系统完整性检查")
        print("2. 桌面临时记文件夹自动处理")
        print("3. 文档处理和知识库存储")
        print("4. E盘GitHub项目扫描和协同分析")
        print("5. GitHub同步功能")
        print("6. 自动清理功能")

        print(f"\n⚠️ 注意: 这是真实的端到端测试，不是模拟!")
        print("测试将在真实环境中创建、处理和清理文件。")

        # 1. 系统完整性检查
        print(f"\n⏳ 开始系统完整性检查...")
        integrity_ok = self.check_system_integrity()

        if not integrity_ok:
            print("❌ 系统完整性检查失败，无法继续测试")
            return self.generate_test_report()

        # 2. 创建测试文件
        print(f"\n⏳ 创建测试文件...")
        test_files = self.create_test_files()

        # 3. 文件监控测试
        print(f"\n⏳ 测试文件监控...")
        monitoring_ok = self.test_file_monitoring(test_files)

        # 4. 文档处理测试
        print(f"\n⏳ 测试文档处理...")
        processing_ok, processed_docs = self.test_document_processing(test_files)

        # 5. 知识库存储测试
        stored_docs = []
        if processing_ok:
            print(f"\n⏳ 测试知识库存储...")
            storage_ok, stored_docs = self.test_knowledge_base_storage(processed_docs)

        # 6. GitHub项目扫描测试
        print(f"\n⏳ 扫描E盘GitHub项目...")
        scan_ok, github_projects = self.test_github_projects_scan()

        # 7. 项目协同分析测试
        if scan_ok:
            print(f"\n⏳ 分析项目协同机会...")
            collab_ok, opportunities = self.analyze_project_collaboration(github_projects)

        # 8. GitHub同步测试
        if stored_docs:
            print(f"\n⏳ 测试GitHub同步...")
            sync_ok = self.test_github_sync(stored_docs)

        # 9. 清理测试文件
        print(f"\n⏳ 清理测试文件...")
        cleanup_ok = self.cleanup_test_files(test_files)

        # 10. 生成测试报告
        print(f"\n⏳ 生成测试报告...")
        overall_success = self.generate_test_report()

        # 显示最终结果
        if overall_success:
            print(f"\n🎉 系统测试完成！所有核心功能正常工作！")
            print("✅ 桌面临时记文件夹自动处理: 正常")
            print("✅ E盘GitHub项目分析: 正常")
            print("✅ 知识库管理: 正常")
            print("✅ GitHub同步: 正常")
        else:
            print(f"\n⚠️ 系统测试完成，但发现一些问题")
            print("请查看详细报告了解具体问题")

        return self.test_results


def main():
    """主函数"""
    print("🔧 Ewandata系统核心功能综合测试")
    print("真诚直接，不打折扣的端到端测试")
    print("=" * 80)

    # 创建测试实例
    test_suite = EwandataSystemTest()

    # 运行全面测试
    results = test_suite.run_comprehensive_test()

    print("\n" + "=" * 80)
    print("测试完成！")

    if isinstance(results, dict) and results.get("summary", {}).get("overall_success", False):
        print("🎉 恭喜！Ewandata系统核心功能全部正常！")
    else:
        if isinstance(results, dict):
            success_rate = results.get("summary", {}).get("success_rate", 0)
            print(f"⚠️ 测试完成，成功率: {success_rate:.1f}%")
        else:
            print("⚠️ 测试未完全完成")
        print("请根据报告修复发现的问题")


if __name__ == "__main__":
    main()
