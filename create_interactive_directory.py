"""
创建交互式目录导航
生成可展开/折叠的HTML目录结构
"""

import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime
import base64

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

async def create_interactive_html_directory():
    """创建交互式HTML目录"""
    print_header("🌐 创建交互式HTML目录")
    
    try:
        # 读取知识库索引
        index_path = Path("data/knowledge_base/knowledge_index.json")
        
        if not index_path.exists():
            print("❌ 知识库索引不存在")
            return False
        
        with open(index_path, 'r', encoding='utf-8') as f:
            index_data = json.load(f)
        
        documents = index_data.get('documents', {})
        categories = index_data.get('statistics', {}).get('documents_by_category', {})
        topics = index_data.get('statistics', {}).get('documents_by_topic', {})
        
        print(f"📊 生成目录: {len(documents)} 个文档")
        
        # 生成HTML内容
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ewandata知识库交互式目录</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }}
        
        .header h1 {{
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }}
        
        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.2em;
        }}
        
        .stats {{
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }}
        
        .stat-item {{
            background: #3498db;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            margin: 5px;
            text-align: center;
            min-width: 120px;
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            display: block;
        }}
        
        .search-container {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }}
        
        .search-box {{
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }}
        
        .search-box:focus {{
            outline: none;
            border-color: #3498db;
        }}
        
        .filters {{
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }}
        
        .filter-btn {{
            padding: 8px 16px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }}
        
        .filter-btn.active {{
            background: #3498db;
            color: white;
        }}
        
        .directory {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }}
        
        .category-section {{
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }}
        
        .category-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s;
        }}
        
        .category-header:hover {{
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }}
        
        .category-title {{
            font-size: 1.3em;
            font-weight: bold;
        }}
        
        .category-count {{
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }}
        
        .expand-icon {{
            transition: transform 0.3s;
            font-size: 1.2em;
        }}
        
        .category-content {{
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background: #f8f9fa;
        }}
        
        .category-content.expanded {{
            max-height: 2000px;
        }}
        
        .document-item {{
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            transition: background-color 0.3s;
        }}
        
        .document-item:hover {{
            background-color: #e3f2fd;
        }}
        
        .document-item:last-child {{
            border-bottom: none;
        }}
        
        .document-title {{
            font-size: 1.1em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        .document-icon {{
            font-size: 1.2em;
        }}
        
        .document-meta {{
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }}
        
        .meta-item {{
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
            color: #666;
        }}
        
        .importance-badge {{
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            font-weight: bold;
        }}
        
        .importance-high {{
            background: #e74c3c;
        }}
        
        .importance-medium {{
            background: #f39c12;
        }}
        
        .importance-low {{
            background: #95a5a6;
        }}
        
        .topics {{
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }}
        
        .topic-tag {{
            background: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }}
        
        .document-summary {{
            color: #555;
            font-size: 0.95em;
            line-height: 1.4;
            margin-bottom: 10px;
        }}
        
        .document-links {{
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }}
        
        .doc-link {{
            color: #3498db;
            text-decoration: none;
            padding: 5px 10px;
            border: 1px solid #3498db;
            border-radius: 5px;
            font-size: 0.9em;
            transition: all 0.3s;
        }}
        
        .doc-link:hover {{
            background: #3498db;
            color: white;
        }}
        
        .hidden {{
            display: none !important;
        }}
        
        @media (max-width: 768px) {{
            .container {{
                padding: 10px;
            }}
            
            .header h1 {{
                font-size: 2em;
            }}
            
            .stats {{
                flex-direction: column;
                align-items: center;
            }}
            
            .filters {{
                justify-content: center;
            }}
            
            .document-meta {{
                flex-direction: column;
                gap: 5px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Ewandata知识库</h1>
            <p class="subtitle">混合AI智能知识管理系统 - 交互式目录</p>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">{len(documents)}</span>
                    <span>总文档</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{len(categories)}</span>
                    <span>分类</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{len(topics)}</span>
                    <span>主题</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{len([d for d in documents.values() if d.get('importance', 0) >= 7])}</span>
                    <span>高重要性</span>
                </div>
            </div>
            <p style="color: #7f8c8d; margin-top: 15px;">
                📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </p>
        </div>
        
        <div class="search-container">
            <input type="text" class="search-box" id="searchBox" placeholder="🔍 搜索文档、关键词、主题...">
            <div class="filters">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="high-importance">高重要性</button>"""
        
        # 添加分类过滤器
        for category in sorted(categories.keys()):
            html_content += f"""
                <button class="filter-btn" data-filter="category-{category}">{category}</button>"""
        
        # 添加主题过滤器
        for topic in sorted(topics.keys()):
            html_content += f"""
                <button class="filter-btn" data-filter="topic-{topic}">{topic}</button>"""
        
        html_content += """
            </div>
        </div>
        
        <div class="directory" id="directory">"""
        
        # 按分类组织文档
        documents_by_category = {}
        for doc_id, doc_info in documents.items():
            category = doc_info.get('category', '其他')
            if category not in documents_by_category:
                documents_by_category[category] = []
            documents_by_category[category].append((doc_id, doc_info))
        
        # 生成每个分类的HTML
        for category, docs in sorted(documents_by_category.items(), key=lambda x: len(x[1]), reverse=True):
            # 按重要性排序文档
            docs.sort(key=lambda x: x[1].get('importance', 0), reverse=True)
            
            html_content += f"""
            <div class="category-section" data-category="{category}">
                <div class="category-header" onclick="toggleCategory('{category}')">
                    <div class="category-title">📁 {category}</div>
                    <div class="category-count">{len(docs)} 个文档</div>
                    <div class="expand-icon" id="icon-{category}">▼</div>
                </div>
                <div class="category-content" id="content-{category}">"""
            
            for doc_id, doc_info in docs:
                name = doc_info.get('name', doc_id)
                importance = doc_info.get('importance', 0)
                doc_topics = doc_info.get('topics', [])
                summary = doc_info.get('summary', '')[:200]
                
                # 重要性样式
                if importance >= 8:
                    importance_class = 'importance-high'
                elif importance >= 5:
                    importance_class = 'importance-medium'
                else:
                    importance_class = 'importance-low'
                
                # 文档图标
                file_ext = name.split('.')[-1].lower() if '.' in name else 'txt'
                if file_ext in ['py', 'js', 'html', 'css']:
                    doc_icon = '💻'
                elif file_ext in ['docx', 'doc']:
                    doc_icon = '📄'
                elif file_ext in ['md']:
                    doc_icon = '📝'
                elif file_ext in ['txt']:
                    doc_icon = '📃'
                elif file_ext in ['json']:
                    doc_icon = '📊'
                else:
                    doc_icon = '📋'
                
                html_content += f"""
                    <div class="document-item" 
                         data-category="{category}" 
                         data-importance="{importance}"
                         data-topics="{','.join(doc_topics)}"
                         data-keywords="{name.lower()} {' '.join(doc_topics).lower()} {summary.lower()}">
                        <div class="document-title">
                            <span class="document-icon">{doc_icon}</span>
                            {name}
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge {importance_class}">{importance}/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>{category}</span>
                            </div>
                        </div>"""
                
                if doc_topics:
                    html_content += f"""
                        <div class="topics">"""
                    for topic in doc_topics:
                        html_content += f"""
                            <span class="topic-tag">{topic}</span>"""
                    html_content += """
                        </div>"""
                
                if summary:
                    html_content += f"""
                        <div class="document-summary">{summary}...</div>"""
                
                html_content += f"""
                        <div class="document-links">
                            <a href="processed_documents/{doc_id}.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/{doc_id}.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>"""
            
            html_content += """
                </div>
            </div>"""
        
        html_content += """
        </div>
    </div>
    
    <script>
        // 切换分类展开/折叠
        function toggleCategory(category) {
            const content = document.getElementById(`content-${category}`);
            const icon = document.getElementById(`icon-${category}`);
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.textContent = '▼';
            } else {
                content.classList.add('expanded');
                icon.textContent = '▲';
            }
        }
        
        // 搜索功能
        const searchBox = document.getElementById('searchBox');
        const documentItems = document.querySelectorAll('.document-item');
        
        searchBox.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            documentItems.forEach(item => {
                const keywords = item.getAttribute('data-keywords');
                if (keywords.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
            
            // 自动展开有匹配结果的分类
            document.querySelectorAll('.category-section').forEach(section => {
                const visibleItems = section.querySelectorAll('.document-item[style="display: block"], .document-item:not([style*="display: none"])');
                const content = section.querySelector('.category-content');
                const icon = section.querySelector('.expand-icon');
                
                if (visibleItems.length > 0 && searchTerm) {
                    content.classList.add('expanded');
                    icon.textContent = '▲';
                }
            });
        });
        
        // 过滤功能
        const filterBtns = document.querySelectorAll('.filter-btn');
        
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // 更新按钮状态
                filterBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                
                documentItems.forEach(item => {
                    let show = false;
                    
                    if (filter === 'all') {
                        show = true;
                    } else if (filter === 'high-importance') {
                        const importance = parseInt(item.getAttribute('data-importance'));
                        show = importance >= 7;
                    } else if (filter.startsWith('category-')) {
                        const category = filter.replace('category-', '');
                        show = item.getAttribute('data-category') === category;
                    } else if (filter.startsWith('topic-')) {
                        const topic = filter.replace('topic-', '');
                        const topics = item.getAttribute('data-topics');
                        show = topics.includes(topic);
                    }
                    
                    item.style.display = show ? 'block' : 'none';
                });
                
                // 自动展开有匹配结果的分类
                document.querySelectorAll('.category-section').forEach(section => {
                    const visibleItems = section.querySelectorAll('.document-item[style="display: block"], .document-item:not([style*="display: none"])');
                    const content = section.querySelector('.category-content');
                    const icon = section.querySelector('.expand-icon');
                    
                    if (visibleItems.length > 0) {
                        content.classList.add('expanded');
                        icon.textContent = '▲';
                    } else {
                        content.classList.remove('expanded');
                        icon.textContent = '▼';
                    }
                });
            });
        });
        
        // 默认展开第一个分类
        document.addEventListener('DOMContentLoaded', function() {
            const firstCategory = document.querySelector('.category-section');
            if (firstCategory) {
                const categoryName = firstCategory.getAttribute('data-category');
                toggleCategory(categoryName);
            }
        });
    </script>
</body>
</html>"""
        
        # 保存HTML文件
        html_path = Path("data/knowledge_base/interactive_directory.html")
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 交互式HTML目录已生成: {html_path}")
        
        # 生成GitHub可用的Markdown版本
        await create_github_markdown_directory(documents_by_category)
        
        return True
        
    except Exception as e:
        print(f"❌ 创建交互式HTML目录失败: {e}")
        return False

async def create_github_markdown_directory(documents_by_category):
    """创建GitHub可用的Markdown目录"""
    print_header("📝 创建GitHub Markdown目录")
    
    try:
        markdown_content = f"""# 📚 Ewandata知识库交互式目录

> 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 概览统计

| 统计项 | 数量 |
|--------|------|
| 📄 总文档数 | {sum(len(docs) for docs in documents_by_category.values())} |
| 📁 分类数 | {len(documents_by_category)} |
| ⭐ 高重要性文档 | {sum(1 for docs in documents_by_category.values() for _, doc in docs if doc.get('importance', 0) >= 7)} |

## 🔍 快速导航

"""
        
        # 生成目录索引
        for category, docs in sorted(documents_by_category.items(), key=lambda x: len(x[1]), reverse=True):
            markdown_content += f"- [📁 {category} ({len(docs)}个)](#-{category.replace(' ', '-').lower()})\n"
        
        markdown_content += "\n---\n\n"
        
        # 生成详细目录
        for category, docs in sorted(documents_by_category.items(), key=lambda x: len(x[1]), reverse=True):
            docs.sort(key=lambda x: x[1].get('importance', 0), reverse=True)
            
            markdown_content += f"""## 📁 {category}

<details>
<summary>📊 {len(docs)} 个文档 (点击展开)</summary>

"""
            
            for doc_id, doc_info in docs:
                name = doc_info.get('name', doc_id)
                importance = doc_info.get('importance', 0)
                doc_topics = doc_info.get('topics', [])
                summary = doc_info.get('summary', '')[:150]
                
                # 重要性图标
                if importance >= 8:
                    importance_icon = '🔴'
                elif importance >= 5:
                    importance_icon = '🟡'
                else:
                    importance_icon = '⚪'
                
                markdown_content += f"""### {importance_icon} {name}

**重要性:** {importance}/10 | **主题:** {', '.join(doc_topics) if doc_topics else '无'}

{summary}{'...' if len(summary) >= 150 else ''}

📊 [JSON数据](processed_documents/{doc_id}.json) | 📝 [详细报告](processed_documents/{doc_id}.md)

---

"""
            
            markdown_content += "</details>\n\n"
        
        markdown_content += f"""
## 🔗 相关链接

- 🌐 [交互式HTML目录](interactive_directory.html) - 完整的可交互浏览体验
- 📊 [知识库索引](knowledge_index.json) - 结构化数据
- 📋 [完整目录](knowledge_catalog.md) - 详细的知识库目录

## 💡 使用说明

### 在线浏览
1. 点击上方的 [交互式HTML目录](interactive_directory.html) 获得最佳浏览体验
2. 使用搜索框快速查找文档
3. 使用过滤器按分类、主题或重要性筛选
4. 点击分类标题展开/折叠内容

### 本地使用
1. 下载 `interactive_directory.html` 文件
2. 在浏览器中打开即可使用所有交互功能

---
*由Ewandata混合AI系统自动生成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存Markdown文件
        md_path = Path("data/knowledge_base/interactive_directory.md")
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"✅ GitHub Markdown目录已生成: {md_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建GitHub Markdown目录失败: {e}")
        return False

async def main():
    """主函数"""
    print_header("🌐 交互式目录导航实现")
    
    print("创建交互式目录导航的功能:")
    print("1. 生成可展开/折叠的HTML目录")
    print("2. 实现搜索和过滤功能")
    print("3. 创建GitHub兼容的Markdown版本")
    print("4. 支持响应式设计和移动端")
    
    success = await create_interactive_html_directory()
    
    if success:
        print("\n🎉 交互式目录导航已成功创建！")
        print("✅ HTML交互式目录: data/knowledge_base/interactive_directory.html")
        print("✅ GitHub Markdown目录: data/knowledge_base/interactive_directory.md")
        print("✅ 支持搜索、过滤、展开/折叠功能")
        print("✅ 响应式设计，支持移动端")
        print(f"\n🌐 在浏览器中打开查看: file:///{Path('data/knowledge_base/interactive_directory.html').absolute()}")
    else:
        print("\n❌ 交互式目录导航创建失败")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
