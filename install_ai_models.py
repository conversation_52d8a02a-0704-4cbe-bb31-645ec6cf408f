"""
AI模型安装和配置脚本
自动安装和配置Microsoft Phi-3-mini模型
"""

import os
import sys
import subprocess
import torch
from pathlib import Path

def print_header(title):
    print(f"\n{'='*60}")
    print(f"  {title}")
    print('='*60)

def print_step(step, description):
    print(f"\n[步骤 {step}] {description}")
    print("-" * 40)

def check_gpu_info():
    """检查GPU信息"""
    print_step(1, "检查GPU配置")
    
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"✅ 检测到 {gpu_count} 个GPU")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        
        # 检查RTX 4070
        current_gpu = torch.cuda.get_device_name(0)
        if "4070" in current_gpu:
            print("✅ RTX 4070检测成功，适合运行Phi-3-mini模型")
            return True
        else:
            print(f"⚠️ 当前GPU: {current_gpu}")
            print("建议使用RTX 4070或更高配置")
            return True
    else:
        print("❌ 未检测到CUDA GPU，将使用CPU模式")
        print("⚠️ CPU模式运行速度较慢，建议使用GPU")
        return False

def install_dependencies():
    """安装依赖库"""
    print_step(2, "安装AI依赖库")
    
    dependencies = [
        "transformers>=4.36.0",
        "torch>=2.0.0",
        "accelerate>=0.24.0",
        "sentencepiece>=0.1.99",
        "protobuf>=3.20.0"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True, check=True)
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {dep} 安装失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    return True

def download_model():
    """下载Phi-3-mini模型"""
    print_step(3, "下载Phi-3-mini模型")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        model_name = "microsoft/Phi-3-mini-4k-instruct"
        print(f"正在下载模型: {model_name}")
        print("⚠️ 首次下载约需要10-20分钟，请耐心等待...")
        
        # 下载tokenizer
        print("下载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True
        )
        print("✅ Tokenizer下载完成")
        
        # 下载模型
        print("下载模型文件...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
        print("✅ 模型下载完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型下载失败: {e}")
        return False

def test_ai_service():
    """测试AI服务"""
    print_step(4, "测试AI服务")
    
    try:
        # 添加系统路径
        sys.path.append('ewandata_system')
        
        from services.ai_service import get_ai_service
        
        print("初始化AI服务...")
        ai_service = get_ai_service()
        
        # 初始化模型
        if ai_service.initialize_model():
            print("✅ AI模型初始化成功")
            
            # 测试基本功能
            test_text = "这是一个关于人工智能和机器学习的测试文档。"
            
            print("测试关键词提取...")
            keywords = ai_service.extract_keywords(test_text)
            print(f"关键词: {keywords}")
            
            print("测试摘要生成...")
            summary = ai_service.generate_summary(test_text)
            print(f"摘要: {summary}")
            
            print("✅ AI服务测试成功")
            return True
        else:
            print("❌ AI模型初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        return False

def create_ai_config():
    """创建AI配置文件"""
    print_step(5, "创建AI配置文件")
    
    config = {
        "ai_service": {
            "model_name": "microsoft/Phi-3-mini-4k-instruct",
            "device": "cuda" if torch.cuda.is_available() else "cpu",
            "max_length": 2048,
            "temperature": 0.7,
            "enable_ai": True
        },
        "document_processing": {
            "enable_ai_enhancement": True,
            "max_keywords": 10,
            "max_summary_length": 200,
            "enable_classification": True
        }
    }
    
    config_file = Path("ewandata_system/config/ai_config.json")
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    import json
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ AI配置文件已创建: {config_file}")
    return True

def update_system_integration():
    """更新系统集成"""
    print_step(6, "更新系统集成")
    
    # 检查是否需要更新文档处理器
    processor_file = Path("ewandata_system/processors/document_processor.py")
    enhanced_processor_file = Path("ewandata_system/processors/enhanced_document_processor.py")
    
    if enhanced_processor_file.exists():
        print("✅ 增强文档处理器已存在")
        
        # 创建符号链接或更新导入
        print("更新系统集成...")
        
        # 这里可以添加更多集成逻辑
        print("✅ 系统集成更新完成")
        return True
    else:
        print("❌ 增强文档处理器不存在")
        return False

def main():
    """主安装函数"""
    print_header("🧠 Ewandata AI模型安装和配置")
    
    print("本脚本将为Ewandata系统安装AI大模型支持")
    print("包括Microsoft Phi-3-mini模型和相关依赖")
    
    # 确认安装
    confirm = input("\n是否继续安装？(y/N): ").lower().strip()
    if confirm != 'y':
        print("安装已取消")
        return
    
    success_steps = 0
    total_steps = 6
    
    # 1. 检查GPU
    if check_gpu_info():
        success_steps += 1
    
    # 2. 安装依赖
    if install_dependencies():
        success_steps += 1
    else:
        print("❌ 依赖安装失败，无法继续")
        return
    
    # 3. 下载模型
    if download_model():
        success_steps += 1
    else:
        print("❌ 模型下载失败，请检查网络连接")
        return
    
    # 4. 测试AI服务
    if test_ai_service():
        success_steps += 1
    
    # 5. 创建配置
    if create_ai_config():
        success_steps += 1
    
    # 6. 更新集成
    if update_system_integration():
        success_steps += 1
    
    # 显示结果
    print_header("📋 安装结果")
    print(f"完成步骤: {success_steps}/{total_steps}")
    
    if success_steps == total_steps:
        print("🎉 AI模型安装完成！")
        print("\n✅ 系统现在具备以下AI能力:")
        print("   - 智能关键词提取")
        print("   - 自动摘要生成")
        print("   - 内容分类和标签")
        print("   - 项目协同分析")
        print("\n🚀 下一步:")
        print("   1. 重启Ewandata系统")
        print("   2. 运行测试验证AI功能")
        print("   3. 开始享受智能化文档处理")
    else:
        print("⚠️ 安装未完全成功")
        print("请检查错误信息并重新运行安装脚本")

if __name__ == "__main__":
    main()
