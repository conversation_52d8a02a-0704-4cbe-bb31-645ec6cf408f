# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
ewandata_env/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/

# Database backups
*.db.backup
*.db.bak

# Large model files
models/*.bin
models/*.safetensors

# Test files
test_*.py
*_test.py
comprehensive_test_suite.py
github_sync_test.py
demo_system.py

# Augment files
.augment/
