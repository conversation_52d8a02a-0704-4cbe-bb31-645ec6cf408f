@echo off
echo 🧪 Ewandata混合AI系统批量测试...
cd /d "%~dp0"

python -c "
import asyncio
import sys
sys.path.append('ewandata_system')

async def main():
    try:
        from services.hybrid_ai_manager import get_hybrid_ai_manager
        
        manager = get_hybrid_ai_manager()
        
        test_cases = [
            {'content': '请分析人工智能的发展趋势', 'type': 'analysis'},
            {'content': 'def quicksort(arr): pass  # 请完成这个快速排序函数', 'type': 'code'},
            {'content': '今天天气怎么样？', 'type': 'quick'},
            {'content': '请写一个关于未来科技的创意故事', 'type': 'creative'},
            {'content': '解释量子计算的工作原理和应用前景', 'type': 'complex'}
        ]
        
        print(f'开始批量测试 {len(test_cases)} 个案例...')
        
        results = []
        for i, test in enumerate(test_cases, 1):
            print(f'\\n[{i}/{len(test_cases)}] 测试: {test[\"content\"][:30]}...')
            
            request = {'content': test['content'], 'max_tokens': 300}
            result = await manager.process_request(request)
            
            if result['success']:
                print(f'✅ 成功 - {result[\"metadata\"][\"processed_by\"]} ({result[\"metadata\"][\"response_time\"]:.1f}s)')
                results.append(True)
            else:
                print(f'❌ 失败 - {result[\"error\"]}')
                results.append(False)
        
        success_rate = sum(results) / len(results) * 100
        print(f'\\n📊 测试完成: {sum(results)}/{len(results)} 成功 ({success_rate:.1f}%)')
        
        # 显示系统状态
        status = await manager.get_system_status()
        print(f'性能统计: {manager.performance_stats}')
        
        await manager.shutdown()
        
    except Exception as e:
        print(f'测试失败: {e}')

asyncio.run(main())
"

pause
