"""
Ewandata GitHub集成与自动同步服务
实现知识库内容的自动GitHub同步和本地清理
"""

import os
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import schedule
import time
import threading
import subprocess
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GitHubSync:
    """GitHub自动同步服务"""

    def __init__(self, repo_path: str, config: Optional[Dict] = None):
        self.repo_path = Path(repo_path)
        self.config = config or {}

        # 同步配置
        self.sync_interval = self.config.get('sync_interval', 3600)  # 1小时
        self.auto_sync = self.config.get('auto_sync', True)
        self.branch = self.config.get('branch', 'main')

        # 文件过滤配置
        self.sync_patterns = self.config.get('sync_patterns', [
            'data/knowledge_base/*.md',
            'data/processed/*.json',
            'README.md',
            'docs/*.md'
        ])

        self.ignore_patterns = self.config.get('ignore_patterns', [
            'temp/*',
            'logs/*',
            'ewandata_env/*',
            '__pycache__/*',
            '*.pyc',
            '.git/*'
        ])

        # 清理配置
        self.cleanup_after_sync = self.config.get('cleanup_after_sync', True)
        self.keep_days = self.config.get('keep_days', 7)

        # 状态跟踪
        self.last_sync_time = None
        self.sync_running = False
        self.scheduler_thread = None

        logger.info(f"GitHub同步服务初始化完成: {self.repo_path}")

    def check_git_status(self) -> Dict[str, Any]:
        """检查Git仓库状态"""
        try:
            os.chdir(self.repo_path)

            # 检查是否为Git仓库
            result = subprocess.run(['git', 'status'],
                                  capture_output=True, text=True, check=True)

            # 获取当前分支
            branch_result = subprocess.run(['git', 'branch', '--show-current'],
                                         capture_output=True, text=True, check=True)
            current_branch = branch_result.stdout.strip()

            # 检查是否有未提交的更改
            status_result = subprocess.run(['git', 'status', '--porcelain'],
                                         capture_output=True, text=True, check=True)
            has_changes = bool(status_result.stdout.strip())

            # 获取远程状态
            try:
                subprocess.run(['git', 'fetch'], capture_output=True, check=True)
                ahead_behind = subprocess.run(['git', 'rev-list', '--left-right', '--count', f'{current_branch}...origin/{current_branch}'],
                                            capture_output=True, text=True, check=True)
                ahead, behind = map(int, ahead_behind.stdout.strip().split('\t'))
            except:
                ahead, behind = 0, 0

            return {
                'is_git_repo': True,
                'current_branch': current_branch,
                'has_changes': has_changes,
                'ahead': ahead,
                'behind': behind,
                'status': 'clean' if not has_changes else 'dirty'
            }

        except subprocess.CalledProcessError as e:
            logger.error(f"Git状态检查失败: {e}")
            return {
                'is_git_repo': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"检查Git状态时出错: {e}")
            return {
                'is_git_repo': False,
                'error': str(e)
            }

    def init_git_repo(self) -> bool:
        """初始化Git仓库"""
        try:
            os.chdir(self.repo_path)

            # 初始化Git仓库
            subprocess.run(['git', 'init'], check=True)

            # 设置默认分支
            subprocess.run(['git', 'branch', '-M', self.branch], check=True)

            # 创建.gitignore文件
            gitignore_content = """
# Ewandata系统忽略文件
temp/
logs/
ewandata_env/
__pycache__/
*.pyc
*.pyo
*.log
.DS_Store
Thumbs.db

# 模型缓存
data/model_cache/
.cache/

# 临时文件
*.tmp
*.bak
*.swp
"""

            with open('.gitignore', 'w', encoding='utf-8') as f:
                f.write(gitignore_content.strip())

            # 添加初始文件
            subprocess.run(['git', 'add', '.gitignore'], check=True)
            subprocess.run(['git', 'add', 'README.md'], check=True)

            # 初始提交
            subprocess.run(['git', 'commit', '-m', 'Initial commit: Ewandata知识管理系统'], check=True)

            logger.info("Git仓库初始化成功")
            return True

        except Exception as e:
            logger.error(f"Git仓库初始化失败: {e}")
            return False

    def add_remote(self, remote_url: str) -> bool:
        """添加远程仓库"""
        try:
            os.chdir(self.repo_path)

            # 检查是否已有远程仓库
            result = subprocess.run(['git', 'remote'], capture_output=True, text=True)
            if 'origin' in result.stdout:
                # 更新远程URL
                subprocess.run(['git', 'remote', 'set-url', 'origin', remote_url], check=True)
            else:
                # 添加远程仓库
                subprocess.run(['git', 'remote', 'add', 'origin', remote_url], check=True)

            logger.info(f"远程仓库配置成功: {remote_url}")
            return True

        except Exception as e:
            logger.error(f"配置远程仓库失败: {e}")
            return False

    def sync_to_github(self, commit_message: Optional[str] = None) -> bool:
        """同步到GitHub"""
        if self.sync_running:
            logger.warning("同步正在进行中，跳过本次同步")
            return False

        self.sync_running = True

        try:
            os.chdir(self.repo_path)

            # 检查Git状态
            git_status = self.check_git_status()
            if not git_status['is_git_repo']:
                logger.error("不是有效的Git仓库")
                return False

            # 添加文件
            files_added = self._add_files_to_git()

            if not files_added:
                logger.info("没有文件需要同步")
                return True

            # 生成提交信息
            if not commit_message:
                commit_message = f"Auto sync: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            # 提交更改
            subprocess.run(['git', 'commit', '-m', commit_message], check=True)

            # 推送到远程
            try:
                subprocess.run(['git', 'push', 'origin', self.branch], check=True)
                logger.info(f"成功同步到GitHub: {commit_message}")

                # 记录同步时间
                self.last_sync_time = datetime.now()

                # 同步后清理
                if self.cleanup_after_sync:
                    self._cleanup_after_sync()

                return True

            except subprocess.CalledProcessError as e:
                if 'rejected' in str(e):
                    # 尝试拉取并合并
                    logger.info("检测到远程更新，尝试拉取合并...")
                    subprocess.run(['git', 'pull', 'origin', self.branch], check=True)
                    subprocess.run(['git', 'push', 'origin', self.branch], check=True)
                    logger.info("拉取合并后同步成功")
                    return True
                else:
                    raise

        except Exception as e:
            logger.error(f"同步到GitHub失败: {e}")
            return False

        finally:
            self.sync_running = False

    def _add_files_to_git(self) -> bool:
        """添加文件到Git"""
        try:
            files_added = False

            # 添加知识库文件
            knowledge_base_dir = self.repo_path / "data" / "knowledge_base"
            if knowledge_base_dir.exists():
                subprocess.run(['git', 'add', str(knowledge_base_dir)], check=True)
                files_added = True

            # 添加处理后的JSON文件
            processed_dir = self.repo_path / "data" / "processed"
            if processed_dir.exists():
                subprocess.run(['git', 'add', str(processed_dir)], check=True)
                files_added = True

            # 添加文档文件
            for doc_file in ['README.md', 'Ewandata实施方案.md', '使用指南.md']:
                doc_path = self.repo_path / doc_file
                if doc_path.exists():
                    subprocess.run(['git', 'add', str(doc_path)], check=True)
                    files_added = True

            return files_added

        except Exception as e:
            logger.error(f"添加文件到Git失败: {e}")
            return False

    def _cleanup_after_sync(self):
        """同步后清理临时文件"""
        try:
            cleanup_dirs = [
                self.repo_path / "temp",
                self.repo_path / "logs"
            ]

            cutoff_date = datetime.now() - timedelta(days=self.keep_days)

            for cleanup_dir in cleanup_dirs:
                if not cleanup_dir.exists():
                    continue

                for file_path in cleanup_dir.rglob('*'):
                    if file_path.is_file():
                        file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                        if file_mtime < cutoff_date:
                            try:
                                file_path.unlink()
                                logger.debug(f"清理过期文件: {file_path}")
                            except Exception as e:
                                logger.warning(f"清理文件失败 {file_path}: {e}")

            logger.info(f"清理完成，保留{self.keep_days}天内的文件")

        except Exception as e:
            logger.error(f"清理操作失败: {e}")

    def start_auto_sync(self):
        """启动自动同步"""
        if not self.auto_sync:
            logger.info("自动同步已禁用")
            return

        def sync_job():
            """同步任务"""
            logger.info("执行定时同步...")
            self.sync_to_github()

        # 设置定时任务
        schedule.every(self.sync_interval).seconds.do(sync_job)

        # 启动调度器线程
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次

        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()

        logger.info(f"自动同步已启动，间隔: {self.sync_interval}秒")

    def stop_auto_sync(self):
        """停止自动同步"""
        schedule.clear()
        if self.scheduler_thread:
            self.scheduler_thread = None
        logger.info("自动同步已停止")

    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        git_status = self.check_git_status()

        return {
            'git_status': git_status,
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'sync_running': self.sync_running,
            'auto_sync_enabled': self.auto_sync,
            'sync_interval': self.sync_interval,
            'next_sync_time': self._get_next_sync_time()
        }

    def _get_next_sync_time(self) -> Optional[str]:
        """获取下次同步时间"""
        if not self.auto_sync or not self.last_sync_time:
            return None

        next_sync = self.last_sync_time + timedelta(seconds=self.sync_interval)
        return next_sync.isoformat()

    def force_sync(self, commit_message: Optional[str] = None) -> bool:
        """强制同步"""
        logger.info("执行强制同步...")
        return self.sync_to_github(commit_message)

    def pull_from_github(self) -> bool:
        """从GitHub拉取更新"""
        try:
            os.chdir(self.repo_path)

            # 检查Git状态
            git_status = self.check_git_status()
            if not git_status['is_git_repo']:
                logger.error("不是有效的Git仓库")
                return False

            # 拉取更新
            subprocess.run(['git', 'pull', 'origin', self.branch], check=True)
            logger.info("成功从GitHub拉取更新")
            return True

        except Exception as e:
            logger.error(f"从GitHub拉取失败: {e}")
            return False

    def create_backup(self) -> str:
        """创建本地备份"""
        try:
            backup_dir = self.repo_path / "backups"
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"ewandata_backup_{timestamp}"
            backup_path = backup_dir / backup_name

            # 复制重要文件
            important_dirs = [
                "data/knowledge_base",
                "data/processed",
                "data/metadata.db"
            ]

            backup_path.mkdir()

            for dir_name in important_dirs:
                src_path = self.repo_path / dir_name
                if src_path.exists():
                    if src_path.is_dir():
                        shutil.copytree(src_path, backup_path / dir_name)
                    else:
                        shutil.copy2(src_path, backup_path / dir_name)

            # 创建备份信息文件
            backup_info = {
                'timestamp': timestamp,
                'backup_time': datetime.now().isoformat(),
                'git_status': self.check_git_status(),
                'files_backed_up': important_dirs
            }

            with open(backup_path / 'backup_info.json', 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)

            logger.info(f"备份创建成功: {backup_path}")
            return str(backup_path)

        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return ""


# 全局GitHub同步实例
github_sync = None

def get_github_sync(repo_path: str = "E:/Ewandata", config: Optional[Dict] = None) -> GitHubSync:
    """获取全局GitHub同步实例"""
    global github_sync
    if github_sync is None:
        github_sync = GitHubSync(repo_path, config)
    return github_sync


if __name__ == "__main__":
    # 测试代码
    sync_service = GitHubSync("E:/Ewandata")

    print("=== GitHub同步服务测试 ===")

    # 检查Git状态
    status = sync_service.check_git_status()
    print(f"Git状态: {status}")

    # 如果不是Git仓库，初始化
    if not status['is_git_repo']:
        print("初始化Git仓库...")
        if sync_service.init_git_repo():
            print("✅ Git仓库初始化成功")
        else:
            print("❌ Git仓库初始化失败")

    # 获取同步状态
    sync_status = sync_service.get_sync_status()
    print(f"同步状态: {sync_status}")

    # 创建备份
    backup_path = sync_service.create_backup()
    if backup_path:
        print(f"✅ 备份创建成功: {backup_path}")
    else:
        print("❌ 备份创建失败")