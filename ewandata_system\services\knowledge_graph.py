"""
知识图谱服务
实现文档间关联分析和知识体系构建
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Set, Tuple
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import re

logger = logging.getLogger(__name__)

class KnowledgeGraph:
    """知识图谱管理器"""
    
    def __init__(self):
        """初始化知识图谱"""
        self.documents = {}  # 文档存储
        self.keywords = defaultdict(set)  # 关键词到文档的映射
        self.categories = defaultdict(list)  # 分类到文档的映射
        self.relationships = []  # 文档间关系
        self.topics = defaultdict(list)  # 主题聚合
        
        logger.info("知识图谱初始化完成")
    
    async def add_document(self, doc_data: Dict[str, Any], ai_result: Dict[str, Any]):
        """
        添加文档到知识图谱
        
        Args:
            doc_data: 文档数据
            ai_result: AI分析结果
        """
        try:
            file_info = doc_data.get('file_info', {})
            ai_analysis = ai_result.get('result', {})
            
            doc_id = self._generate_doc_id(file_info)
            
            # 构建文档节点
            document = {
                'id': doc_id,
                'title': file_info.get('name', 'Unknown'),
                'path': file_info.get('full_path', ''),
                'type': doc_data.get('file_type', 'unknown'),
                'size': file_info.get('size_bytes', 0),
                'created_time': datetime.now().isoformat(),
                'keywords': ai_analysis.get('keywords', []),
                'summary': ai_analysis.get('summary', ''),
                'classification': ai_analysis.get('classification', {}),
                'content_preview': doc_data.get('content', '')[:500]
            }
            
            # 存储文档
            self.documents[doc_id] = document
            
            # 更新关键词索引
            for keyword in document['keywords']:
                self.keywords[keyword.lower()].add(doc_id)
            
            # 更新分类索引
            category = document['classification'].get('category', 'uncategorized')
            self.categories[category].append(doc_id)
            
            # 更新主题索引
            topics = document['classification'].get('topics', [])
            for topic in topics:
                self.topics[topic].append(doc_id)
            
            # 分析文档关系
            await self._analyze_relationships(doc_id, document)
            
            logger.info(f"文档已添加到知识图谱: {document['title']}")
            
        except Exception as e:
            logger.error(f"添加文档到知识图谱失败: {e}")
    
    def _generate_doc_id(self, file_info: Dict[str, Any]) -> str:
        """生成文档ID"""
        name = file_info.get('name', 'unknown')
        path = file_info.get('full_path', '')
        
        # 使用文件名和路径的哈希作为ID
        import hashlib
        content = f"{name}_{path}_{datetime.now().date()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    async def _analyze_relationships(self, doc_id: str, document: Dict[str, Any]):
        """分析文档关系"""
        try:
            current_keywords = set(kw.lower() for kw in document['keywords'])
            current_topics = set(document['classification'].get('topics', []))
            
            # 与现有文档比较
            for existing_id, existing_doc in self.documents.items():
                if existing_id == doc_id:
                    continue
                
                # 计算关键词重叠度
                existing_keywords = set(kw.lower() for kw in existing_doc['keywords'])
                keyword_overlap = len(current_keywords & existing_keywords)
                
                # 计算主题重叠度
                existing_topics = set(existing_doc['classification'].get('topics', []))
                topic_overlap = len(current_topics & existing_topics)
                
                # 计算相似度
                similarity = self._calculate_similarity(
                    keyword_overlap, len(current_keywords | existing_keywords),
                    topic_overlap, len(current_topics | existing_topics)
                )
                
                # 如果相似度足够高，建立关系
                if similarity > 0.3:
                    relationship = {
                        'source': doc_id,
                        'target': existing_id,
                        'type': self._determine_relationship_type(document, existing_doc),
                        'strength': similarity,
                        'common_keywords': list(current_keywords & existing_keywords),
                        'common_topics': list(current_topics & existing_topics)
                    }
                    
                    self.relationships.append(relationship)
                    
                    logger.info(f"发现文档关系: {document['title']} <-> {existing_doc['title']} (相似度: {similarity:.2f})")
        
        except Exception as e:
            logger.error(f"分析文档关系失败: {e}")
    
    def _calculate_similarity(self, keyword_overlap: int, total_keywords: int, 
                            topic_overlap: int, total_topics: int) -> float:
        """计算文档相似度"""
        if total_keywords == 0 and total_topics == 0:
            return 0.0
        
        keyword_sim = keyword_overlap / max(total_keywords, 1) if total_keywords > 0 else 0
        topic_sim = topic_overlap / max(total_topics, 1) if total_topics > 0 else 0
        
        # 加权平均
        return (keyword_sim * 0.7 + topic_sim * 0.3)
    
    def _determine_relationship_type(self, doc1: Dict[str, Any], doc2: Dict[str, Any]) -> str:
        """确定关系类型"""
        # 根据文档类型和内容确定关系
        type1 = doc1.get('type', '')
        type2 = doc2.get('type', '')
        
        category1 = doc1['classification'].get('category', '')
        category2 = doc2['classification'].get('category', '')
        
        if type1 == 'text' and type2 == 'text':
            if category1 == category2:
                return 'same_category'
            else:
                return 'related_content'
        elif 'code' in type1.lower() or 'code' in type2.lower():
            return 'code_related'
        elif 'image' in type1 or 'image' in type2:
            return 'visual_related'
        else:
            return 'general_related'
    
    async def generate_index(self) -> Dict[str, Any]:
        """生成知识库索引"""
        try:
            # 统计信息
            stats = {
                'total_documents': len(self.documents),
                'total_keywords': len(self.keywords),
                'total_categories': len(self.categories),
                'total_topics': len(self.topics),
                'total_relationships': len(self.relationships)
            }
            
            # 分类统计
            category_stats = {}
            for category, docs in self.categories.items():
                category_stats[category] = {
                    'count': len(docs),
                    'documents': [self.documents[doc_id]['title'] for doc_id in docs[:5]]
                }
            
            # 主题统计
            topic_stats = {}
            for topic, docs in self.topics.items():
                topic_stats[topic] = {
                    'count': len(docs),
                    'documents': [self.documents[doc_id]['title'] for doc_id in docs[:5]]
                }
            
            # 热门关键词
            popular_keywords = sorted(
                [(kw, len(docs)) for kw, docs in self.keywords.items()],
                key=lambda x: x[1],
                reverse=True
            )[:20]
            
            # 强关联文档对
            strong_relationships = [
                rel for rel in self.relationships 
                if rel['strength'] > 0.5
            ]
            
            index_data = {
                'generated_time': datetime.now().isoformat(),
                'statistics': stats,
                'categories': category_stats,
                'topics': topic_stats,
                'popular_keywords': popular_keywords,
                'strong_relationships': strong_relationships,
                'document_list': [
                    {
                        'id': doc['id'],
                        'title': doc['title'],
                        'type': doc['type'],
                        'category': doc['classification'].get('category', 'uncategorized'),
                        'keywords_count': len(doc['keywords'])
                    }
                    for doc in self.documents.values()
                ]
            }
            
            return index_data
            
        except Exception as e:
            logger.error(f"生成知识库索引失败: {e}")
            return {}
    
    async def search_related_documents(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索相关文档"""
        try:
            query_keywords = set(re.findall(r'\w+', query.lower()))
            
            # 计算文档相关性
            doc_scores = []
            
            for doc_id, document in self.documents.items():
                doc_keywords = set(kw.lower() for kw in document['keywords'])
                
                # 关键词匹配分数
                keyword_match = len(query_keywords & doc_keywords)
                
                # 内容匹配分数
                content_match = sum(1 for kw in query_keywords if kw in document['content_preview'].lower())
                
                # 总分数
                total_score = keyword_match * 2 + content_match
                
                if total_score > 0:
                    doc_scores.append((document, total_score))
            
            # 按分数排序
            doc_scores.sort(key=lambda x: x[1], reverse=True)
            
            return [doc for doc, score in doc_scores[:limit]]
            
        except Exception as e:
            logger.error(f"搜索相关文档失败: {e}")
            return []
    
    async def get_document_network(self, doc_id: str) -> Dict[str, Any]:
        """获取文档的关系网络"""
        try:
            if doc_id not in self.documents:
                return {}
            
            document = self.documents[doc_id]
            
            # 找到所有相关文档
            related_docs = []
            relationships = []
            
            for rel in self.relationships:
                if rel['source'] == doc_id:
                    related_docs.append(self.documents[rel['target']])
                    relationships.append(rel)
                elif rel['target'] == doc_id:
                    related_docs.append(self.documents[rel['source']])
                    relationships.append(rel)
            
            return {
                'center_document': document,
                'related_documents': related_docs,
                'relationships': relationships,
                'network_size': len(related_docs)
            }
            
        except Exception as e:
            logger.error(f"获取文档网络失败: {e}")
            return {}
    
    async def generate_topic_clusters(self) -> Dict[str, Any]:
        """生成主题聚类"""
        try:
            clusters = {}
            
            # 基于主题聚类
            for topic, doc_ids in self.topics.items():
                if len(doc_ids) >= 2:  # 至少2个文档才形成聚类
                    cluster_docs = [self.documents[doc_id] for doc_id in doc_ids]
                    
                    # 计算聚类统计
                    total_keywords = set()
                    categories = set()
                    
                    for doc in cluster_docs:
                        total_keywords.update(doc['keywords'])
                        categories.add(doc['classification'].get('category', 'uncategorized'))
                    
                    clusters[topic] = {
                        'document_count': len(cluster_docs),
                        'documents': [
                            {
                                'title': doc['title'],
                                'type': doc['type'],
                                'category': doc['classification'].get('category', 'uncategorized')
                            }
                            for doc in cluster_docs
                        ],
                        'common_keywords': list(total_keywords)[:10],
                        'categories': list(categories),
                        'cluster_strength': len(doc_ids) / len(self.documents) if self.documents else 0
                    }
            
            return {
                'total_clusters': len(clusters),
                'clusters': clusters,
                'generated_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成主题聚类失败: {e}")
            return {}
    
    async def export_knowledge_graph(self, output_path: Path):
        """导出知识图谱"""
        try:
            export_data = {
                'metadata': {
                    'export_time': datetime.now().isoformat(),
                    'total_documents': len(self.documents),
                    'total_relationships': len(self.relationships)
                },
                'documents': self.documents,
                'relationships': self.relationships,
                'categories': dict(self.categories),
                'topics': dict(self.topics),
                'keywords': {k: list(v) for k, v in self.keywords.items()}
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"知识图谱已导出: {output_path}")
            
        except Exception as e:
            logger.error(f"导出知识图谱失败: {e}")


async def main():
    """测试函数"""
    kg = KnowledgeGraph()
    
    # 模拟添加文档
    test_docs = [
        {
            'file_info': {'name': 'test1.txt', 'full_path': '/path/test1.txt'},
            'file_type': 'text',
            'content': 'This is a test document about AI and machine learning.'
        },
        {
            'file_info': {'name': 'test2.txt', 'full_path': '/path/test2.txt'},
            'file_type': 'text', 
            'content': 'Another document discussing artificial intelligence applications.'
        }
    ]
    
    test_ai_results = [
        {
            'result': {
                'keywords': ['AI', 'machine learning', 'test'],
                'summary': 'A test document about AI',
                'classification': {'category': 'technology', 'topics': ['AI', 'ML']}
            }
        },
        {
            'result': {
                'keywords': ['artificial intelligence', 'applications', 'AI'],
                'summary': 'Document about AI applications',
                'classification': {'category': 'technology', 'topics': ['AI', 'applications']}
            }
        }
    ]
    
    # 添加文档
    for doc_data, ai_result in zip(test_docs, test_ai_results):
        await kg.add_document(doc_data, ai_result)
    
    # 生成索引
    index = await kg.generate_index()
    print("知识库索引:", json.dumps(index, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    asyncio.run(main())
