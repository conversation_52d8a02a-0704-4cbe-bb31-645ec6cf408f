"""
多格式文档处理器
支持文本、Office文档、PDF、图片OCR、压缩文件等多种格式
"""

import os
import sys
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class MultiFormatProcessor:
    """多格式文档处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.supported_formats = {
            'text': ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv'],
            'office': ['.doc', '.docx', '.pdf', '.ppt', '.pptx', '.xls', '.xlsx'],
            'image': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff'],
            'archive': ['.zip', '.rar', '.7z', '.tar', '.gz']
        }
        
        # 检查可用的处理库
        self._check_dependencies()
        
        logger.info("多格式文档处理器初始化完成")
    
    def _check_dependencies(self):
        """检查依赖库"""
        # 检查OCR库
        try:
            import pytesseract
            from PIL import Image
            self.ocr_available = True
            logger.info("✅ OCR功能可用")
        except ImportError:
            self.ocr_available = False
            logger.warning("⚠️ OCR库未安装，图片处理功能受限")
        
        # 检查Office文档处理库
        try:
            import docx
            self.docx_available = True
        except ImportError:
            self.docx_available = False
        
        try:
            import PyPDF2
            self.pdf_available = True
        except ImportError:
            self.pdf_available = False
        
        try:
            import pandas as pd
            self.excel_available = True
        except ImportError:
            self.excel_available = False
        
        office_status = "✅" if (self.docx_available and self.pdf_available) else "⚠️"
        logger.info(f"{office_status} Office文档处理功能: Word({self.docx_available}), PDF({self.pdf_available}), Excel({self.excel_available})")
    
    async def process_file(self, file_path: Path) -> Dict[str, Any]:
        """
        处理文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            处理结果
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return {'success': False, 'error': '文件不存在'}
            
            # 获取文件信息
            file_info = self._get_file_info(file_path)
            
            # 根据文件类型选择处理方法
            file_type = self._get_file_type(file_path)
            
            logger.info(f"开始处理文件: {file_path.name} (类型: {file_type})")
            
            if file_type == 'text':
                content = await self._process_text_file(file_path)
            elif file_type == 'office':
                content = await self._process_office_file(file_path)
            elif file_type == 'image':
                content = await self._process_image_file(file_path)
            elif file_type == 'archive':
                content = await self._process_archive_file(file_path)
            else:
                return {'success': False, 'error': f'不支持的文件类型: {file_path.suffix}'}
            
            if content is None:
                return {'success': False, 'error': '文件内容提取失败'}
            
            return {
                'success': True,
                'content': content,
                'file_info': file_info,
                'file_type': file_type,
                'content_length': len(content)
            }
            
        except Exception as e:
            logger.error(f"文件处理失败 {file_path}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """获取文件基本信息"""
        try:
            stat = file_path.stat()
            
            return {
                'name': file_path.name,
                'stem': file_path.stem,
                'suffix': file_path.suffix,
                'size_bytes': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'modified_time': stat.st_mtime,
                'created_time': stat.st_ctime,
                'full_path': str(file_path.absolute()),
                'relative_path': str(file_path)
            }
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return {'name': file_path.name, 'error': str(e)}
    
    def _get_file_type(self, file_path: Path) -> str:
        """获取文件类型"""
        file_ext = file_path.suffix.lower()
        
        for format_type, extensions in self.supported_formats.items():
            if file_ext in extensions:
                return format_type
        
        return 'unknown'
    
    async def _process_text_file(self, file_path: Path) -> Optional[str]:
        """处理文本文件"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    
                    logger.info(f"成功读取文本文件 {file_path.name} (编码: {encoding})")
                    return content
                    
                except UnicodeDecodeError:
                    continue
            
            logger.error(f"无法解码文本文件: {file_path.name}")
            return f"[编码错误] 无法读取文件 {file_path.name}"
            
        except Exception as e:
            logger.error(f"文本文件处理失败: {e}")
            return f"[处理错误] {file_path.name}: {str(e)}"
    
    async def _process_office_file(self, file_path: Path) -> Optional[str]:
        """处理Office文档"""
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext == '.pdf':
                return await self._process_pdf(file_path)
            elif file_ext in ['.doc', '.docx']:
                return await self._process_word(file_path)
            elif file_ext in ['.xls', '.xlsx']:
                return await self._process_excel(file_path)
            elif file_ext in ['.ppt', '.pptx']:
                return await self._process_powerpoint(file_path)
            else:
                return f"[Office文档] {file_path.name} - 暂不支持此格式"
                
        except Exception as e:
            logger.error(f"Office文档处理失败: {e}")
            return f"[Office文档处理错误] {file_path.name}: {str(e)}"
    
    async def _process_pdf(self, file_path: Path) -> str:
        """处理PDF文件"""
        if not self.pdf_available:
            return f"[PDF文档] {file_path.name} - 需要安装PyPDF2库"
        
        try:
            import PyPDF2
            
            content_parts = [f"[PDF文档] {file_path.name}"]
            
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                total_pages = len(pdf_reader.pages)
                
                content_parts.append(f"总页数: {total_pages}")
                
                for page_num, page in enumerate(pdf_reader.pages[:10]):  # 限制前10页
                    try:
                        text = page.extract_text()
                        if text.strip():
                            content_parts.append(f"\n[第{page_num + 1}页]\n{text.strip()}")
                    except Exception as e:
                        content_parts.append(f"\n[第{page_num + 1}页] 提取失败: {e}")
                
                if total_pages > 10:
                    content_parts.append(f"\n... 还有 {total_pages - 10} 页未显示")
            
            return "\n".join(content_parts)
                
        except Exception as e:
            return f"[PDF处理错误] {file_path.name}: {str(e)}"
    
    async def _process_word(self, file_path: Path) -> str:
        """处理Word文档"""
        if not self.docx_available:
            return f"[Word文档] {file_path.name} - 需要安装python-docx库"
        
        try:
            import docx
            
            doc = docx.Document(file_path)
            content_parts = [f"[Word文档] {file_path.name}"]
            
            # 提取段落
            paragraphs = []
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs.append(para.text.strip())
            
            if paragraphs:
                content_parts.append("\n[正文内容]")
                content_parts.extend(paragraphs[:50])  # 限制段落数
                
                if len(paragraphs) > 50:
                    content_parts.append(f"... 还有 {len(paragraphs) - 50} 个段落未显示")
            
            # 提取表格
            if doc.tables:
                content_parts.append("\n[表格内容]")
                for i, table in enumerate(doc.tables[:5]):  # 限制表格数
                    content_parts.append(f"\n表格 {i+1}:")
                    for row in table.rows[:10]:  # 限制行数
                        row_data = [cell.text.strip() for cell in row.cells]
                        content_parts.append(" | ".join(row_data))
            
            return "\n".join(content_parts)
            
        except Exception as e:
            return f"[Word处理错误] {file_path.name}: {str(e)}"
    
    async def _process_excel(self, file_path: Path) -> str:
        """处理Excel文件"""
        if not self.excel_available:
            return f"[Excel文档] {file_path.name} - 需要安装pandas库"
        
        try:
            import pandas as pd
            
            content_parts = [f"[Excel文档] {file_path.name}"]
            
            # 读取所有工作表
            excel_file = pd.ExcelFile(file_path)
            content_parts.append(f"工作表数量: {len(excel_file.sheet_names)}")
            
            for sheet_name in excel_file.sheet_names[:5]:  # 限制工作表数
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=20)  # 限制行数
                    
                    content_parts.append(f"\n[工作表: {sheet_name}]")
                    content_parts.append(f"行数: {len(df)}, 列数: {len(df.columns)}")
                    content_parts.append("列名: " + ", ".join(df.columns.astype(str)))
                    
                    # 显示前几行数据
                    if not df.empty:
                        content_parts.append("\n前几行数据:")
                        content_parts.append(df.head().to_string(index=False, max_cols=10))
                    
                except Exception as e:
                    content_parts.append(f"\n[工作表: {sheet_name}] 读取失败: {e}")
            
            if len(excel_file.sheet_names) > 5:
                content_parts.append(f"\n... 还有 {len(excel_file.sheet_names) - 5} 个工作表未显示")
            
            return "\n".join(content_parts)
            
        except Exception as e:
            return f"[Excel处理错误] {file_path.name}: {str(e)}"
    
    async def _process_powerpoint(self, file_path: Path) -> str:
        """处理PowerPoint文件"""
        try:
            from pptx import Presentation
            
            prs = Presentation(file_path)
            content_parts = [f"[PowerPoint文档] {file_path.name}"]
            content_parts.append(f"幻灯片数量: {len(prs.slides)}")
            
            for slide_num, slide in enumerate(prs.slides[:10], 1):  # 限制幻灯片数
                slide_content = f"\n[幻灯片 {slide_num}]"
                
                # 提取文本
                texts = []
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        texts.append(shape.text.strip())
                
                if texts:
                    slide_content += "\n" + "\n".join(texts)
                else:
                    slide_content += "\n[无文本内容]"
                
                content_parts.append(slide_content)
            
            if len(prs.slides) > 10:
                content_parts.append(f"\n... 还有 {len(prs.slides) - 10} 张幻灯片未显示")
            
            return "\n".join(content_parts)
            
        except Exception as e:
            return f"[PowerPoint处理错误] {file_path.name}: {str(e)}"
    
    async def _process_image_file(self, file_path: Path) -> Optional[str]:
        """处理图片文件（OCR）"""
        if not self.ocr_available:
            return f"[图片文件] {file_path.name} - 需要安装pytesseract和PIL库进行OCR识别"
        
        try:
            import pytesseract
            from PIL import Image
            
            # 打开图片
            image = Image.open(file_path)
            
            # 获取图片信息
            width, height = image.size
            mode = image.mode
            
            content_parts = [
                f"[图片文件] {file_path.name}",
                f"尺寸: {width}x{height}",
                f"模式: {mode}"
            ]
            
            # OCR识别（支持中英文）
            try:
                text = pytesseract.image_to_string(image, lang='chi_sim+eng')
                
                if text.strip():
                    content_parts.append("\n[OCR识别结果]")
                    content_parts.append(text.strip())
                else:
                    content_parts.append("\n[OCR结果] 未识别到文本内容")
                    
            except Exception as ocr_error:
                content_parts.append(f"\n[OCR错误] {ocr_error}")
            
            return "\n".join(content_parts)
                
        except Exception as e:
            logger.error(f"图片处理失败: {e}")
            return f"[图片处理错误] {file_path.name}: {str(e)}"
    
    async def _process_archive_file(self, file_path: Path) -> Optional[str]:
        """处理压缩文件"""
        try:
            file_ext = file_path.suffix.lower()
            content_parts = [f"[压缩文件] {file_path.name}"]
            
            if file_ext == '.zip':
                import zipfile
                
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    file_list = zip_ref.namelist()
                    content_parts.append(f"包含 {len(file_list)} 个文件:")
                    
                    for file_name in file_list[:30]:  # 限制显示文件数
                        content_parts.append(f"  - {file_name}")
                    
                    if len(file_list) > 30:
                        content_parts.append(f"  ... 还有 {len(file_list) - 30} 个文件")
            
            else:
                content_parts.append(f"暂不支持 {file_ext} 格式的详细分析")
                content_parts.append("建议解压后处理其中的文件")
            
            return "\n".join(content_parts)
            
        except Exception as e:
            logger.error(f"压缩文件处理失败: {e}")
            return f"[压缩文件错误] {file_path.name}: {str(e)}"


async def main():
    """测试函数"""
    processor = MultiFormatProcessor()
    
    # 测试文件路径
    test_folder = Path(r"C:\Users\<USER>\Desktop\临时记")
    
    if test_folder.exists():
        print(f"扫描文件夹: {test_folder}")
        
        for file_path in test_folder.rglob("*"):
            if file_path.is_file():
                result = await processor.process_file(file_path)
                
                if result.get('success', False):
                    print(f"✅ {file_path.name}: {result.get('content_length', 0)} 字符")
                else:
                    print(f"❌ {file_path.name}: {result.get('error', 'Unknown error')}")
    else:
        print(f"测试文件夹不存在: {test_folder}")


if __name__ == "__main__":
    asyncio.run(main())
