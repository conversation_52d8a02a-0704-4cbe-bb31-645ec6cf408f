"""
创建文档关系可视化
生成交互式关系图谱和网络图
"""

import sys
import asyncio
import json
import math
from pathlib import Path
from datetime import datetime
from collections import defaultdict, Counter

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

async def create_mermaid_relationship_diagram():
    """创建Mermaid关系图"""
    print_header("🔗 创建Mermaid关系图")

    try:
        # 读取知识库索引
        index_path = Path("data/knowledge_base/knowledge_index.json")

        if not index_path.exists():
            print("❌ 知识库索引不存在")
            return False

        with open(index_path, 'r', encoding='utf-8') as f:
            index_data = json.load(f)

        documents = index_data.get('documents', {})

        print(f"📊 分析文档关系: {len(documents)} 个文档")

        # 分析文档关系
        relationships = []
        keyword_connections = defaultdict(list)
        topic_connections = defaultdict(list)
        category_connections = defaultdict(list)

        # 构建关系网络
        for doc_id, doc_info in documents.items():
            keywords = doc_info.get('keywords', [])
            topics = doc_info.get('topics', [])
            category = doc_info.get('category', '其他')
            importance = doc_info.get('importance', 0)

            # 关键词关系
            for keyword in keywords[:5]:  # 只取前5个关键词
                keyword_connections[keyword].append((doc_id, doc_info, importance))

            # 主题关系
            for topic in topics:
                topic_connections[topic].append((doc_id, doc_info, importance))

            # 分类关系
            category_connections[category].append((doc_id, doc_info, importance))

        # 生成Mermaid图表
        mermaid_content = """graph TB
    %% 文档关系网络图

    %% 定义样式
    classDef highImportance fill:#ff6b6b,stroke:#d63031,stroke-width:3px,color:#fff
    classDef mediumImportance fill:#fdcb6e,stroke:#e17055,stroke-width:2px,color:#2d3436
    classDef lowImportance fill:#74b9ff,stroke:#0984e3,stroke-width:1px,color:#2d3436
    classDef category fill:#6c5ce7,stroke:#5f3dc4,stroke-width:2px,color:#fff
    classDef topic fill:#00b894,stroke:#00a085,stroke-width:2px,color:#fff
    classDef keyword fill:#fd79a8,stroke:#e84393,stroke-width:2px,color:#fff

    %% 分类节点
"""

        # 添加分类节点
        for category, docs in category_connections.items():
            if len(docs) > 1:  # 只显示有多个文档的分类
                safe_category = category.replace(' ', '_').replace('/', '_')
                mermaid_content += f'    CAT_{safe_category}["📁 {category}<br/>{len(docs)}个文档"]\n'

        mermaid_content += "\n    %% 主题节点\n"

        # 添加主题节点
        for topic, docs in topic_connections.items():
            if len(docs) > 1:  # 只显示有多个文档的主题
                safe_topic = topic.replace(' ', '_').replace('/', '_')
                mermaid_content += f'    TOPIC_{safe_topic}["🏷️ {topic}<br/>{len(docs)}个文档"]\n'

        mermaid_content += "\n    %% 文档节点\n"

        # 添加高重要性文档节点
        high_importance_docs = [(doc_id, doc_info) for doc_id, doc_info in documents.items() if doc_info.get('importance', 0) >= 7]

        for doc_id, doc_info in high_importance_docs[:15]:  # 限制显示数量
            name = doc_info.get('name', doc_id)
            importance = doc_info.get('importance', 0)
            safe_name = name.replace('.', '_').replace(' ', '_').replace('-', '_')[:20]
            short_name = name[:20] + '...' if len(name) > 20 else name

            mermaid_content += f'    DOC_{safe_name}["📄 {short_name}<br/>重要性: {importance}/10"]\n'

        mermaid_content += "\n    %% 关系连接\n"

        # 添加文档到分类的连接
        for category, docs in category_connections.items():
            if len(docs) > 1:
                safe_category = category.replace(' ', '_').replace('/', '_')
                for doc_id, doc_info, importance in docs:
                    if importance >= 7:  # 只连接高重要性文档
                        name = doc_info.get('name', doc_id)
                        safe_name = name.replace('.', '_').replace(' ', '_').replace('-', '_')[:20]
                        mermaid_content += f'    DOC_{safe_name} --> CAT_{safe_category}\n'

        # 添加文档到主题的连接
        for topic, docs in topic_connections.items():
            if len(docs) > 1:
                safe_topic = topic.replace(' ', '_').replace('/', '_')
                for doc_id, doc_info, importance in docs:
                    if importance >= 7:  # 只连接高重要性文档
                        name = doc_info.get('name', doc_id)
                        safe_name = name.replace('.', '_').replace(' ', '_').replace('-', '_')[:20]
                        mermaid_content += f'    DOC_{safe_name} --> TOPIC_{safe_topic}\n'

        mermaid_content += "\n    %% 应用样式\n"

        # 应用样式
        for doc_id, doc_info in high_importance_docs[:15]:
            name = doc_info.get('name', doc_id)
            importance = doc_info.get('importance', 0)
            safe_name = name.replace('.', '_').replace(' ', '_').replace('-', '_')[:20]

            if importance >= 8:
                mermaid_content += f'    class DOC_{safe_name} highImportance\n'
            elif importance >= 5:
                mermaid_content += f'    class DOC_{safe_name} mediumImportance\n'
            else:
                mermaid_content += f'    class DOC_{safe_name} lowImportance\n'

        # 分类和主题样式
        for category in category_connections.keys():
            if len(category_connections[category]) > 1:
                safe_category = category.replace(' ', '_').replace('/', '_')
                mermaid_content += f'    class CAT_{safe_category} category\n'

        for topic in topic_connections.keys():
            if len(topic_connections[topic]) > 1:
                safe_topic = topic.replace(' ', '_').replace('/', '_')
                mermaid_content += f'    class TOPIC_{safe_topic} topic\n'

        # 保存Mermaid文件
        mermaid_path = Path("data/knowledge_base/document_relationships.mmd")
        with open(mermaid_path, 'w', encoding='utf-8') as f:
            f.write(mermaid_content)

        print(f"✅ Mermaid关系图已生成: {mermaid_path}")

        return True, mermaid_content

    except Exception as e:
        print(f"❌ 创建Mermaid关系图失败: {e}")
        return False, ""

async def create_interactive_network_visualization():
    """创建交互式网络可视化"""
    print_header("🌐 创建交互式网络可视化")

    try:
        # 读取知识库索引
        index_path = Path("data/knowledge_base/knowledge_index.json")

        with open(index_path, 'r', encoding='utf-8') as f:
            index_data = json.load(f)

        documents = index_data.get('documents', {})

        # 构建网络数据
        nodes = []
        links = []
        node_id_map = {}

        # 添加文档节点
        for i, (doc_id, doc_info) in enumerate(documents.items()):
            name = doc_info.get('name', doc_id)
            importance = doc_info.get('importance', 0)
            category = doc_info.get('category', '其他')
            topics = doc_info.get('topics', [])

            # 节点大小基于重要性
            size = max(10, importance * 3)

            # 节点颜色基于分类
            color_map = {
                '技术文档': '#3498db',
                '项目管理': '#e74c3c',
                '学习笔记': '#2ecc71',
                '商业文档': '#f39c12',
                '个人资料': '#9b59b6',
                '其他': '#95a5a6'
            }
            color = color_map.get(category, '#95a5a6')

            node = {
                'id': i,
                'label': name[:30] + '...' if len(name) > 30 else name,
                'size': size,
                'color': color,
                'importance': importance,
                'category': category,
                'topics': topics,
                'doc_id': doc_id,
                'full_name': name
            }

            nodes.append(node)
            node_id_map[doc_id] = i

        # 计算文档间的相似性并添加连接
        for i, (doc_id1, doc_info1) in enumerate(documents.items()):
            for j, (doc_id2, doc_info2) in enumerate(documents.items()):
                if i >= j:  # 避免重复连接
                    continue

                # 计算相似性
                similarity = calculate_similarity(doc_info1, doc_info2)

                if similarity > 0.3:  # 相似性阈值
                    link = {
                        'source': node_id_map[doc_id1],
                        'target': node_id_map[doc_id2],
                        'weight': similarity,
                        'width': max(1, similarity * 5)
                    }
                    links.append(link)

        print(f"📊 网络数据: {len(nodes)} 个节点, {len(links)} 个连接")

        # 生成HTML可视化
        html_content = create_network_html(nodes, links)

        # 保存HTML文件
        html_path = Path("data/knowledge_base/network_visualization.html")
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ 交互式网络可视化已生成: {html_path}")

        return True

    except Exception as e:
        print(f"❌ 创建交互式网络可视化失败: {e}")
        return False

def calculate_similarity(doc1, doc2):
    """计算两个文档的相似性"""
    try:
        similarity = 0.0

        # 分类相似性
        if doc1.get('category') == doc2.get('category'):
            similarity += 0.3

        # 主题相似性
        topics1 = set(doc1.get('topics', []))
        topics2 = set(doc2.get('topics', []))
        if topics1 and topics2:
            topic_similarity = len(topics1.intersection(topics2)) / len(topics1.union(topics2))
            similarity += topic_similarity * 0.4

        # 关键词相似性
        keywords1 = set(doc1.get('keywords', [])[:10])  # 取前10个关键词
        keywords2 = set(doc2.get('keywords', [])[:10])
        if keywords1 and keywords2:
            keyword_similarity = len(keywords1.intersection(keywords2)) / len(keywords1.union(keywords2))
            similarity += keyword_similarity * 0.3

        return min(similarity, 1.0)

    except Exception as e:
        return 0.0

def create_network_html(nodes, links):
    """创建网络可视化HTML"""
    nodes_json = json.dumps(nodes, ensure_ascii=False)
    links_json = json.dumps(links, ensure_ascii=False)

    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ewandata文档关系网络图</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}

        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }}

        .header {{
            text-align: center;
            margin-bottom: 20px;
        }}

        .header h1 {{
            color: #2c3e50;
            margin-bottom: 10px;
        }}

        .controls {{
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}

        .control-btn {{
            padding: 8px 16px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }}

        .control-btn:hover, .control-btn.active {{
            background: #3498db;
            color: white;
        }}

        .network-container {{
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }}

        .tooltip {{
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            max-width: 300px;
        }}

        .legend {{
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ddd;
        }}

        .legend-item {{
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }}

        .legend-color {{
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 8px;
        }}

        .info-panel {{
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ddd;
            max-width: 250px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Ewandata文档关系网络图</h1>
            <p>交互式文档关系可视化 - 点击节点查看详情，拖拽移动</p>
        </div>

        <div class="controls">
            <button class="control-btn active" onclick="filterByCategory('all')">全部</button>
            <button class="control-btn" onclick="filterByCategory('技术文档')">技术文档</button>
            <button class="control-btn" onclick="filterByCategory('项目管理')">项目管理</button>
            <button class="control-btn" onclick="filterByCategory('个人资料')">个人资料</button>
            <button class="control-btn" onclick="filterByImportance(7)">高重要性</button>
            <button class="control-btn" onclick="resetZoom()">重置视图</button>
        </div>

        <div class="network-container">
            <svg id="network" width="100%" height="600"></svg>
        </div>

        <div class="tooltip" id="tooltip"></div>

        <div class="legend">
            <h4>图例</h4>
            <div class="legend-item">
                <div class="legend-color" style="background: #3498db;"></div>
                <span>技术文档</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #e74c3c;"></div>
                <span>项目管理</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #2ecc71;"></div>
                <span>学习笔记</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #f39c12;"></div>
                <span>商业文档</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #9b59b6;"></div>
                <span>个人资料</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #95a5a6;"></div>
                <span>其他</span>
            </div>
        </div>

        <div class="info-panel" id="infoPanel">
            <h4>网络统计</h4>
            <p>节点数: <span id="nodeCount">{len(nodes)}</span></p>
            <p>连接数: <span id="linkCount">{len(links)}</span></p>
            <p>点击节点查看详细信息</p>
        </div>
    </div>

    <script>
        // 数据
        const nodes = {nodes_json};
        const links = {links_json};

        // SVG设置
        const svg = d3.select("#network");
        const width = 1200;
        const height = 600;
        svg.attr("width", width).attr("height", height);

        // 创建缩放行为
        const zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on("zoom", (event) => {{
                g.attr("transform", event.transform);
            }});

        svg.call(zoom);

        // 创建主容器
        const g = svg.append("g");

        // 创建力导向图
        const simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links).id(d => d.id).distance(100))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(d => d.size + 5));

        // 创建连接线
        const link = g.append("g")
            .selectAll("line")
            .data(links)
            .enter().append("line")
            .attr("stroke", "#999")
            .attr("stroke-opacity", 0.6)
            .attr("stroke-width", d => d.width);

        // 创建节点
        const node = g.append("g")
            .selectAll("circle")
            .data(nodes)
            .enter().append("circle")
            .attr("r", d => d.size)
            .attr("fill", d => d.color)
            .attr("stroke", "#fff")
            .attr("stroke-width", 2)
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended))
            .on("click", showNodeInfo)
            .on("mouseover", showTooltip)
            .on("mouseout", hideTooltip);

        // 创建标签
        const label = g.append("g")
            .selectAll("text")
            .data(nodes)
            .enter().append("text")
            .text(d => d.label)
            .attr("font-size", "10px")
            .attr("text-anchor", "middle")
            .attr("dy", ".35em")
            .style("pointer-events", "none");

        // 更新位置
        simulation.on("tick", () => {{
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);

            label
                .attr("x", d => d.x)
                .attr("y", d => d.y);
        }});

        // 拖拽函数
        function dragstarted(event, d) {{
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }}

        function dragged(event, d) {{
            d.fx = event.x;
            d.fy = event.y;
        }}

        function dragended(event, d) {{
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }}

        // 显示节点信息
        function showNodeInfo(event, d) {{
            const infoPanel = document.getElementById('infoPanel');
            infoPanel.innerHTML = `
                <h4>文档详情</h4>
                <p><strong>名称:</strong> ${{d.full_name}}</p>
                <p><strong>重要性:</strong> ${{d.importance}}/10</p>
                <p><strong>分类:</strong> ${{d.category}}</p>
                <p><strong>主题:</strong> ${{d.topics.join(', ') || '无'}}</p>
                <p><strong>连接数:</strong> ${{links.filter(l => l.source.id === d.id || l.target.id === d.id).length}}</p>
            `;
        }}

        // 显示提示框
        function showTooltip(event, d) {{
            const tooltip = document.getElementById('tooltip');
            tooltip.style.opacity = 1;
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY - 10) + 'px';
            tooltip.innerHTML = `
                <strong>${{d.full_name}}</strong><br/>
                重要性: ${{d.importance}}/10<br/>
                分类: ${{d.category}}<br/>
                主题: ${{d.topics.join(', ') || '无'}}
            `;
        }}

        function hideTooltip() {{
            document.getElementById('tooltip').style.opacity = 0;
        }}

        // 过滤函数
        function filterByCategory(category) {{
            // 更新按钮状态
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (category === 'all') {{
                node.style('opacity', 1);
                label.style('opacity', 1);
                link.style('opacity', 0.6);
            }} else {{
                node.style('opacity', d => d.category === category ? 1 : 0.2);
                label.style('opacity', d => d.category === category ? 1 : 0.2);
                link.style('opacity', d =>
                    (d.source.category === category || d.target.category === category) ? 0.6 : 0.1
                );
            }}
        }}

        function filterByImportance(minImportance) {{
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            node.style('opacity', d => d.importance >= minImportance ? 1 : 0.2);
            label.style('opacity', d => d.importance >= minImportance ? 1 : 0.2);
            link.style('opacity', d =>
                (d.source.importance >= minImportance || d.target.importance >= minImportance) ? 0.6 : 0.1
            );
        }}

        function resetZoom() {{
            svg.transition().duration(750).call(
                zoom.transform,
                d3.zoomIdentity
            );
        }}
    </script>
</body>
</html>"""

    return html_content

async def main():
    """主函数"""
    print_header("🔗 文档关系可视化生成")

    print("创建文档关系可视化的功能:")
    print("1. 生成Mermaid关系图")
    print("2. 创建交互式网络可视化")
    print("3. 分析文档间的关联关系")
    print("4. 提供多种视图和过滤选项")

    # 执行可视化生成
    steps = [
        ("Mermaid关系图", create_mermaid_relationship_diagram),
        ("交互式网络可视化", create_interactive_network_visualization)
    ]

    results = {}
    mermaid_content = ""

    for step_name, step_func in steps:
        try:
            print(f"\n⏳ 执行: {step_name}")
            if step_name == "Mermaid关系图":
                result, mermaid_content = await step_func()
            else:
                result = await step_func()

            results[step_name] = result

            if result:
                print(f"✅ {step_name}: 完成")
            else:
                print(f"❌ {step_name}: 失败")

        except Exception as e:
            print(f"❌ {step_name}: 异常 - {str(e)}")
            results[step_name] = False

    # 总结
    print_header("📋 文档关系可视化总结")

    successful = sum(1 for r in results.values() if r)
    total = len(results)
    success_rate = (successful / total) * 100 if total > 0 else 0

    print(f"📊 生成结果: {successful}/{total} 完成 ({success_rate:.1f}%)")

    for step_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {step_name}")

    if success_rate >= 50:
        print("\n🎉 文档关系可视化已成功创建！")
        print("✅ Mermaid关系图: data/knowledge_base/document_relationships.mmd")
        print("✅ 交互式网络图: data/knowledge_base/network_visualization.html")
        print("✅ 支持多种过滤和交互功能")
        print("✅ 显示文档间的关联关系")

        # 显示文件路径
        mermaid_path = Path("data/knowledge_base/document_relationships.mmd").absolute()
        network_path = Path("data/knowledge_base/network_visualization.html").absolute()

        print(f"\n🌐 在浏览器中查看:")
        print(f"   网络图: file:///{network_path}")

        # 渲染Mermaid图
        if mermaid_content:
            print(f"\n🔗 Mermaid关系图预览:")
            print("=" * 50)
            print(mermaid_content[:500] + "..." if len(mermaid_content) > 500 else mermaid_content)
            print("=" * 50)
    else:
        print("\n⚠️ 文档关系可视化生成需要进一步完善")

    return success_rate >= 50

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())