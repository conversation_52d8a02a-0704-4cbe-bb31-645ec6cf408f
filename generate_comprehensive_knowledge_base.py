"""
生成完整的知识库组织结构
包括知识目录、索引和关系图谱
"""

import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import re

# 添加系统路径
sys.path.append('ewandata_system')

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

async def generate_comprehensive_knowledge_catalog():
    """生成完整的知识库目录"""
    print_header("📚 生成完整知识库目录")
    
    try:
        from services.knowledge_graph import KnowledgeGraph
        
        # 创建知识库目录
        knowledge_base_dir = Path("data/knowledge_base")
        knowledge_base_dir.mkdir(parents=True, exist_ok=True)
        
        processed_dir = Path("data/processed")
        
        if not processed_dir.exists():
            print("❌ 处理结果目录不存在")
            return False
        
        # 收集所有处理结果
        all_documents = {}
        json_files = list(processed_dir.glob("*_processed.json")) + list(processed_dir.glob("*_improved_processed.json"))
        
        print(f"📊 发现处理结果文件: {len(json_files)} 个")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                doc_id = json_file.stem
                all_documents[doc_id] = data
                
            except Exception as e:
                print(f"⚠️ 读取文件失败: {json_file} - {e}")
        
        print(f"✅ 成功加载文档: {len(all_documents)} 个")
        
        # 按分类组织文档
        categories = defaultdict(list)
        topics = defaultdict(list)
        importance_levels = defaultdict(list)
        
        for doc_id, doc_data in all_documents.items():
            ai_analysis = doc_data.get('ai_analysis', {})
            classification = ai_analysis.get('classification', {})
            
            category = classification.get('category', '未分类')
            doc_topics = classification.get('topics', [])
            importance = classification.get('importance', 0)
            
            categories[category].append((doc_id, doc_data))
            
            for topic in doc_topics:
                topics[topic].append((doc_id, doc_data))
            
            if importance >= 9:
                importance_levels['极高重要性 (9-10分)'].append((doc_id, doc_data))
            elif importance >= 7:
                importance_levels['高重要性 (7-8分)'].append((doc_id, doc_data))
            elif importance >= 5:
                importance_levels['中等重要性 (5-6分)'].append((doc_id, doc_data))
            else:
                importance_levels['低重要性 (1-4分)'].append((doc_id, doc_data))
        
        # 生成知识库目录
        catalog_content = f"""# Ewandata混合AI系统知识库目录

> 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
> 文档总数: {len(all_documents)}
> 分类数量: {len(categories)}
> 主题数量: {len(topics)}

## 📊 知识库统计概览

### 文档分布
- **总文档数**: {len(all_documents)}
- **分类数量**: {len(categories)}
- **主题数量**: {len(topics)}
- **高重要性文档**: {len(importance_levels.get('极高重要性 (9-10分)', [])) + len(importance_levels.get('高重要性 (7-8分)', []))}

### 重要性分布
"""
        
        for level, docs in importance_levels.items():
            catalog_content += f"- **{level}**: {len(docs)} 个文档\n"
        
        catalog_content += "\n## 📂 按分类组织\n\n"
        
        # 按分类组织
        for category, docs in sorted(categories.items(), key=lambda x: len(x[1]), reverse=True):
            catalog_content += f"### {category} ({len(docs)} 个文档)\n\n"
            
            # 按重要性排序
            docs.sort(key=lambda x: x[1].get('ai_analysis', {}).get('classification', {}).get('importance', 0), reverse=True)
            
            for doc_id, doc_data in docs:
                file_info = doc_data.get('file_info', {})
                ai_analysis = doc_data.get('ai_analysis', {})
                classification = ai_analysis.get('classification', {})
                
                file_name = file_info.get('name', doc_id)
                importance = classification.get('importance', 0)
                doc_topics = classification.get('topics', [])
                keywords = ai_analysis.get('keywords', [])
                
                catalog_content += f"#### 📄 {file_name}\n"
                catalog_content += f"- **重要性**: {importance}/10\n"
                catalog_content += f"- **主题**: {', '.join(doc_topics) if doc_topics else '无'}\n"
                catalog_content += f"- **关键词**: {', '.join(keywords[:5])}{'...' if len(keywords) > 5 else ''}\n"
                catalog_content += f"- **文件路径**: `{doc_data.get('original_file', 'Unknown')}`\n"
                catalog_content += f"- **处理结果**: `data/processed/{doc_id}.json`\n\n"
        
        catalog_content += "\n## 🏷️ 按主题组织\n\n"
        
        # 按主题组织
        for topic, docs in sorted(topics.items(), key=lambda x: len(x[1]), reverse=True):
            catalog_content += f"### {topic} ({len(docs)} 个文档)\n\n"
            
            for doc_id, doc_data in docs:
                file_info = doc_data.get('file_info', {})
                ai_analysis = doc_data.get('ai_analysis', {})
                classification = ai_analysis.get('classification', {})
                
                file_name = file_info.get('name', doc_id)
                importance = classification.get('importance', 0)
                category = classification.get('category', '未分类')
                
                catalog_content += f"- **{file_name}** (重要性: {importance}/10, 分类: {category})\n"
        
        catalog_content += "\n## ⭐ 高重要性文档详情\n\n"
        
        # 高重要性文档详情
        high_importance_docs = []
        for level in ['极高重要性 (9-10分)', '高重要性 (7-8分)']:
            high_importance_docs.extend(importance_levels.get(level, []))
        
        high_importance_docs.sort(key=lambda x: x[1].get('ai_analysis', {}).get('classification', {}).get('importance', 0), reverse=True)
        
        for doc_id, doc_data in high_importance_docs:
            file_info = doc_data.get('file_info', {})
            ai_analysis = doc_data.get('ai_analysis', {})
            classification = ai_analysis.get('classification', {})
            
            file_name = file_info.get('name', doc_id)
            importance = classification.get('importance', 0)
            category = classification.get('category', '未分类')
            doc_topics = classification.get('topics', [])
            summary = ai_analysis.get('summary', '')
            
            catalog_content += f"### 📄 {file_name}\n"
            catalog_content += f"- **重要性**: {importance}/10\n"
            catalog_content += f"- **分类**: {category}\n"
            catalog_content += f"- **主题**: {', '.join(doc_topics)}\n"
            catalog_content += f"- **摘要**: {summary[:200]}{'...' if len(summary) > 200 else ''}\n"
            catalog_content += f"- **原始文件**: `{doc_data.get('original_file', 'Unknown')}`\n\n"
        
        catalog_content += "\n## 🔗 文档关联关系\n\n"
        
        # 分析文档关联
        keyword_relationships = defaultdict(list)
        topic_relationships = defaultdict(list)
        
        for doc_id, doc_data in all_documents.items():
            ai_analysis = doc_data.get('ai_analysis', {})
            keywords = ai_analysis.get('keywords', [])
            doc_topics = ai_analysis.get('classification', {}).get('topics', [])
            
            for keyword in keywords:
                keyword_relationships[keyword.lower()].append(doc_id)
            
            for topic in doc_topics:
                topic_relationships[topic].append(doc_id)
        
        # 显示关键词关联
        common_keywords = {k: v for k, v in keyword_relationships.items() if len(v) > 1}
        catalog_content += "### 关键词关联\n\n"
        
        for keyword, doc_ids in sorted(common_keywords.items(), key=lambda x: len(x[1]), reverse=True)[:10]:
            catalog_content += f"- **{keyword}**: {len(doc_ids)} 个文档\n"
            for doc_id in doc_ids[:3]:
                file_name = all_documents[doc_id].get('file_info', {}).get('name', doc_id)
                catalog_content += f"  - {file_name}\n"
            if len(doc_ids) > 3:
                catalog_content += f"  - ... 还有 {len(doc_ids) - 3} 个文档\n"
        
        # 显示主题关联
        catalog_content += "\n### 主题关联\n\n"
        
        for topic, doc_ids in sorted(topic_relationships.items(), key=lambda x: len(x[1]), reverse=True):
            if len(doc_ids) > 1:
                catalog_content += f"- **{topic}**: {len(doc_ids)} 个文档\n"
                for doc_id in doc_ids[:3]:
                    file_name = all_documents[doc_id].get('file_info', {}).get('name', doc_id)
                    catalog_content += f"  - {file_name}\n"
                if len(doc_ids) > 3:
                    catalog_content += f"  - ... 还有 {len(doc_ids) - 3} 个文档\n"
        
        catalog_content += f"\n---\n*由Ewandata混合AI系统自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
        
        # 保存知识库目录
        catalog_path = knowledge_base_dir / "knowledge_catalog.md"
        with open(catalog_path, 'w', encoding='utf-8') as f:
            f.write(catalog_content)
        
        print(f"✅ 知识库目录已生成: {catalog_path}")
        
        return True, all_documents, categories, topics, keyword_relationships, topic_relationships
        
    except Exception as e:
        print(f"❌ 生成知识库目录失败: {e}")
        return False, {}, {}, {}, {}, {}

async def generate_knowledge_index(all_documents, categories, topics, keyword_relationships, topic_relationships):
    """生成知识库索引"""
    print_header("📊 生成知识库索引")
    
    try:
        knowledge_base_dir = Path("data/knowledge_base")
        
        # 构建完整的知识库索引
        index_data = {
            "metadata": {
                "generated_time": datetime.now().isoformat(),
                "total_documents": len(all_documents),
                "total_categories": len(categories),
                "total_topics": len(topics),
                "total_keywords": len(keyword_relationships),
                "generator": "Ewandata混合AI系统"
            },
            "statistics": {
                "documents_by_category": {cat: len(docs) for cat, docs in categories.items()},
                "documents_by_topic": {topic: len(docs) for topic, docs in topic_relationships.items()},
                "keyword_frequency": {kw: len(docs) for kw, docs in keyword_relationships.items() if len(docs) > 1},
                "importance_distribution": {}
            },
            "documents": {},
            "relationships": {
                "keyword_based": {},
                "topic_based": {},
                "category_based": {}
            },
            "cross_references": {},
            "search_index": {
                "by_keyword": {},
                "by_topic": {},
                "by_category": {},
                "by_importance": {}
            }
        }
        
        # 处理每个文档
        for doc_id, doc_data in all_documents.items():
            file_info = doc_data.get('file_info', {})
            ai_analysis = doc_data.get('ai_analysis', {})
            classification = ai_analysis.get('classification', {})
            
            # 文档基本信息
            doc_entry = {
                "id": doc_id,
                "name": file_info.get('name', doc_id),
                "original_file": doc_data.get('original_file', ''),
                "processed_time": doc_data.get('processed_time', ''),
                "file_type": file_info.get('type', ''),
                "size_chars": file_info.get('size_chars', 0),
                "size_bytes": file_info.get('size_bytes', 0),
                "keywords": ai_analysis.get('keywords', []),
                "summary": ai_analysis.get('summary', ''),
                "category": classification.get('category', '未分类'),
                "topics": classification.get('topics', []),
                "importance": classification.get('importance', 0),
                "tags": classification.get('tags', []),
                "related_documents": []
            }
            
            index_data["documents"][doc_id] = doc_entry
        
        # 构建关系网络
        for doc_id, doc_data in all_documents.items():
            ai_analysis = doc_data.get('ai_analysis', {})
            keywords = ai_analysis.get('keywords', [])
            doc_topics = ai_analysis.get('classification', {}).get('topics', [])
            category = ai_analysis.get('classification', {}).get('category', '未分类')
            
            related_docs = set()
            
            # 基于关键词的关系
            for keyword in keywords:
                related_by_keyword = keyword_relationships.get(keyword.lower(), [])
                for related_id in related_by_keyword:
                    if related_id != doc_id:
                        related_docs.add(related_id)
            
            # 基于主题的关系
            for topic in doc_topics:
                related_by_topic = topic_relationships.get(topic, [])
                for related_id in related_by_topic:
                    if related_id != doc_id:
                        related_docs.add(related_id)
            
            # 基于分类的关系
            related_by_category = [d[0] for d in categories.get(category, [])]
            for related_id in related_by_category:
                if related_id != doc_id:
                    related_docs.add(related_id)
            
            index_data["documents"][doc_id]["related_documents"] = list(related_docs)
        
        # 构建搜索索引
        for doc_id, doc_entry in index_data["documents"].items():
            # 按关键词索引
            for keyword in doc_entry["keywords"]:
                if keyword not in index_data["search_index"]["by_keyword"]:
                    index_data["search_index"]["by_keyword"][keyword] = []
                index_data["search_index"]["by_keyword"][keyword].append(doc_id)
            
            # 按主题索引
            for topic in doc_entry["topics"]:
                if topic not in index_data["search_index"]["by_topic"]:
                    index_data["search_index"]["by_topic"][topic] = []
                index_data["search_index"]["by_topic"][topic].append(doc_id)
            
            # 按分类索引
            category = doc_entry["category"]
            if category not in index_data["search_index"]["by_category"]:
                index_data["search_index"]["by_category"][category] = []
            index_data["search_index"]["by_category"][category].append(doc_id)
            
            # 按重要性索引
            importance = doc_entry["importance"]
            importance_level = "high" if importance >= 7 else "medium" if importance >= 5 else "low"
            if importance_level not in index_data["search_index"]["by_importance"]:
                index_data["search_index"]["by_importance"][importance_level] = []
            index_data["search_index"]["by_importance"][importance_level].append(doc_id)
        
        # 保存知识库索引
        index_path = knowledge_base_dir / "knowledge_index.json"
        with open(index_path, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 知识库索引已生成: {index_path}")
        print(f"   📊 索引统计:")
        print(f"      文档数: {len(index_data['documents'])}")
        print(f"      关键词: {len(index_data['search_index']['by_keyword'])}")
        print(f"      主题: {len(index_data['search_index']['by_topic'])}")
        print(f"      分类: {len(index_data['search_index']['by_category'])}")
        
        return True, index_data
        
    except Exception as e:
        print(f"❌ 生成知识库索引失败: {e}")
        return False, {}

async def main():
    """主函数"""
    print_header("📚 完整知识库组织生成")
    
    print("生成完整的知识库组织结构:")
    print("1. 层次化知识目录 (knowledge_catalog.md)")
    print("2. 完整知识索引 (knowledge_index.json)")
    print("3. 文档关系网络和交叉引用")
    print("4. 主题聚类和重要性排序")
    
    # 生成知识库目录
    success1, all_documents, categories, topics, keyword_relationships, topic_relationships = await generate_comprehensive_knowledge_catalog()
    
    if not success1:
        print("❌ 知识库目录生成失败")
        return False
    
    # 生成知识库索引
    success2, index_data = await generate_knowledge_index(all_documents, categories, topics, keyword_relationships, topic_relationships)
    
    if not success2:
        print("❌ 知识库索引生成失败")
        return False
    
    # 总结
    print_header("📋 知识库组织生成总结")
    
    print(f"🎉 完整知识库组织已生成！")
    print(f"")
    print(f"📁 生成的文件:")
    print(f"   📚 知识库目录: data/knowledge_base/knowledge_catalog.md")
    print(f"   📊 知识库索引: data/knowledge_base/knowledge_index.json")
    print(f"")
    print(f"📊 知识库统计:")
    print(f"   总文档数: {len(all_documents)}")
    print(f"   分类数量: {len(categories)}")
    print(f"   主题数量: {len(topics)}")
    print(f"   关键词关联: {len([k for k, v in keyword_relationships.items() if len(v) > 1])}")
    print(f"")
    print(f"🔗 关系网络:")
    total_relationships = sum(len(doc.get('related_documents', [])) for doc in index_data.get('documents', {}).values())
    print(f"   文档间关系: {total_relationships} 个")
    print(f"   平均每文档关联: {total_relationships/len(all_documents):.1f} 个")
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
