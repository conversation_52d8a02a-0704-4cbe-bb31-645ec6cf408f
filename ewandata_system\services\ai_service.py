"""
AI服务模块
集成Microsoft Phi-3-mini大语言模型，提供智能文档处理能力
"""

import os
import torch
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import time

# 设置日志
logger = logging.getLogger(__name__)

class AIService:
    """AI服务类，封装大语言模型调用"""
    
    def __init__(self, model_name: str = "microsoft/Phi-3-mini-4k-instruct"):
        """
        初始化AI服务
        
        Args:
            model_name: 模型名称
        """
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.max_length = 2048
        self.is_initialized = False
        
        logger.info(f"AI服务初始化，设备: {self.device}")
    
    def initialize_model(self):
        """初始化模型"""
        if self.is_initialized:
            return True
        
        try:
            logger.info(f"正在加载模型: {self.model_name}")
            
            # 检查是否安装了transformers
            try:
                from transformers import AutoTokenizer, AutoModelForCausalLM
            except ImportError:
                logger.error("未安装transformers库，请运行: pip install transformers torch")
                return False
            
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            self.is_initialized = True
            logger.info("✅ AI模型加载成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ AI模型加载失败: {e}")
            return False
    
    def generate_response(self, prompt: str, max_new_tokens: int = 512) -> str:
        """生成AI响应"""
        if not self.is_initialized:
            if not self.initialize_model():
                return "AI模型未初始化"
        
        try:
            # 编码输入
            inputs = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
            
            # 生成响应
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码响应
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 移除原始prompt
            if prompt in response:
                response = response.replace(prompt, "").strip()
            
            return response
            
        except Exception as e:
            logger.error(f"AI生成失败: {e}")
            return f"生成失败: {str(e)}"
    
    def extract_keywords(self, content: str, max_keywords: int = 10) -> List[str]:
        """提取关键词"""
        prompt = f"""请从以下文本中提取{max_keywords}个最重要的关键词，用逗号分隔：

文本内容：
{content[:1000]}

关键词："""
        
        response = self.generate_response(prompt, max_new_tokens=100)
        
        # 解析关键词
        try:
            keywords = [kw.strip() for kw in response.split(',')]
            keywords = [kw for kw in keywords if kw and len(kw) > 1]
            return keywords[:max_keywords]
        except:
            return []
    
    def generate_summary(self, content: str, max_length: int = 200) -> str:
        """生成摘要"""
        prompt = f"""请为以下文本生成一个简洁的摘要（不超过{max_length}字）：

文本内容：
{content[:2000]}

摘要："""
        
        response = self.generate_response(prompt, max_new_tokens=256)
        
        # 清理响应
        summary = response.strip()
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
    
    def classify_content(self, content: str) -> Dict[str, Any]:
        """内容分类"""
        prompt = f"""请分析以下文本的类型和主题，返回JSON格式：

文本内容：
{content[:1000]}

请返回包含以下字段的JSON：
- category: 主要类别（如：技术文档、项目计划、学习笔记等）
- topics: 主要话题列表
- importance: 重要性评分（1-10）
- tags: 相关标签

JSON："""
        
        response = self.generate_response(prompt, max_new_tokens=200)
        
        try:
            # 尝试解析JSON
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return result
        except:
            pass
        
        # 如果JSON解析失败，返回默认结果
        return {
            "category": "未分类",
            "topics": [],
            "importance": 5,
            "tags": []
        }
    
    def analyze_project_collaboration(self, project_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析项目协同机会"""
        prompt = f"""请分析以下项目信息，识别协同合作机会：

项目信息：
{json.dumps(project_info, ensure_ascii=False, indent=2)}

请分析：
1. 技术栈特点
2. 可能的协同领域
3. 代码复用机会
4. 创新合作点

分析结果："""
        
        response = self.generate_response(prompt, max_new_tokens=400)
        
        return {
            "analysis": response,
            "collaboration_score": 7,  # 默认评分
            "recommendations": response.split('\n')[:5]  # 前5行作为建议
        }
    
    def enhance_document_data(self, content: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """增强文档数据"""
        if not content or len(content.strip()) < 10:
            return {
                "keywords": [],
                "summary": "",
                "classification": {"category": "空文档", "topics": [], "importance": 1, "tags": []},
                "ai_enhanced": False
            }
        
        try:
            # 提取关键词
            keywords = self.extract_keywords(content)
            
            # 生成摘要
            summary = self.generate_summary(content)
            
            # 内容分类
            classification = self.classify_content(content)
            
            return {
                "keywords": keywords,
                "summary": summary,
                "classification": classification,
                "ai_enhanced": True,
                "ai_processing_time": time.time()
            }
            
        except Exception as e:
            logger.error(f"AI增强处理失败: {e}")
            return {
                "keywords": [],
                "summary": f"AI处理失败: {str(e)}",
                "classification": {"category": "处理失败", "topics": [], "importance": 1, "tags": []},
                "ai_enhanced": False
            }


# 全局AI服务实例
_ai_service = None

def get_ai_service() -> AIService:
    """获取AI服务实例（单例模式）"""
    global _ai_service
    if _ai_service is None:
        _ai_service = AIService()
    return _ai_service


def test_ai_service():
    """测试AI服务"""
    print("🧠 测试AI服务...")
    
    ai = get_ai_service()
    
    # 测试文本
    test_content = """
    这是一个关于人工智能和机器学习的技术文档。
    文档介绍了深度学习的基本概念，包括神经网络、反向传播算法等。
    同时讨论了在自然语言处理领域的应用，如文本分类、情感分析等。
    """
    
    print("测试内容:", test_content[:50] + "...")
    
    # 测试关键词提取
    keywords = ai.extract_keywords(test_content)
    print(f"关键词: {keywords}")
    
    # 测试摘要生成
    summary = ai.generate_summary(test_content)
    print(f"摘要: {summary}")
    
    # 测试分类
    classification = ai.classify_content(test_content)
    print(f"分类: {classification}")


if __name__ == "__main__":
    test_ai_service()
