"""
修复Git连接问题的专用脚本
针对"Empty reply from server"错误
"""

import os
import subprocess
import time

def run_command(cmd, timeout=30):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, 
                              text=True, timeout=timeout, cwd="E:/Ewandata")
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def print_step(step, description):
    print(f"\n[步骤 {step}] {description}")
    print("-" * 50)

def fix_git_config():
    """修复Git配置"""
    print_step(1, "修复Git配置")
    
    fixes = [
        ("设置HTTP版本", "git config --global http.version HTTP/1.1"),
        ("增加缓冲区", "git config --global http.postBuffer 524288000"),
        ("设置超时时间", "git config --global http.lowSpeedLimit 0"),
        ("设置超时时间2", "git config --global http.lowSpeedTime 999999"),
        ("禁用SSL验证", "git config --global http.sslVerify false"),
        ("设置凭据助手", "git config --global credential.helper manager-core")
    ]
    
    for name, cmd in fixes:
        success, stdout, stderr = run_command(cmd)
        if success:
            print(f"✅ {name}")
        else:
            print(f"❌ {name}: {stderr}")

def clear_git_cache():
    """清理Git缓存"""
    print_step(2, "清理Git缓存和凭据")
    
    commands = [
        ("清理Git缓存", "git gc --prune=now"),
        ("清理远程缓存", "git remote prune origin"),
    ]
    
    for name, cmd in commands:
        success, stdout, stderr = run_command(cmd)
        if success:
            print(f"✅ {name}")
        else:
            print(f"⚠️ {name}: {stderr}")

def test_different_urls():
    """测试不同的URL格式"""
    print_step(3, "测试不同的远程URL")
    
    urls = [
        ("原始HTTPS", "https://github.com/EwanCosmos/Ewandata.git"),
        ("带用户名HTTPS", "https://<EMAIL>/EwanCosmos/Ewandata.git"),
        ("SSH", "**************:EwanCosmos/Ewandata.git")
    ]
    
    for name, url in urls:
        print(f"\n测试 {name}: {url}")
        
        # 设置URL
        success, _, _ = run_command(f'git remote set-url origin "{url}"')
        if not success:
            print(f"❌ 设置URL失败")
            continue
        
        # 测试连接
        success, stdout, stderr = run_command("git ls-remote --heads origin", timeout=20)
        if success:
            print(f"✅ {name} 连接成功!")
            print(f"远程分支: {stdout}")
            return url
        else:
            print(f"❌ {name} 连接失败: {stderr}")
    
    return None

def try_push_with_working_url(url):
    """使用工作的URL尝试推送"""
    print_step(4, f"使用工作URL推送: {url}")
    
    if not url:
        print("❌ 没有找到工作的URL")
        return False
    
    # 尝试推送
    success, stdout, stderr = run_command("git push origin main", timeout=60)
    
    if success:
        print("✅ 推送成功!")
        print(f"输出: {stdout}")
        return True
    else:
        print(f"❌ 推送失败: {stderr}")
        
        # 如果是认证问题，尝试重新认证
        if "authentication" in stderr.lower() or "credential" in stderr.lower():
            print("\n尝试重新认证...")
            
            # 清除凭据
            run_command("git config --global --unset credential.helper")
            run_command("git config --global credential.helper manager-core")
            
            # 再次尝试推送
            success, stdout, stderr = run_command("git push origin main", timeout=60)
            if success:
                print("✅ 重新认证后推送成功!")
                return True
            else:
                print(f"❌ 重新认证后仍失败: {stderr}")
        
        return False

def create_ssh_solution():
    """创建SSH解决方案"""
    print_step(5, "SSH解决方案")
    
    print("如果HTTPS持续失败，建议使用SSH:")
    print()
    print("1. 生成SSH密钥 (如果没有):")
    print("   ssh-keygen -t rsa -b 4096 -C '<EMAIL>'")
    print()
    print("2. 添加SSH密钥到GitHub:")
    print("   - 复制 ~/.ssh/id_rsa.pub 内容")
    print("   - 在GitHub Settings > SSH and GPG keys 中添加")
    print()
    print("3. 测试SSH连接:")
    print("   ssh -T **************")
    print()
    print("4. 切换到SSH URL:")
    print("   git remote set-<NAME_EMAIL>:EwanCosmos/Ewandata.git")

def verify_final_status():
    """验证最终状态"""
    print_step(6, "验证最终状态")
    
    # 检查远程URL
    success, url, _ = run_command("git remote get-url origin")
    if success:
        print(f"当前远程URL: {url}")
    
    # 检查分支状态
    success, status, _ = run_command("git status -b")
    if success:
        lines = status.split('\n')
        for line in lines[:3]:
            if line.strip():
                print(f"状态: {line}")
    
    # 最后一次推送尝试
    print("\n最后一次推送尝试...")
    success, stdout, stderr = run_command("git push origin main", timeout=60)
    
    if success:
        print("🎉 最终推送成功!")
        return True
    else:
        print(f"❌ 最终推送失败: {stderr}")
        return False

def main():
    """主修复函数"""
    print("🔧 Git连接问题专项修复")
    print("=" * 60)
    print("针对 'Empty reply from server' 错误")
    
    os.chdir("E:/Ewandata")
    
    # 1. 修复Git配置
    fix_git_config()
    
    # 2. 清理缓存
    clear_git_cache()
    
    # 3. 测试不同URL
    working_url = test_different_urls()
    
    # 4. 尝试推送
    if working_url:
        push_success = try_push_with_working_url(working_url)
        
        if push_success:
            print("\n🎉 问题已解决！GitHub推送成功！")
        else:
            # 5. 提供SSH解决方案
            create_ssh_solution()
    else:
        print("\n❌ 所有URL都无法连接")
        create_ssh_solution()
    
    # 6. 验证最终状态
    final_success = verify_final_status()
    
    if final_success:
        print("\n✅ 修复完成！Ewandata已成功同步到GitHub！")
    else:
        print("\n⚠️ 需要手动处理，请参考SSH解决方案")

if __name__ == "__main__":
    main()
