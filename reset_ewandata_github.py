"""
重置Ewandata项目与GitHub的连接
清理混乱的配置，重新建立干净的同步
"""

import os
import subprocess
import shutil
from pathlib import Path
import json

def print_step(step, description):
    print(f"\n[步骤 {step}] {description}")
    print("=" * 60)

def run_command(cmd, timeout=30):
    """执行命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, 
                              text=True, timeout=timeout, cwd="E:/Ewandata")
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def backup_important_data():
    """备份重要数据"""
    print_step(1, "备份重要数据")
    
    backup_dir = Path("E:/Ewandata_backup")
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    backup_dir.mkdir()
    
    # 备份核心数据
    important_items = [
        "data/",
        "ewandata_system/",
        "README.md"
    ]
    
    for item in important_items:
        src = Path("E:/Ewandata") / item
        if src.exists():
            if src.is_dir():
                shutil.copytree(src, backup_dir / item)
                print(f"✅ 备份目录: {item}")
            else:
                shutil.copy2(src, backup_dir / item)
                print(f"✅ 备份文件: {item}")
        else:
            print(f"⚠️ 未找到: {item}")
    
    print(f"✅ 数据已备份到: {backup_dir}")
    return backup_dir

def clean_git_config():
    """清理Git配置"""
    print_step(2, "清理Git配置")
    
    os.chdir("E:/Ewandata")
    
    # 清理本地Git配置
    configs_to_clear = [
        "http.version",
        "http.postBuffer", 
        "http.lowSpeedLimit",
        "http.lowSpeedTime",
        "http.sslVerify"
    ]
    
    for config in configs_to_clear:
        run_command(f"git config --unset {config}")
        print(f"✅ 清理配置: {config}")
    
    # 重置到默认配置
    essential_configs = [
        ("user.name", "EwanCosmos"),
        ("user.email", "<EMAIL>"),
        ("credential.helper", "manager-core")
    ]
    
    for key, value in essential_configs:
        success, _, _ = run_command(f'git config {key} "{value}"')
        if success:
            print(f"✅ 设置配置: {key} = {value}")

def reset_remote_connection():
    """重置远程连接"""
    print_step(3, "重置远程连接")
    
    # 移除现有远程
    run_command("git remote remove origin")
    print("✅ 移除旧的远程连接")
    
    # 重新添加远程
    github_url = "https://github.com/EwanCosmos/Ewandata.git"
    success, _, stderr = run_command(f'git remote add origin {github_url}')
    
    if success:
        print(f"✅ 重新添加远程: {github_url}")
    else:
        print(f"❌ 添加远程失败: {stderr}")
        return False
    
    # 验证远程连接
    success, stdout, stderr = run_command("git remote -v")
    if success:
        print(f"✅ 远程验证:\n{stdout}")
        return True
    else:
        print(f"❌ 远程验证失败: {stderr}")
        return False

def clean_working_directory():
    """清理工作目录"""
    print_step(4, "清理工作目录")
    
    # 删除测试和临时文件
    files_to_remove = [
        "comprehensive_test_suite.py",
        "github_sync_test.py", 
        "fix_github_sync.py",
        "quick_sync_fix.py",
        "final_sync_test.py",
        "git_diagnostic.py",
        "fix_git_connection.py",
        "test_report.json",
        "quick_start.bat"
    ]
    
    for file_name in files_to_remove:
        file_path = Path("E:/Ewandata") / file_name
        if file_path.exists():
            file_path.unlink()
            print(f"✅ 删除: {file_name}")
    
    # 删除测试目录
    dirs_to_remove = [
        "桌面文件包",
        ".augment"
    ]
    
    for dir_name in dirs_to_remove:
        dir_path = Path("E:/Ewandata") / dir_name
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"✅ 删除目录: {dir_name}")

def create_clean_gitignore():
    """创建干净的.gitignore"""
    print_step(5, "创建干净的.gitignore")
    
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
ewandata_env/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/

# Database backups
*.db.backup
*.db.bak

# Test files
test_*.py
*_test.py

# Large model files
models/*.bin
models/*.safetensors
"""
    
    gitignore_path = Path("E:/Ewandata") / ".gitignore"
    with open(gitignore_path, 'w', encoding='utf-8') as f:
        f.write(gitignore_content)
    
    print("✅ 创建干净的.gitignore文件")

def test_github_connection():
    """测试GitHub连接"""
    print_step(6, "测试GitHub连接")
    
    # 测试基本连接
    success, stdout, stderr = run_command("git ls-remote --heads origin", timeout=30)
    
    if success:
        print("✅ GitHub连接正常")
        print(f"远程分支:\n{stdout}")
        return True
    else:
        print(f"❌ GitHub连接失败: {stderr}")
        return False

def sync_with_github():
    """与GitHub同步"""
    print_step(7, "与GitHub同步")
    
    # 获取远程更新
    success, _, stderr = run_command("git fetch origin")
    if success:
        print("✅ 获取远程更新成功")
    else:
        print(f"⚠️ 获取远程更新失败: {stderr}")
    
    # 检查当前状态
    success, status, _ = run_command("git status --porcelain")
    if success:
        if status:
            print(f"📝 工作区有变更: {len(status.split())} 个文件")
        else:
            print("✅ 工作区干净")
    
    # 添加重要文件
    important_files = ["data/", "ewandata_system/", "README.md", ".gitignore"]
    
    for file_path in important_files:
        if Path("E:/Ewandata") / file_path.exists():
            success, _, _ = run_command(f"git add {file_path}")
            if success:
                print(f"✅ 添加: {file_path}")
    
    # 创建提交
    commit_msg = "重置项目：清理配置，恢复与GitHub的正常同步"
    success, stdout, stderr = run_command(f'git commit -m "{commit_msg}"')
    
    if success:
        print(f"✅ 创建提交: {commit_msg}")
    elif "nothing to commit" in stderr:
        print("ℹ️ 没有变更需要提交")
    else:
        print(f"⚠️ 提交失败: {stderr}")
    
    # 推送到GitHub
    print("\n尝试推送到GitHub...")
    success, stdout, stderr = run_command("git push origin main", timeout=60)
    
    if success:
        print("🎉 推送成功！")
        print(f"输出: {stdout}")
        return True
    else:
        print(f"❌ 推送失败: {stderr}")
        return False

def verify_final_state():
    """验证最终状态"""
    print_step(8, "验证最终状态")
    
    checks = [
        ("Git配置", "git config --list | grep -E '(user|remote|credential)'"),
        ("远程连接", "git remote -v"),
        ("分支状态", "git status -b"),
        ("最近提交", "git log --oneline -3")
    ]
    
    for name, cmd in checks:
        success, stdout, stderr = run_command(cmd)
        if success:
            print(f"✅ {name}:")
            for line in stdout.split('\n')[:5]:  # 只显示前5行
                if line.strip():
                    print(f"   {line}")
        else:
            print(f"❌ {name}: {stderr}")

def main():
    """主重置函数"""
    print("🔄 Ewandata项目GitHub连接重置")
    print("=" * 80)
    print("这将清理所有混乱的配置，重新建立干净的GitHub连接")
    
    # 确认操作
    print("\n⚠️ 注意：此操作将：")
    print("1. 备份重要数据到 E:/Ewandata_backup")
    print("2. 清理所有测试文件和混乱配置")
    print("3. 重置Git配置和远程连接")
    print("4. 重新同步到GitHub")
    
    confirm = input("\n是否继续？(y/N): ").lower().strip()
    if confirm != 'y':
        print("操作已取消")
        return
    
    try:
        # 1. 备份数据
        backup_dir = backup_important_data()
        
        # 2. 清理Git配置
        clean_git_config()
        
        # 3. 重置远程连接
        remote_ok = reset_remote_connection()
        if not remote_ok:
            print("❌ 远程连接重置失败，请检查网络")
            return
        
        # 4. 清理工作目录
        clean_working_directory()
        
        # 5. 创建干净的.gitignore
        create_clean_gitignore()
        
        # 6. 测试连接
        connection_ok = test_github_connection()
        if not connection_ok:
            print("❌ GitHub连接测试失败")
            return
        
        # 7. 同步到GitHub
        sync_ok = sync_with_github()
        
        # 8. 验证最终状态
        verify_final_state()
        
        if sync_ok:
            print("\n🎉 重置完成！Ewandata项目已成功重新连接到GitHub！")
            print(f"✅ 数据备份位置: {backup_dir}")
            print("✅ 配置已清理并重置")
            print("✅ GitHub同步正常")
        else:
            print("\n⚠️ 重置基本完成，但推送失败")
            print("请稍后手动尝试: git push origin main")
            
    except Exception as e:
        print(f"\n❌ 重置过程中出现错误: {e}")
        print("请检查备份数据并手动恢复")

if __name__ == "__main__":
    main()
