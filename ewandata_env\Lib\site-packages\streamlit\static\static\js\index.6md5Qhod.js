import{bt as _,bK as ye,bu as $,r as u,z as $e,C as ne,j as R,bx as Re,bq as Oe,b7 as _e,br as Se,aC as oe,H as ie,c4 as ae,bp as Pe}from"./index.C1NIn1Y2.js";import{a as we}from"./useBasicWidgetState.Ci89jaH5.js";import"./FormClearHelper.D1M9GM_c.js";var se={vertical:"vertical",horizontal:"horizontal"};function le(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function D(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?le(Object(n),!0).forEach(function(r){O(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):le(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var E=0,L=1,T=2;function z(e){return e.$isActive?T:e.$isHovered?L:E}function Me(e){var t=e.$theme.colors,n=e.$disabled,r=e.$checked,o=e.$isFocusVisible,i=e.$error;if(n)return t.tickFillDisabled;if(r)if(i)switch(z(e)){case E:return t.tickFillErrorSelected;case L:return t.tickFillErrorSelectedHover;case T:return t.tickFillErrorSelectedHoverActive}else switch(z(e)){case E:return t.tickFillSelected;case L:return t.tickFillSelectedHover;case T:return t.tickFillSelectedHoverActive}else return o?t.borderSelected:i?t.tickBorderError:t.tickBorder;return null}function ke(e){var t=e.$theme.colors;if(e.$disabled)return t.tickMarkFillDisabled;if(e.$checked)return t.tickMarkFill;if(e.$error)switch(z(e)){case E:return t.tickFillError;case L:return t.tickFillErrorHover;case T:return t.tickFillErrorHoverActive}else switch(z(e)){case E:return t.tickFill;case L:return t.tickFillHover;case T:return t.tickFillActive}}function Fe(e){var t=e.$labelPlacement,n=t===void 0?"":t,r=e.$theme,o;switch(n){case"top":o="Bottom";break;case"bottom":o="Top";break;case"left":o=r.direction==="rtl"?"Left":"Right";break;default:case"right":o=r.direction==="rtl"?"Right":"Left";break}var i=r.sizing,s=i.scale300;return O({},"padding".concat(o),s)}function Ie(e){var t=e.$disabled,n=e.$theme,r=n.colors;return t?r.contentSecondary:r.contentPrimary}var q=_("div",function(e){var t=e.$disabled,n=e.$align;return{display:"flex",flexWrap:"wrap",flexDirection:n==="horizontal"?"row":"column",alignItems:n==="horizontal"?"center":"flex-start",cursor:t?"not-allowed":"pointer","-webkit-tap-highlight-color":"transparent"}});q.displayName="RadioGroupRoot";q.displayName="RadioGroupRoot";var X=_("label",function(e){var t,n=e.$disabled,r=e.$hasDescription,o=e.$labelPlacement,i=e.$theme,s=e.$align,a=i.sizing,c=s==="horizontal",f=i.direction==="rtl"?"Left":"Right";return t={flexDirection:o==="top"||o==="bottom"?"column":"row",display:"flex",alignItems:"center",cursor:n?"not-allowed":"pointer",marginTop:a.scale200},O(t,"margin".concat(f),c?a.scale200:null),O(t,"marginBottom",r&&!c?null:a.scale200),t});X.displayName="Root";X.displayName="Root";var K=_("div",function(e){var t=e.$theme,n=t.animation,r=t.sizing;return{backgroundColor:ke(e),borderTopLeftRadius:"50%",borderTopRightRadius:"50%",borderBottomRightRadius:"50%",borderBottomLeftRadius:"50%",height:e.$checked?r.scale200:r.scale550,transitionDuration:n.timing200,transitionTimingFunction:n.easeOutCurve,width:e.$checked?r.scale200:r.scale550}});K.displayName="RadioMarkInner";K.displayName="RadioMarkInner";var J=_("div",function(e){var t=e.$theme,n=t.animation,r=t.sizing;return{alignItems:"center",backgroundColor:Me(e),borderTopLeftRadius:"50%",borderTopRightRadius:"50%",borderBottomRightRadius:"50%",borderBottomLeftRadius:"50%",boxShadow:e.$isFocusVisible&&e.$checked?"0 0 0 3px ".concat(e.$theme.colors.accent):"none",display:"flex",height:r.scale700,justifyContent:"center",marginTop:r.scale0,marginRight:r.scale0,marginBottom:r.scale0,marginLeft:r.scale0,outline:"none",verticalAlign:"middle",width:r.scale700,flexShrink:0,transitionDuration:n.timing200,transitionTimingFunction:n.easeOutCurve}});J.displayName="RadioMarkOuter";J.displayName="RadioMarkOuter";var Q=_("div",function(e){var t=e.$theme.typography;return D(D({verticalAlign:"middle"},Fe(e)),{},{color:Ie(e)},t.LabelMedium)});Q.displayName="Label";Q.displayName="Label";var Y=_("input",{width:0,height:0,marginTop:0,marginRight:0,marginBottom:0,marginLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0,clip:"rect(0 0 0 0)",position:"absolute"});Y.displayName="Input";Y.displayName="Input";var Z=_("div",function(e){var t,n=e.$theme,r=e.$align,o=r==="horizontal",i=n.direction==="rtl"?"Right":"Left",s=n.direction==="rtl"?"Left":"Right";return D(D({},n.typography.ParagraphSmall),{},(t={color:n.colors.contentSecondary,cursor:"auto"},O(t,"margin".concat(i),r==="horizontal"?null:n.sizing.scale900),O(t,"margin".concat(s),o?n.sizing.scale200:null),O(t,"maxWidth","240px"),t))});Z.displayName="Description";Z.displayName="Description";function N(e){"@babel/helpers - typeof";return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},N(e)}function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},V.apply(this,arguments)}function Ee(e,t){return Ae(e)||xe(e,t)||Te(e,t)||Le()}function Le(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Te(e,t){if(e){if(typeof e=="string")return ue(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ue(e,t)}}function ue(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function xe(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],o=!0,i=!1,s,a;try{for(n=n.call(e);!(o=(s=n.next()).done)&&(r.push(s.value),!(t&&r.length===t));o=!0);}catch(c){i=!0,a=c}finally{try{!o&&n.return!=null&&n.return()}finally{if(i)throw a}}return r}}function Ae(e){if(Array.isArray(e))return e}function je(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function De(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ze(e,t,n){return t&&De(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Be(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&U(e,t)}function U(e,t){return U=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},U(e,t)}function Ce(e){var t=Ne();return function(){var r=B(e),o;if(t){var i=B(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return He(this,o)}}function He(e,t){if(t&&(N(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return A(e)}function A(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ne(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function B(e){return B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},B(e)}function j(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var pe=function(e){Be(n,e);var t=Ce(n);function n(){var r;je(this,n);for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return r=t.call.apply(t,[this].concat(i)),j(A(r),"state",{isFocusVisible:!1,focusedRadioIndex:-1}),j(A(r),"handleFocus",function(a,c){ye(a)&&r.setState({isFocusVisible:!0}),r.setState({focusedRadioIndex:c}),r.props.onFocus&&r.props.onFocus(a)}),j(A(r),"handleBlur",function(a,c){r.state.isFocusVisible!==!1&&r.setState({isFocusVisible:!1}),r.setState({focusedRadioIndex:-1}),r.props.onBlur&&r.props.onBlur(a)}),r}return ze(n,[{key:"render",value:function(){var o=this,i=this.props.overrides,s=i===void 0?{}:i,a=$(s.RadioGroupRoot,q),c=Ee(a,2),f=c[0],b=c[1];return u.createElement(f,V({id:this.props.id,role:"radiogroup","aria-describedby":this.props["aria-describedby"],"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props.error||null,"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],$align:this.props.align,$disabled:this.props.disabled,$error:this.props.error,$required:this.props.required},b),u.Children.map(this.props.children,function(p,l){if(!u.isValidElement(p))return null;var d=o.props.value===p.props.value;return u.cloneElement(p,{align:o.props.align,autoFocus:o.props.autoFocus,checked:d,disabled:o.props.disabled||p.props.disabled,error:o.props.error,isFocused:o.state.focusedRadioIndex===l,isFocusVisible:o.state.isFocusVisible,tabIndex:l===0&&!o.props.value||d?"0":"-1",labelPlacement:o.props.labelPlacement,name:o.props.name,onBlur:function(g){return o.handleBlur(g,l)},onFocus:function(g){return o.handleFocus(g,l)},onChange:o.props.onChange,onMouseEnter:o.props.onMouseEnter,onMouseLeave:o.props.onMouseLeave})}))}}]),n}(u.Component);j(pe,"defaultProps",{name:"",value:"",disabled:!1,autoFocus:!1,labelPlacement:"right",align:"vertical",error:!1,required:!1,onChange:function(){},onMouseEnter:function(){},onMouseLeave:function(){},onFocus:function(){},onBlur:function(){},overrides:{}});function G(e){"@babel/helpers - typeof";return G=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},G(e)}function y(){return y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},y.apply(this,arguments)}function M(e,t){return We(e)||Ge(e,t)||Ue(e,t)||Ve()}function Ve(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ue(e,t){if(e){if(typeof e=="string")return ce(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ce(e,t)}}function ce(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ge(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],o=!0,i=!1,s,a;try{for(n=n.call(e);!(o=(s=n.next()).done)&&(r.push(s.value),!(t&&r.length===t));o=!0);}catch(c){i=!0,a=c}finally{try{!o&&n.return!=null&&n.return()}finally{if(i)throw a}}return r}}function We(e){if(Array.isArray(e))return e}function qe(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Xe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Ke(e,t,n){return t&&Xe(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Je(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&W(e,t)}function W(e,t){return W=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},W(e,t)}function Qe(e){var t=Ze();return function(){var r=C(e),o;if(t){var i=C(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return Ye(this,o)}}function Ye(e,t){if(t&&(G(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return k(e)}function k(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ze(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},C(e)}function F(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function et(e){return e==="top"||e==="left"}function tt(e){return e==="bottom"||e==="right"}var rt=function(t){return t.stopPropagation()},fe=function(e){Je(n,e);var t=Qe(n);function n(){var r;qe(this,n);for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return r=t.call.apply(t,[this].concat(i)),F(k(r),"state",{isActive:!1,isHovered:!1}),F(k(r),"onMouseEnter",function(a){r.setState({isHovered:!0}),r.props.onMouseEnter&&r.props.onMouseEnter(a)}),F(k(r),"onMouseLeave",function(a){r.setState({isHovered:!1}),r.props.onMouseLeave&&r.props.onMouseLeave(a)}),F(k(r),"onMouseDown",function(a){r.setState({isActive:!0}),r.props.onMouseDown&&r.props.onMouseDown(a)}),F(k(r),"onMouseUp",function(a){r.setState({isActive:!1}),r.props.onMouseUp&&r.props.onMouseUp(a)}),r}return Ke(n,[{key:"componentDidMount",value:function(){var o;this.props.autoFocus&&(o=this.props.inputRef)!==null&&o!==void 0&&o.current&&this.props.inputRef.current.focus()}},{key:"render",value:function(){var o=this.props.overrides,i=o===void 0?{}:o,s=$(i.Root,X),a=M(s,2),c=a[0],f=a[1],b=$(i.Label,Q),p=M(b,2),l=p[0],d=p[1],S=$(i.Input,Y),g=M(S,2),P=g[0],H=g[1],x=$(i.Description,Z),I=M(x,2),v=I[0],h=I[1],m=$(i.RadioMarkInner,K),ee=M(m,2),de=ee[0],he=ee[1],be=$(i.RadioMarkOuter,J),te=M(be,2),ge=te[0],ve=te[1],w={$align:this.props.align,$checked:this.props.checked,$disabled:this.props.disabled,$hasDescription:!!this.props.description,$isActive:this.state.isActive,$error:this.props.error,$isFocused:this.props.isFocused,$isFocusVisible:this.props.isFocused&&this.props.isFocusVisible,$isHovered:this.state.isHovered,$labelPlacement:this.props.labelPlacement,$required:this.props.required,$value:this.props.value},re=u.createElement(l,y({},w,d),this.props.containsInteractiveElement?u.createElement("div",{onClick:function(me){return me.preventDefault()}},this.props.children):this.props.children);return u.createElement(u.Fragment,null,u.createElement(c,y({"data-baseweb":"radio",onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave,onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp},w,f),et(this.props.labelPlacement)&&re,u.createElement(ge,y({},w,ve),u.createElement(de,y({},w,he))),u.createElement(P,y({"aria-invalid":this.props.error||null,checked:this.props.checked,disabled:this.props.disabled,name:this.props.name,onBlur:this.props.onBlur,onFocus:this.props.onFocus,onClick:rt,onChange:this.props.onChange,ref:this.props.inputRef,required:this.props.required,tabIndex:this.props.tabIndex,type:"radio",value:this.props.value},w,H)),tt(this.props.labelPlacement)&&re),!!this.props.description&&u.createElement(v,y({},w,h),this.props.description))}}]),n}(u.Component);F(fe,"defaultProps",{overrides:{},containsInteractiveElement:!1,checked:!1,disabled:!1,autoFocus:!1,inputRef:u.createRef(),align:"vertical",error:!1,onChange:function(){},onMouseEnter:function(){},onMouseLeave:function(){},onMouseDown:function(){},onMouseUp:function(){},onFocus:function(){},onBlur:function(){}});function nt(e){const t=parseFloat(e.sizes.checkbox),n=parseFloat(e.spacing.threeXS),r=ie(t.toString()),o=Math.round(r*.375);let i=Math.round(ie((t-n).toString()));return i>=r&&(i-=1),[ae(o,"px"),ae(i,"px")]}function ot({disabled:e,horizontal:t,value:n,onChange:r,options:o,captions:i,label:s,labelVisibility:a,help:c}){const[f,b]=u.useState(n??null);u.useEffect(()=>{n!==f&&b(n??null)},[n]);const p=u.useCallback(v=>{const h=parseInt(v.target.value,10);b(h),r(h)},[r]),l=$e(),d=i.length>0,S=o.length>0,g=S?o:["No options to select."],P=e||!S,H=v=>v==""&&t&&d?"&nbsp;":v,[x,I]=nt(l);return ne("div",{className:"stRadio","data-testid":"stRadio",children:[R(Se,{label:s,disabled:P,labelVisibility:a,children:c&&R(Re,{children:R(Oe,{content:c,placement:_e.TOP_RIGHT})})}),R(pe,{onChange:p,value:f!==null?f.toString():void 0,disabled:P,align:t?se.horizontal:se.vertical,"aria-label":s,"data-testid":"stRadioGroup",overrides:{RadioGroupRoot:{style:{gap:d?l.spacing.sm:l.spacing.none,minHeight:l.sizes.minElementHeight}}},children:g.map((v,h)=>ne(fe,{value:h.toString(),overrides:{Root:{style:({$isFocusVisible:m})=>({marginBottom:l.spacing.none,marginTop:l.spacing.none,marginRight:d?l.spacing.sm:l.spacing.lg,paddingLeft:l.spacing.none,alignItems:"start",paddingRight:l.spacing.threeXS,backgroundColor:m?l.colors.darkenedBgMix25:""})},RadioMarkOuter:{style:({$checked:m})=>({width:l.sizes.checkbox,height:l.sizes.checkbox,marginTop:"0.35rem",marginRight:l.spacing.none,marginLeft:l.spacing.none,backgroundColor:m&&!P?l.colors.primary:l.colors.fadedText40})},RadioMarkInner:{style:({$checked:m})=>({height:m?x:I,width:m?x:I})},Label:{style:{color:P?l.colors.fadedText40:l.colors.bodyText,position:"relative",top:l.spacing.px}}},children:[R(oe,{source:v,allowHTML:!1,isLabel:!0,largerLabel:!0}),d&&R(oe,{source:H(i[h]),allowHTML:!1,isCaption:!0,isLabel:!0})]},h))})]})}const it=u.memo(ot);function at({disabled:e,element:t,widgetMgr:n,fragmentId:r}){const[o,i]=we({getStateFromWidgetMgr:st,getDefaultStateFromProto:lt,getCurrStateFromProto:ut,updateWidgetMgrState:ct,element:t,widgetMgr:n,fragmentId:r}),s=u.useCallback(d=>{i({value:d,fromUi:!0})},[i]),{horizontal:a,options:c,captions:f,label:b,labelVisibility:p,help:l}=t;return R(it,{label:b,onChange:s,options:c,captions:f,disabled:e,horizontal:a,labelVisibility:Pe(p==null?void 0:p.value),value:o??null,help:l})}function st(e,t){return e.getIntValue(t)}function lt(e){return e.default??null}function ut(e){return e.value??null}function ct(e,t,n,r){t.setIntValue(e,n.value??null,{fromUi:n.fromUi},r)}const bt=u.memo(at);export{bt as default};
