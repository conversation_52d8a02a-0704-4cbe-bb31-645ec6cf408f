{"id": "doc_482a783c_1751558955", "timestamp": "2025-07-04T00:09:15.503587", "document": {"file_name": "README.md", "file_path": "E:\\Ewandata\\README.md", "file_extension": ".md", "size": 4444, "size_mb": 0.004238128662109375, "created_time": "2025-06-28T12:59:02.107303", "modified_time": "2025-07-03T23:31:02.201137", "file_hash": "482a783c8b95ee7dc9bcea764dbc66b0", "mime_type": "text/markdown", "type": "markdown", "content": "# 🧠 Ewandata 本地知识管理系统\n\n> 基于RTX 4070 GPU的个人AI数字助手和知识管理系统\n\n## 🎯 项目概述\n\nEwandata是一个完全本地化部署的智能知识管理系统，专为个人学习和项目管理设计。系统充分利用RTX 4070 12GB GPU的计算能力，提供AI驱动的文档处理、知识组织和项目跟踪功能。\n\n## ✨ 核心功能\n\n### 📚 智能知识库\n- **多格式支持**: PDF、DOCX、图片、Markdown等格式自动处理\n- **AI内容分析**: 自动摘要、关键词提取、主题分类\n- **语义搜索**: 基于内容理解的精准搜索\n- **知识关联**: 自动发现内容间的隐藏联系\n\n### 📁 项目管理\n- **自动发现**: 实时监控E盘所有项目变化\n- **关联分析**: 识别项目间的技术栈重叠和协同机会\n- **知识复用**: 跨项目的代码和经验复用建议\n\n### 🔄 自动化工作流\n- **文件监控**: 自动处理临时记文件夹的新内容\n- **GitHub同步**: 自动同步处理后的内容到GitHub\n- **本地清理**: 智能清理临时文件，保持系统整洁\n\n## 🚀 快速开始\n\n### 系统要求\n- **硬件**: RTX 4070 12GB GPU (已满足)\n- **软件**: Python 3.9+, CUDA 11.8+, Git 2.30+\n- **存储**: 100GB+ SSD空间\n\n### 一键安装\n```bash\n# 1. 双击运行快速启动脚本\nquick_start.bat\n\n# 2. 等待自动安装完成\n# 系统会自动：\n# - 检查环境依赖\n# - 创建虚拟环境\n# - 安装所需包\n# - 创建目录结构\n# - 配置临时记文件夹\n```\n\n### 启动系统\n```bash\n# 启动完整系统\nstart_system.bat\n\n# 或分别启动各组件\npython api/main.py          # API服务\nstreamlit run app/streamlit_app.py  # Web界面\npython services/file_monitor.py    # 文件监控\n```\n\n## 📋 系统架构\n\n```\nEwandata系统架构：\n├── 数据输入层\n│   ├── 临时记文件夹监控\n│   ├── 多格式文档处理器\n│   └── 网页内容抓取器\n├── AI处理层\n│   ├── Microsoft Phi-3-mini (主要推理)\n│   ├── 文本嵌入模型 (语义理解)\n│   └── OCR引擎 (图片文字识别)\n├── 知识存储层\n│   ├── 向量数据库 (ChromaDB)\n│   ├── 关系数据库 (SQLite)\n│   └── 文件系统 (Markdown + JSON)\n└── 应用服务层\n    ├── FastAPI后端服务\n    ├── 项目跟踪服务\n    └── GitHub同步服务\n```\n\n## 🎛️ 使用方式\n\n### Web管理界面\n访问 `http://localhost:8501` 使用图形化界面：\n- 📊 系统概览仪表板\n- 📚 知识库管理\n- 🔍 智能搜索\n- 📁 项目跟踪\n- ⚙️ 系统设置\n\n### API接口\n访问 `http://localhost:8000/docs` 查看API文档：\n- 文档上传和处理\n- 智能搜索接口\n- 项目信息获取\n- 系统状态监控\n\n### 自动化工作流\n1. 将文件放入 `C:\\Users\\<USER>\\Desktop\\临时记`\n2. 系统自动检测并处理\n3. 生成Markdown和JSON格式\n4. 自动同步到GitHub\n5. 清理本地临时文件\n\n## 📊 预期效果\n\n### 效率提升\n- ⚡ 节省90%手动整理时间\n- 🎯 提高检索效率5倍\n- 🔗 自动发现知识关联\n- 📈 加速学习和项目开发\n\n### 智能化管理\n- 🤖 AI驱动的内容分析\n- 🏷️ 自动分类和标签\n- 📊 项目关联分析\n- 💡 个性化推荐\n\n## 📁 项目结构\n\n```\nE:\\Ewandata\\\n├── ewandata_system\\      # 系统核心代码\n├── data\\                 # 数据存储\n├── temp\\                 # 临时文件\n├── logs\\                 # 日志文件\n├── requirements.txt      # 依赖列表\n├── quick_start.bat       # 快速启动脚本\n├── Ewandata实施方案.md   # 详细实施方案\n└── README.md            # 本文件\n```\n\n## 🔧 技术栈\n\n- **AI模型**: Microsoft Phi-3-mini (RTX 4070优化)\n- **后端**: Python + FastAPI\n- **前端**: Streamlit + React\n- **数据库**: ChromaDB + SQLite\n- **文档处理**: LangChain + PyPDF2\n- **监控**: Watchdog + Schedule\n\n## 📖 详细文档\n\n- [📋 完整实施方案](./Ewandata实施方案.md) - 详细的系统设计和实施计划\n- [🛠️ 使用指南](./使用指南.md) - 系统使用方法和最佳实践\n- [🔧 开发文档](./docs/) - 开发者文档和API参考\n\n## 🤝 贡献\n\n欢迎提交Issue和Pull Request来改进系统！\n\n## 📄 许可证\n\n本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件\n\n---\n\n**立即开始**: 运行 `quick_start.bat` 开始您的智能知识管理之旅！\n", "encoding": "utf-8", "line_count": 158, "char_count": 2496, "word_count": 375, "headers": [{"level": 1, "text": "🧠 Ewandata 本地知识管理系统"}, {"level": 2, "text": "🎯 项目概述"}, {"level": 2, "text": "✨ 核心功能"}, {"level": 3, "text": "📚 智能知识库"}, {"level": 3, "text": "📁 项目管理"}, {"level": 3, "text": "🔄 自动化工作流"}, {"level": 2, "text": "🚀 快速开始"}, {"level": 3, "text": "系统要求"}, {"level": 3, "text": "一键安装"}, {"level": 1, "text": "1. 双击运行快速启动脚本"}, {"level": 1, "text": "2. 等待自动安装完成"}, {"level": 1, "text": "系统会自动："}, {"level": 1, "text": "- 检查环境依赖"}, {"level": 1, "text": "- 创建虚拟环境"}, {"level": 1, "text": "- 安装所需包"}, {"level": 1, "text": "- 创建目录结构"}, {"level": 1, "text": "- 配置临时记文件夹"}, {"level": 3, "text": "启动系统"}, {"level": 1, "text": "启动完整系统"}, {"level": 1, "text": "或分别启动各组件"}, {"level": 2, "text": "📋 系统架构"}, {"level": 2, "text": "🎛️ 使用方式"}, {"level": 3, "text": "Web管理界面"}, {"level": 3, "text": "API接口"}, {"level": 3, "text": "自动化工作流"}, {"level": 2, "text": "📊 预期效果"}, {"level": 3, "text": "效率提升"}, {"level": 3, "text": "智能化管理"}, {"level": 2, "text": "📁 项目结构"}, {"level": 2, "text": "🔧 技术栈"}, {"level": 2, "text": "📖 详细文档"}, {"level": 2, "text": "🤝 贡献"}, {"level": 2, "text": "📄 许可证"}], "header_count": 33, "processing_time": "2025-07-04T00:09:15.501660", "processor_version": "1.0.0"}}