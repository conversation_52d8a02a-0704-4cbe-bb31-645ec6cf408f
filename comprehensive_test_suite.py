"""
Ewandata 全面自动化测试和调试系统
专注于GitHub集成和数据库同步问题诊断
"""

import sys
import os
import json
import time
import subprocess
import sqlite3
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# 添加系统路径
sys.path.append('ewandata_system')

class EwandataTestSuite:
    """Ewandata系统全面测试套件"""
    
    def __init__(self, base_path: str = "E:/Ewandata"):
        self.base_path = Path(base_path)
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "base_path": str(self.base_path),
            "tests": {},
            "errors": [],
            "recommendations": []
        }
        
        # 测试配置
        self.github_repo = "https://github.com/EwanCosmos/Ewandata.git"
        self.temp_folder = Path("C:/Users/<USER>/Desktop/临时记")
        
        print(f"🔧 初始化Ewandata测试套件")
        print(f"📁 基础路径: {self.base_path}")
        print(f"🌐 GitHub仓库: {self.github_repo}")
    
    def print_header(self, title: str):
        """打印测试标题"""
        print("\n" + "="*80)
        print(f"  {title}")
        print("="*80)
    
    def print_step(self, step: str, description: str):
        """打印测试步骤"""
        print(f"\n[{step}] {description}")
        print("-" * 60)
    
    def record_test_result(self, test_name: str, success: bool, details: Dict[str, Any], error: str = None):
        """记录测试结果"""
        self.test_results["tests"][test_name] = {
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details,
            "error": error
        }
        
        if error:
            self.test_results["errors"].append({
                "test": test_name,
                "error": error,
                "timestamp": datetime.now().isoformat()
            })
    
    def test_github_integration(self) -> bool:
        """测试GitHub集成功能"""
        self.print_header("🔗 GitHub集成功能测试")
        
        try:
            from services.github_sync import GitHubSync
            
            # 初始化GitHub同步服务
            self.print_step("1", "初始化GitHub同步服务")
            sync_service = GitHubSync(str(self.base_path))
            print("✅ GitHub同步服务初始化成功")
            
            # 检查Git仓库状态
            self.print_step("2", "检查Git仓库状态")
            git_status = sync_service.check_git_status()
            print(f"Git状态: {git_status}")
            
            if not git_status.get('is_git_repo', False):
                print("⚠️ 不是Git仓库，尝试初始化...")
                if sync_service.init_git_repo():
                    print("✅ Git仓库初始化成功")
                    # 添加远程仓库
                    if sync_service.add_remote(self.github_repo):
                        print("✅ 远程仓库配置成功")
                    else:
                        print("❌ 远程仓库配置失败")
                        return False
                else:
                    print("❌ Git仓库初始化失败")
                    return False
            
            # 检查远程连接
            self.print_step("3", "检查远程连接")
            try:
                os.chdir(self.base_path)
                result = subprocess.run(['git', 'remote', '-v'], 
                                      capture_output=True, text=True, check=True)
                print(f"远程仓库配置:\n{result.stdout}")
                
                # 测试连接
                fetch_result = subprocess.run(['git', 'fetch', '--dry-run'], 
                                            capture_output=True, text=True)
                if fetch_result.returncode == 0:
                    print("✅ 远程连接正常")
                else:
                    print(f"⚠️ 远程连接问题: {fetch_result.stderr}")
                    
            except subprocess.CalledProcessError as e:
                print(f"❌ Git命令执行失败: {e}")
                return False
            
            # 获取同步状态
            self.print_step("4", "获取同步状态")
            sync_status = sync_service.get_sync_status()
            print(f"同步状态: {json.dumps(sync_status, indent=2, ensure_ascii=False)}")
            
            # 创建测试备份
            self.print_step("5", "创建测试备份")
            backup_path = sync_service.create_backup()
            if backup_path:
                print(f"✅ 备份创建成功: {backup_path}")
            else:
                print("❌ 备份创建失败")
            
            self.record_test_result("github_integration", True, {
                "git_status": git_status,
                "sync_status": sync_status,
                "backup_path": backup_path
            })
            
            return True
            
        except Exception as e:
            error_msg = f"GitHub集成测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.record_test_result("github_integration", False, {}, error_msg)
            return False
    
    def test_database_sync(self) -> bool:
        """测试数据库同步功能"""
        self.print_header("🗄️ 数据库同步测试")
        
        try:
            from storage.knowledge_base import KnowledgeBase
            from processors.document_processor import DocumentProcessor
            
            # 初始化组件
            self.print_step("1", "初始化知识库和文档处理器")
            kb = KnowledgeBase(str(self.base_path))
            processor = DocumentProcessor()
            print("✅ 组件初始化成功")
            
            # 检查本地数据库状态
            self.print_step("2", "检查本地数据库状态")
            stats = kb.get_statistics()
            print(f"本地数据库统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")
            
            # 检查知识库文件
            self.print_step("3", "检查知识库文件")
            knowledge_base_dir = self.base_path / "data" / "knowledge_base"
            processed_dir = self.base_path / "data" / "processed"
            
            kb_files = list(knowledge_base_dir.glob("*.md")) if knowledge_base_dir.exists() else []
            json_files = list(processed_dir.glob("*.json")) if processed_dir.exists() else []
            
            print(f"Markdown文件数量: {len(kb_files)}")
            print(f"JSON文件数量: {len(json_files)}")
            
            if kb_files:
                print("Markdown文件示例:")
                for file in kb_files[:3]:
                    print(f"  - {file.name}")
            
            if json_files:
                print("JSON文件示例:")
                for file in json_files[:3]:
                    print(f"  - {file.name}")
            
            # 创建测试文档并验证同步
            self.print_step("4", "创建测试文档验证同步")
            test_doc = {
                'title': f'同步测试文档_{int(time.time())}',
                'content': f'''这是一个用于测试GitHub同步功能的文档。
                
创建时间: {datetime.now().isoformat()}
测试目的: 验证本地数据库与GitHub仓库的同步
                
内容包括:
1. 数据库存储测试
2. Markdown文件生成测试  
3. JSON文件生成测试
4. GitHub同步验证
                
系统状态: 正常运行
''',
                'file_path': f'sync_test_{int(time.time())}.md',
                'file_hash': hashlib.md5(f'sync_test_{time.time()}'.encode()).hexdigest(),
                'type': 'markdown',
                'size': 1024,
                'tags': ['同步测试', 'GitHub', '数据库'],
                'keywords': ['同步', '测试', 'GitHub', '数据库'],
                'summary': '用于验证GitHub同步功能的测试文档',
                'importance_score': 0.9
            }
            
            # 存储测试文档
            doc_id = kb.store_document(test_doc)
            print(f"✅ 测试文档存储成功，ID: {doc_id}")
            
            # 验证文件生成
            md_file = knowledge_base_dir / f"{doc_id}.md"
            json_file = processed_dir / f"{doc_id}.json"
            
            md_exists = md_file.exists() if knowledge_base_dir.exists() else False
            json_exists = json_file.exists() if processed_dir.exists() else False
            
            print(f"Markdown文件生成: {'✅' if md_exists else '❌'}")
            print(f"JSON文件生成: {'✅' if json_exists else '❌'}")
            
            # 验证数据库记录
            stored_doc = kb.get_document(doc_id)
            db_verified = stored_doc is not None and stored_doc['title'] == test_doc['title']
            print(f"数据库记录验证: {'✅' if db_verified else '❌'}")
            
            kb.close()
            
            self.record_test_result("database_sync", True, {
                "local_stats": stats,
                "markdown_files": len(kb_files),
                "json_files": len(json_files),
                "test_doc_id": doc_id,
                "markdown_generated": md_exists,
                "json_generated": json_exists,
                "database_verified": db_verified
            })
            
            return True
            
        except Exception as e:
            error_msg = f"数据库同步测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.record_test_result("database_sync", False, {}, error_msg)
            return False

    def test_end_to_end_workflow(self) -> bool:
        """测试端到端工作流"""
        self.print_header("🔄 端到端工作流测试")

        try:
            from processors.document_processor import DocumentProcessor
            from storage.knowledge_base import KnowledgeBase
            from services.github_sync import GitHubSync

            # 初始化所有组件
            self.print_step("1", "初始化所有组件")
            processor = DocumentProcessor()
            kb = KnowledgeBase(str(self.base_path))
            sync_service = GitHubSync(str(self.base_path))
            print("✅ 所有组件初始化成功")

            # 创建测试文档文件
            self.print_step("2", "创建测试文档文件")
            test_file_path = self.base_path / "test_workflow.md"
            test_content = f"""# 端到端工作流测试文档

## 测试信息
- 创建时间: {datetime.now().isoformat()}
- 测试目的: 验证完整的文档处理→存储→同步工作流
- 测试ID: workflow_test_{int(time.time())}

## 测试内容
这是一个用于验证Ewandata系统端到端工作流的测试文档。

### 功能验证
1. **文档处理**: 验证Markdown文档的解析和处理
2. **知识库存储**: 验证文档在本地数据库中的存储
3. **文件生成**: 验证Markdown和JSON文件的生成
4. **GitHub同步**: 验证数据同步到GitHub仓库

### 系统状态
- 处理器状态: 正常
- 知识库状态: 正常
- 同步服务状态: 正常

## 测试结果
此文档将用于验证系统的完整工作流程。
"""

            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(test_content)
            print(f"✅ 测试文件创建成功: {test_file_path}")

            # 步骤1: 文档处理
            self.print_step("3", "文档处理阶段")
            doc_data = processor.process_file(str(test_file_path))

            if "error" in doc_data:
                print(f"❌ 文档处理失败: {doc_data['error']}")
                return False

            print(f"✅ 文档处理成功:")
            print(f"   文件大小: {doc_data.get('size_mb', 0):.3f} MB")
            print(f"   字符数: {doc_data.get('char_count', 0)}")
            print(f"   标题数: {doc_data.get('header_count', 0)}")

            # 步骤2: 知识库存储
            self.print_step("4", "知识库存储阶段")
            doc_id = kb.store_document(doc_data)
            print(f"✅ 文档存储成功，ID: {doc_id}")

            # 验证存储
            stored_doc = kb.get_document(doc_id)
            if not stored_doc:
                print("❌ 文档存储验证失败")
                return False
            print("✅ 文档存储验证成功")

            # 步骤3: 文件生成验证
            self.print_step("5", "文件生成验证")
            knowledge_base_dir = self.base_path / "data" / "knowledge_base"
            processed_dir = self.base_path / "data" / "processed"

            md_file = knowledge_base_dir / f"{doc_id}.md"
            json_file = processed_dir / f"{doc_id}.json"

            md_exists = md_file.exists()
            json_exists = json_file.exists()

            print(f"Markdown文件: {'✅' if md_exists else '❌'} {md_file}")
            print(f"JSON文件: {'✅' if json_exists else '❌'} {json_file}")

            # 步骤4: GitHub同步准备
            self.print_step("6", "GitHub同步准备")
            git_status = sync_service.check_git_status()

            if git_status.get('is_git_repo', False):
                print("✅ Git仓库状态正常")

                # 检查是否有变更
                try:
                    os.chdir(self.base_path)
                    status_result = subprocess.run(['git', 'status', '--porcelain'],
                                                 capture_output=True, text=True, check=True)

                    if status_result.stdout.strip():
                        print("✅ 检测到文件变更，准备同步")
                        print("变更文件:")
                        for line in status_result.stdout.strip().split('\n'):
                            print(f"   {line}")
                    else:
                        print("ℹ️ 没有检测到文件变更")

                except subprocess.CalledProcessError as e:
                    print(f"⚠️ Git状态检查失败: {e}")
            else:
                print("⚠️ 不是Git仓库，跳过同步测试")

            # 清理测试文件
            if test_file_path.exists():
                test_file_path.unlink()
                print("🧹 测试文件已清理")

            kb.close()

            self.record_test_result("end_to_end_workflow", True, {
                "document_processed": True,
                "document_stored": True,
                "doc_id": doc_id,
                "markdown_generated": md_exists,
                "json_generated": json_exists,
                "git_ready": git_status.get('is_git_repo', False)
            })

            return True

        except Exception as e:
            error_msg = f"端到端工作流测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.record_test_result("end_to_end_workflow", False, {}, error_msg)
            return False

    def diagnose_sync_issues(self) -> Dict[str, Any]:
        """诊断同步问题"""
        self.print_header("🔍 同步问题诊断")

        diagnosis = {
            "git_status": {},
            "file_status": {},
            "database_status": {},
            "issues": [],
            "recommendations": []
        }

        try:
            # 检查Git状态
            self.print_step("1", "Git状态诊断")
            os.chdir(self.base_path)

            # 检查是否是Git仓库
            if (self.base_path / ".git").exists():
                diagnosis["git_status"]["is_repo"] = True
                print("✅ 是Git仓库")

                # 检查远程仓库
                try:
                    remote_result = subprocess.run(['git', 'remote', '-v'],
                                                 capture_output=True, text=True, check=True)
                    diagnosis["git_status"]["remotes"] = remote_result.stdout
                    print(f"远程仓库: {remote_result.stdout.strip()}")

                    if "origin" in remote_result.stdout:
                        print("✅ 远程仓库已配置")
                    else:
                        diagnosis["issues"].append("远程仓库未配置")
                        print("❌ 远程仓库未配置")

                except subprocess.CalledProcessError:
                    diagnosis["issues"].append("无法获取远程仓库信息")
                    print("❌ 无法获取远程仓库信息")

                # 检查工作区状态
                try:
                    status_result = subprocess.run(['git', 'status', '--porcelain'],
                                                 capture_output=True, text=True, check=True)
                    diagnosis["git_status"]["working_tree"] = status_result.stdout

                    if status_result.stdout.strip():
                        print(f"⚠️ 工作区有未提交的变更:")
                        for line in status_result.stdout.strip().split('\n'):
                            print(f"   {line}")
                    else:
                        print("✅ 工作区干净")

                except subprocess.CalledProcessError:
                    diagnosis["issues"].append("无法检查工作区状态")
                    print("❌ 无法检查工作区状态")

            else:
                diagnosis["git_status"]["is_repo"] = False
                diagnosis["issues"].append("不是Git仓库")
                print("❌ 不是Git仓库")

            # 检查数据库文件
            self.print_step("2", "数据库文件诊断")
            db_files = [
                self.base_path / "data" / "knowledge_base.db",
                self.base_path / "data" / "projects.db"
            ]

            for db_file in db_files:
                if db_file.exists():
                    size = db_file.stat().st_size
                    diagnosis["database_status"][db_file.name] = {
                        "exists": True,
                        "size": size,
                        "readable": True
                    }
                    print(f"✅ {db_file.name}: {size} bytes")

                    # 尝试连接数据库
                    try:
                        conn = sqlite3.connect(str(db_file))
                        cursor = conn.cursor()
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = cursor.fetchall()
                        diagnosis["database_status"][db_file.name]["tables"] = [t[0] for t in tables]
                        conn.close()
                        print(f"   表: {', '.join([t[0] for t in tables])}")
                    except Exception as e:
                        diagnosis["database_status"][db_file.name]["readable"] = False
                        diagnosis["issues"].append(f"{db_file.name} 无法读取: {str(e)}")
                        print(f"   ❌ 无法读取: {str(e)}")
                else:
                    diagnosis["database_status"][db_file.name] = {"exists": False}
                    diagnosis["issues"].append(f"{db_file.name} 不存在")
                    print(f"❌ {db_file.name} 不存在")

            # 检查知识库文件
            self.print_step("3", "知识库文件诊断")
            data_dirs = [
                ("knowledge_base", self.base_path / "data" / "knowledge_base"),
                ("processed", self.base_path / "data" / "processed"),
                ("vectors", self.base_path / "data" / "vectors")
            ]

            for dir_name, dir_path in data_dirs:
                if dir_path.exists():
                    files = list(dir_path.glob("*"))
                    diagnosis["file_status"][dir_name] = {
                        "exists": True,
                        "file_count": len(files),
                        "files": [f.name for f in files[:5]]  # 只记录前5个
                    }
                    print(f"✅ {dir_name}: {len(files)} 个文件")
                    if files:
                        print(f"   示例: {', '.join([f.name for f in files[:3]])}")
                else:
                    diagnosis["file_status"][dir_name] = {"exists": False}
                    diagnosis["issues"].append(f"{dir_name} 目录不存在")
                    print(f"❌ {dir_name} 目录不存在")

            # 生成建议
            self.print_step("4", "生成修复建议")
            if not diagnosis["git_status"].get("is_repo", False):
                diagnosis["recommendations"].append("初始化Git仓库: git init")
                diagnosis["recommendations"].append(f"添加远程仓库: git remote add origin {self.github_repo}")

            if "远程仓库未配置" in diagnosis["issues"]:
                diagnosis["recommendations"].append(f"配置远程仓库: git remote add origin {self.github_repo}")

            for db_name, db_info in diagnosis["database_status"].items():
                if not db_info.get("exists", False):
                    diagnosis["recommendations"].append(f"重新初始化{db_name}数据库")

            for dir_name, dir_info in diagnosis["file_status"].items():
                if not dir_info.get("exists", False):
                    diagnosis["recommendations"].append(f"创建{dir_name}目录: mkdir -p data/{dir_name}")

            if diagnosis["recommendations"]:
                print("🔧 修复建议:")
                for i, rec in enumerate(diagnosis["recommendations"], 1):
                    print(f"   {i}. {rec}")
            else:
                print("✅ 未发现明显问题")

            return diagnosis

        except Exception as e:
            diagnosis["issues"].append(f"诊断过程出错: {str(e)}")
            print(f"❌ 诊断失败: {str(e)}")
            return diagnosis

    def auto_fix_issues(self, diagnosis: Dict[str, Any]) -> bool:
        """自动修复发现的问题"""
        self.print_header("🔧 自动修复问题")

        fixed_issues = []
        failed_fixes = []

        try:
            # 修复Git仓库问题
            if not diagnosis["git_status"].get("is_repo", False):
                self.print_step("1", "初始化Git仓库")
                try:
                    os.chdir(self.base_path)
                    subprocess.run(['git', 'init'], check=True)
                    print("✅ Git仓库初始化成功")
                    fixed_issues.append("Git仓库初始化")
                except subprocess.CalledProcessError as e:
                    print(f"❌ Git仓库初始化失败: {e}")
                    failed_fixes.append("Git仓库初始化")

            # 配置远程仓库
            if "远程仓库未配置" in diagnosis["issues"]:
                self.print_step("2", "配置远程仓库")
                try:
                    subprocess.run(['git', 'remote', 'add', 'origin', self.github_repo], check=True)
                    print("✅ 远程仓库配置成功")
                    fixed_issues.append("远程仓库配置")
                except subprocess.CalledProcessError as e:
                    print(f"❌ 远程仓库配置失败: {e}")
                    failed_fixes.append("远程仓库配置")

            # 创建缺失的目录
            self.print_step("3", "创建缺失的目录")
            required_dirs = [
                self.base_path / "data",
                self.base_path / "data" / "knowledge_base",
                self.base_path / "data" / "processed",
                self.base_path / "data" / "vectors",
                self.base_path / "temp",
                self.base_path / "logs"
            ]

            for dir_path in required_dirs:
                if not dir_path.exists():
                    try:
                        dir_path.mkdir(parents=True, exist_ok=True)
                        print(f"✅ 创建目录: {dir_path}")
                        fixed_issues.append(f"创建目录 {dir_path.name}")
                    except Exception as e:
                        print(f"❌ 创建目录失败 {dir_path}: {e}")
                        failed_fixes.append(f"创建目录 {dir_path.name}")

            # 初始化数据库
            self.print_step("4", "初始化数据库")
            try:
                from storage.knowledge_base import KnowledgeBase
                from services.project_tracker import ProjectTracker

                # 初始化知识库数据库
                kb = KnowledgeBase(str(self.base_path))
                kb.close()
                print("✅ 知识库数据库初始化成功")
                fixed_issues.append("知识库数据库初始化")

                # 初始化项目跟踪数据库
                tracker = ProjectTracker(str(self.base_path))
                print("✅ 项目跟踪数据库初始化成功")
                fixed_issues.append("项目跟踪数据库初始化")

            except Exception as e:
                print(f"❌ 数据库初始化失败: {e}")
                failed_fixes.append("数据库初始化")

            # 创建.gitignore文件
            self.print_step("5", "创建.gitignore文件")
            gitignore_path = self.base_path / ".gitignore"
            if not gitignore_path.exists():
                gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/

# Database backups
*.db.backup
*.db.bak

# Large model files
models/*.bin
models/*.safetensors
"""
                try:
                    with open(gitignore_path, 'w', encoding='utf-8') as f:
                        f.write(gitignore_content)
                    print("✅ .gitignore文件创建成功")
                    fixed_issues.append(".gitignore文件创建")
                except Exception as e:
                    print(f"❌ .gitignore文件创建失败: {e}")
                    failed_fixes.append(".gitignore文件创建")

            # 显示修复结果
            self.print_step("6", "修复结果汇总")
            print(f"✅ 成功修复: {len(fixed_issues)} 个问题")
            for issue in fixed_issues:
                print(f"   - {issue}")

            if failed_fixes:
                print(f"❌ 修复失败: {len(failed_fixes)} 个问题")
                for issue in failed_fixes:
                    print(f"   - {issue}")

            return len(failed_fixes) == 0

        except Exception as e:
            print(f"❌ 自动修复过程失败: {str(e)}")
            return False

    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行全面测试"""
        self.print_header("🚀 Ewandata 全面自动化测试开始")

        print("本测试将验证以下功能:")
        print("1. GitHub集成和同步功能")
        print("2. 数据库同步和一致性")
        print("3. 端到端工作流")
        print("4. 系统问题诊断和自动修复")

        # 测试列表
        tests = [
            ("GitHub集成功能", self.test_github_integration),
            ("数据库同步功能", self.test_database_sync),
            ("端到端工作流", self.test_end_to_end_workflow)
        ]

        # 运行测试
        for test_name, test_func in tests:
            print(f"\n⏳ 开始测试: {test_name}")
            time.sleep(1)

            try:
                success = test_func()
                status = "✅ 通过" if success else "❌ 失败"
                print(f"📊 {test_name}: {status}")
            except Exception as e:
                print(f"❌ {test_name}: 异常 - {str(e)}")
                self.record_test_result(test_name.replace(" ", "_").lower(), False, {}, str(e))

        # 运行诊断
        print(f"\n⏳ 开始系统诊断...")
        diagnosis = self.diagnose_sync_issues()

        # 如果发现问题，尝试自动修复
        if diagnosis["issues"]:
            print(f"\n⏳ 发现 {len(diagnosis['issues'])} 个问题，尝试自动修复...")
            auto_fix_success = self.auto_fix_issues(diagnosis)

            if auto_fix_success:
                print("✅ 自动修复完成，重新运行诊断...")
                diagnosis = self.diagnose_sync_issues()

        # 生成最终报告
        self.generate_final_report(diagnosis)

        return self.test_results

    def generate_final_report(self, diagnosis: Dict[str, Any]):
        """生成最终测试报告"""
        self.print_header("📋 最终测试报告")

        # 测试结果汇总
        total_tests = len(self.test_results["tests"])
        passed_tests = sum(1 for test in self.test_results["tests"].values() if test["success"])

        print(f"📊 测试汇总:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "   成功率: 0%")

        # 详细测试结果
        print(f"\n📝 详细结果:")
        for test_name, result in self.test_results["tests"].items():
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {test_name}")
            if result["error"]:
                print(f"      错误: {result['error']}")

        # 系统状态
        print(f"\n🔍 系统状态:")
        print(f"   Git仓库: {'✅' if diagnosis['git_status'].get('is_repo', False) else '❌'}")
        print(f"   数据库: {'✅' if any(db.get('exists', False) for db in diagnosis['database_status'].values()) else '❌'}")
        print(f"   文件系统: {'✅' if any(dir_info.get('exists', False) for dir_info in diagnosis['file_status'].values()) else '❌'}")

        # 问题和建议
        if diagnosis["issues"]:
            print(f"\n⚠️ 发现的问题:")
            for i, issue in enumerate(diagnosis["issues"], 1):
                print(f"   {i}. {issue}")

        if diagnosis["recommendations"]:
            print(f"\n🔧 建议:")
            for i, rec in enumerate(diagnosis["recommendations"], 1):
                print(f"   {i}. {rec}")

        # 保存报告
        report_file = self.base_path / "test_report.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "test_results": self.test_results,
                    "diagnosis": diagnosis
                }, f, ensure_ascii=False, indent=2)
            print(f"\n💾 详细报告已保存到: {report_file}")
        except Exception as e:
            print(f"\n❌ 报告保存失败: {e}")

        # 最终状态
        if passed_tests == total_tests and not diagnosis["issues"]:
            print(f"\n🎉 恭喜！所有测试通过，系统状态良好！")
            print(f"   GitHub集成功能正常，可以进行数据同步")
        elif passed_tests > total_tests // 2:
            print(f"\n✅ 大部分功能正常，系统基本可用")
            print(f"   建议解决剩余问题以获得最佳体验")
        else:
            print(f"\n⚠️ 发现多个问题，建议进行系统检查和修复")
            print(f"   请参考上述建议进行修复")


def main():
    """主函数"""
    print("🔧 Ewandata 全面自动化测试和调试系统")
    print("=" * 60)

    # 创建测试套件
    test_suite = EwandataTestSuite()

    # 运行全面测试
    results = test_suite.run_comprehensive_test()

    print("\n" + "=" * 60)
    print("测试完成！请查看上述报告了解详细结果。")


if __name__ == "__main__":
    main()
