"""
Ewandata 全面自动化测试和调试系统
专注于GitHub集成和数据库同步问题诊断
"""

import sys
import os
import json
import time
import subprocess
import sqlite3
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# 添加系统路径
sys.path.append('ewandata_system')

class EwandataTestSuite:
    """Ewandata系统全面测试套件"""
    
    def __init__(self, base_path: str = "E:/Ewandata"):
        self.base_path = Path(base_path)
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "base_path": str(self.base_path),
            "tests": {},
            "errors": [],
            "recommendations": []
        }
        
        # 测试配置
        self.github_repo = "https://github.com/EwanCosmos/Ewandata.git"
        self.temp_folder = Path("C:/Users/<USER>/Desktop/临时记")
        
        print(f"🔧 初始化Ewandata测试套件")
        print(f"📁 基础路径: {self.base_path}")
        print(f"🌐 GitHub仓库: {self.github_repo}")
    
    def print_header(self, title: str):
        """打印测试标题"""
        print("\n" + "="*80)
        print(f"  {title}")
        print("="*80)
    
    def print_step(self, step: str, description: str):
        """打印测试步骤"""
        print(f"\n[{step}] {description}")
        print("-" * 60)
    
    def record_test_result(self, test_name: str, success: bool, details: Dict[str, Any], error: str = None):
        """记录测试结果"""
        self.test_results["tests"][test_name] = {
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details,
            "error": error
        }
        
        if error:
            self.test_results["errors"].append({
                "test": test_name,
                "error": error,
                "timestamp": datetime.now().isoformat()
            })
    
    def test_github_integration(self) -> bool:
        """测试GitHub集成功能"""
        self.print_header("🔗 GitHub集成功能测试")
        
        try:
            from services.github_sync import GitHubSync
            
            # 初始化GitHub同步服务
            self.print_step("1", "初始化GitHub同步服务")
            sync_service = GitHubSync(str(self.base_path))
            print("✅ GitHub同步服务初始化成功")
            
            # 检查Git仓库状态
            self.print_step("2", "检查Git仓库状态")
            git_status = sync_service.check_git_status()
            print(f"Git状态: {git_status}")
            
            if not git_status.get('is_git_repo', False):
                print("⚠️ 不是Git仓库，尝试初始化...")
                if sync_service.init_git_repo():
                    print("✅ Git仓库初始化成功")
                    # 添加远程仓库
                    if sync_service.add_remote(self.github_repo):
                        print("✅ 远程仓库配置成功")
                    else:
                        print("❌ 远程仓库配置失败")
                        return False
                else:
                    print("❌ Git仓库初始化失败")
                    return False
            
            # 检查远程连接
            self.print_step("3", "检查远程连接")
            try:
                os.chdir(self.base_path)
                result = subprocess.run(['git', 'remote', '-v'], 
                                      capture_output=True, text=True, check=True)
                print(f"远程仓库配置:\n{result.stdout}")
                
                # 测试连接
                fetch_result = subprocess.run(['git', 'fetch', '--dry-run'], 
                                            capture_output=True, text=True)
                if fetch_result.returncode == 0:
                    print("✅ 远程连接正常")
                else:
                    print(f"⚠️ 远程连接问题: {fetch_result.stderr}")
                    
            except subprocess.CalledProcessError as e:
                print(f"❌ Git命令执行失败: {e}")
                return False
            
            # 获取同步状态
            self.print_step("4", "获取同步状态")
            sync_status = sync_service.get_sync_status()
            print(f"同步状态: {json.dumps(sync_status, indent=2, ensure_ascii=False)}")
            
            # 创建测试备份
            self.print_step("5", "创建测试备份")
            backup_path = sync_service.create_backup()
            if backup_path:
                print(f"✅ 备份创建成功: {backup_path}")
            else:
                print("❌ 备份创建失败")
            
            self.record_test_result("github_integration", True, {
                "git_status": git_status,
                "sync_status": sync_status,
                "backup_path": backup_path
            })
            
            return True
            
        except Exception as e:
            error_msg = f"GitHub集成测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.record_test_result("github_integration", False, {}, error_msg)
            return False
    
    def test_database_sync(self) -> bool:
        """测试数据库同步功能"""
        self.print_header("🗄️ 数据库同步测试")
        
        try:
            from storage.knowledge_base import KnowledgeBase
            from processors.document_processor import DocumentProcessor
            
            # 初始化组件
            self.print_step("1", "初始化知识库和文档处理器")
            kb = KnowledgeBase(str(self.base_path))
            processor = DocumentProcessor()
            print("✅ 组件初始化成功")
            
            # 检查本地数据库状态
            self.print_step("2", "检查本地数据库状态")
            stats = kb.get_statistics()
            print(f"本地数据库统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")
            
            # 检查知识库文件
            self.print_step("3", "检查知识库文件")
            knowledge_base_dir = self.base_path / "data" / "knowledge_base"
            processed_dir = self.base_path / "data" / "processed"
            
            kb_files = list(knowledge_base_dir.glob("*.md")) if knowledge_base_dir.exists() else []
            json_files = list(processed_dir.glob("*.json")) if processed_dir.exists() else []
            
            print(f"Markdown文件数量: {len(kb_files)}")
            print(f"JSON文件数量: {len(json_files)}")
            
            if kb_files:
                print("Markdown文件示例:")
                for file in kb_files[:3]:
                    print(f"  - {file.name}")
            
            if json_files:
                print("JSON文件示例:")
                for file in json_files[:3]:
                    print(f"  - {file.name}")
            
            # 创建测试文档并验证同步
            self.print_step("4", "创建测试文档验证同步")
            test_doc = {
                'title': f'同步测试文档_{int(time.time())}',
                'content': f'''这是一个用于测试GitHub同步功能的文档。
                
创建时间: {datetime.now().isoformat()}
测试目的: 验证本地数据库与GitHub仓库的同步
                
内容包括:
1. 数据库存储测试
2. Markdown文件生成测试  
3. JSON文件生成测试
4. GitHub同步验证
                
系统状态: 正常运行
''',
                'file_path': f'sync_test_{int(time.time())}.md',
                'file_hash': hashlib.md5(f'sync_test_{time.time()}'.encode()).hexdigest(),
                'type': 'markdown',
                'size': 1024,
                'tags': ['同步测试', 'GitHub', '数据库'],
                'keywords': ['同步', '测试', 'GitHub', '数据库'],
                'summary': '用于验证GitHub同步功能的测试文档',
                'importance_score': 0.9
            }
            
            # 存储测试文档
            doc_id = kb.store_document(test_doc)
            print(f"✅ 测试文档存储成功，ID: {doc_id}")
            
            # 验证文件生成
            md_file = knowledge_base_dir / f"{doc_id}.md"
            json_file = processed_dir / f"{doc_id}.json"
            
            md_exists = md_file.exists() if knowledge_base_dir.exists() else False
            json_exists = json_file.exists() if processed_dir.exists() else False
            
            print(f"Markdown文件生成: {'✅' if md_exists else '❌'}")
            print(f"JSON文件生成: {'✅' if json_exists else '❌'}")
            
            # 验证数据库记录
            stored_doc = kb.get_document(doc_id)
            db_verified = stored_doc is not None and stored_doc['title'] == test_doc['title']
            print(f"数据库记录验证: {'✅' if db_verified else '❌'}")
            
            kb.close()
            
            self.record_test_result("database_sync", True, {
                "local_stats": stats,
                "markdown_files": len(kb_files),
                "json_files": len(json_files),
                "test_doc_id": doc_id,
                "markdown_generated": md_exists,
                "json_generated": json_exists,
                "database_verified": db_verified
            })
            
            return True
            
        except Exception as e:
            error_msg = f"数据库同步测试失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.record_test_result("database_sync", False, {}, error_msg)
            return False
