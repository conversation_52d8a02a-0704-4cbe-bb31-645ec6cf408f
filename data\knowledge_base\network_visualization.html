<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ewandata文档关系网络图</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 16px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .control-btn:hover, .control-btn.active {
            background: #3498db;
            color: white;
        }

        .network-container {
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            background: white;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            max-width: 300px;
        }

        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ddd;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ddd;
            max-width: 250px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Ewandata文档关系网络图</h1>
            <p>交互式文档关系可视化 - 点击节点查看详情，拖拽移动</p>
        </div>

        <div class="controls">
            <button class="control-btn active" onclick="filterByCategory('all')">全部</button>
            <button class="control-btn" onclick="filterByCategory('技术文档')">技术文档</button>
            <button class="control-btn" onclick="filterByCategory('项目管理')">项目管理</button>
            <button class="control-btn" onclick="filterByCategory('个人资料')">个人资料</button>
            <button class="control-btn" onclick="filterByImportance(7)">高重要性</button>
            <button class="control-btn" onclick="resetZoom()">重置视图</button>
        </div>

        <div class="network-container">
            <svg id="network" width="100%" height="600"></svg>
        </div>

        <div class="tooltip" id="tooltip"></div>

        <div class="legend">
            <h4>图例</h4>
            <div class="legend-item">
                <div class="legend-color" style="background: #3498db;"></div>
                <span>技术文档</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #e74c3c;"></div>
                <span>项目管理</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #2ecc71;"></div>
                <span>学习笔记</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #f39c12;"></div>
                <span>商业文档</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #9b59b6;"></div>
                <span>个人资料</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #95a5a6;"></div>
                <span>其他</span>
            </div>
        </div>

        <div class="info-panel" id="infoPanel">
            <h4>网络统计</h4>
            <p>节点数: <span id="nodeCount">23</span></p>
            <p>连接数: <span id="linkCount">55</span></p>
            <p>点击节点查看详细信息</p>
        </div>
    </div>

    <script>
        // 数据
        const nodes = [{"id": 0, "label": "29条工程提示词简介及案例.docx", "size": 10, "color": "#95a5a6", "importance": 1, "category": "其他", "topics": [], "doc_id": "29条工程提示词简介及案例_processed", "full_name": "29条工程提示词简介及案例.docx"}, {"id": 1, "label": "ai自动化记忆.docx", "size": 10, "color": "#95a5a6", "importance": 1, "category": "其他", "topics": ["人工智能"], "doc_id": "ai自动化记忆_processed", "full_name": "ai自动化记忆.docx"}, {"id": 2, "label": "blogger.txt", "size": 30, "color": "#3498db", "importance": 10, "category": "技术文档", "topics": ["编程", "人工智能"], "doc_id": "blogger_processed", "full_name": "blogger.txt"}, {"id": 3, "label": "brain记忆文档.docx", "size": 30, "color": "#3498db", "importance": 10, "category": "技术文档", "topics": ["编程", "人工智能"], "doc_id": "brain记忆文档_improved_processed", "full_name": "brain记忆文档.docx"}, {"id": 4, "label": "data流程cursor生成.txt", "size": 12, "color": "#3498db", "importance": 4, "category": "技术文档", "topics": ["编程"], "doc_id": "data流程cursor生成_processed", "full_name": "data流程cursor生成.txt"}, {"id": 5, "label": "Ewandata项目需求英文描述.txt", "size": 30, "color": "#3498db", "importance": 10, "category": "技术文档", "topics": ["编程", "人工智能"], "doc_id": "Ewandata项目需求英文描述_processed", "full_name": "Ewandata项目需求英文描述.txt"}, {"id": 6, "label": "ima网址信息.txt", "size": 10, "color": "#95a5a6", "importance": 1, "category": "其他", "topics": [], "doc_id": "ima网址信息_processed", "full_name": "ima网址信息.txt"}, {"id": 7, "label": "logo设计.txt", "size": 10, "color": "#95a5a6", "importance": 1, "category": "其他", "topics": ["人工智能"], "doc_id": "logo设计_processed", "full_name": "logo设计.txt"}, {"id": 8, "label": "qwen cli.txt", "size": 30, "color": "#3498db", "importance": 10, "category": "技术文档", "topics": ["编程", "人工智能"], "doc_id": "qwen cli_processed", "full_name": "qwen cli.txt"}, {"id": 9, "label": "type Engligh改成人工智能学习工具.txt", "size": 10, "color": "#2ecc71", "importance": 2, "category": "学习笔记", "topics": ["学习", "人工智能"], "doc_id": "type Engligh改成人工智能学习工具_processed", "full_name": "type Engligh改成人工智能学习工具.txt"}, {"id": 10, "label": "个人简介.docx", "size": 30, "color": "#3498db", "importance": 10, "category": "技术文档", "topics": ["编程", "人工智能"], "doc_id": "个人简介_improved_processed", "full_name": "个人简介.docx"}, {"id": 11, "label": "代码示例.py", "size": 30, "color": "#3498db", "importance": 10, "category": "技术文档", "topics": ["编程", "人工智能"], "doc_id": "代码示例_processed", "full_name": "代码示例.py"}, {"id": 12, "label": "以太坊信息.docx", "size": 30, "color": "#95a5a6", "importance": 10, "category": "其他", "topics": ["人工智能"], "doc_id": "以太坊信息_improved_processed", "full_name": "以太坊信息.docx"}, {"id": 13, "label": "你猜我做.txt", "size": 12, "color": "#e74c3c", "importance": 4, "category": "项目管理", "topics": ["管理"], "doc_id": "你猜我做_processed", "full_name": "你猜我做.txt"}, {"id": 14, "label": "区块链大模型构想.docx", "size": 10, "color": "#95a5a6", "importance": 1, "category": "其他", "topics": [], "doc_id": "区块链大模型构想_processed", "full_name": "区块链大模型构想.docx"}, {"id": 15, "label": "博客.txt", "size": 10, "color": "#95a5a6", "importance": 1, "category": "其他", "topics": [], "doc_id": "博客_processed", "full_name": "博客.txt"}, {"id": 16, "label": "厨房agent.txt", "size": 21, "color": "#e74c3c", "importance": 7, "category": "项目管理", "topics": ["管理"], "doc_id": "厨房agent_processed", "full_name": "厨房agent.txt"}, {"id": 17, "label": "厨房agent有啥吃啥计划.docx", "size": 21, "color": "#e74c3c", "importance": 7, "category": "项目管理", "topics": ["管理"], "doc_id": "厨房agent有啥吃啥计划_improved_processed", "full_name": "厨房agent有啥吃啥计划.docx"}, {"id": 18, "label": "测试文档1.txt", "size": 18, "color": "#e74c3c", "importance": 6, "category": "项目管理", "topics": ["管理", "人工智能"], "doc_id": "测试文档1_processed", "full_name": "测试文档1.txt"}, {"id": 19, "label": "短视频ai新闻prompt完美版.docx", "size": 30, "color": "#e74c3c", "importance": 10, "category": "项目管理", "topics": ["管理", "人工智能"], "doc_id": "短视频ai新闻prompt完美版_improved_processed", "full_name": "短视频ai新闻prompt完美版.docx"}, {"id": 20, "label": "进销存记忆.docx", "size": 10, "color": "#95a5a6", "importance": 1, "category": "其他", "topics": [], "doc_id": "进销存记忆_processed", "full_name": "进销存记忆.docx"}, {"id": 21, "label": "项目笔记.md", "size": 27, "color": "#3498db", "importance": 9, "category": "技术文档", "topics": ["编程", "人工智能"], "doc_id": "项目笔记_processed", "full_name": "项目笔记.md"}, {"id": 22, "label": "项目结构树.docx", "size": 10, "color": "#e74c3c", "importance": 1, "category": "项目管理", "topics": ["管理"], "doc_id": "项目结构树_processed", "full_name": "项目结构树.docx"}];
        const links = [{"source": 0, "target": 1, "weight": 0.5142857142857142, "width": 2.571428571428571}, {"source": 0, "target": 12, "weight": 0.32, "width": 1.6}, {"source": 0, "target": 14, "weight": 0.5142857142857142, "width": 2.571428571428571}, {"source": 0, "target": 20, "weight": 0.5142857142857142, "width": 2.571428571428571}, {"source": 1, "target": 7, "weight": 0.7, "width": 3.5}, {"source": 1, "target": 12, "weight": 0.72, "width": 3.5999999999999996}, {"source": 1, "target": 14, "weight": 0.5142857142857142, "width": 2.571428571428571}, {"source": 1, "target": 20, "weight": 0.5142857142857142, "width": 2.571428571428571}, {"source": 2, "target": 3, "weight": 0.7, "width": 3.5}, {"source": 2, "target": 4, "weight": 0.5, "width": 2.5}, {"source": 2, "target": 5, "weight": 0.7, "width": 3.5}, {"source": 2, "target": 8, "weight": 0.7157894736842105, "width": 3.5789473684210527}, {"source": 2, "target": 10, "weight": 0.7, "width": 3.5}, {"source": 2, "target": 11, "weight": 0.7157894736842105, "width": 3.5789473684210527}, {"source": 2, "target": 21, "weight": 0.7, "width": 3.5}, {"source": 3, "target": 4, "weight": 0.5, "width": 2.5}, {"source": 3, "target": 5, "weight": 0.7, "width": 3.5}, {"source": 3, "target": 8, "weight": 0.7, "width": 3.5}, {"source": 3, "target": 10, "weight": 0.7, "width": 3.5}, {"source": 3, "target": 11, "weight": 0.7, "width": 3.5}, {"source": 3, "target": 21, "weight": 0.7, "width": 3.5}, {"source": 4, "target": 5, "weight": 0.5, "width": 2.5}, {"source": 4, "target": 8, "weight": 0.5157894736842106, "width": 2.578947368421053}, {"source": 4, "target": 10, "weight": 0.5, "width": 2.5}, {"source": 4, "target": 11, "weight": 0.5, "width": 2.5}, {"source": 4, "target": 21, "weight": 0.5, "width": 2.5}, {"source": 5, "target": 8, "weight": 0.7, "width": 3.5}, {"source": 5, "target": 10, "weight": 0.7, "width": 3.5}, {"source": 5, "target": 11, "weight": 0.7, "width": 3.5}, {"source": 5, "target": 21, "weight": 0.7157894736842105, "width": 3.5789473684210527}, {"source": 7, "target": 12, "weight": 0.7, "width": 3.5}, {"source": 8, "target": 10, "weight": 0.7, "width": 3.5}, {"source": 8, "target": 11, "weight": 0.7333333333333333, "width": 3.6666666666666665}, {"source": 8, "target": 21, "weight": 0.7, "width": 3.5}, {"source": 10, "target": 11, "weight": 0.7, "width": 3.5}, {"source": 10, "target": 21, "weight": 0.7, "width": 3.5}, {"source": 11, "target": 21, "weight": 0.7, "width": 3.5}, {"source": 12, "target": 14, "weight": 0.32, "width": 1.6}, {"source": 12, "target": 20, "weight": 0.32, "width": 1.6}, {"source": 13, "target": 16, "weight": 0.7, "width": 3.5}, {"source": 13, "target": 17, "weight": 0.7, "width": 3.5}, {"source": 13, "target": 18, "weight": 0.5, "width": 2.5}, {"source": 13, "target": 19, "weight": 0.5, "width": 2.5}, {"source": 13, "target": 22, "weight": 0.7, "width": 3.5}, {"source": 14, "target": 20, "weight": 0.5142857142857142, "width": 2.571428571428571}, {"source": 16, "target": 17, "weight": 0.8615384615384615, "width": 4.3076923076923075}, {"source": 16, "target": 18, "weight": 0.5, "width": 2.5}, {"source": 16, "target": 19, "weight": 0.5, "width": 2.5}, {"source": 16, "target": 22, "weight": 0.7, "width": 3.5}, {"source": 17, "target": 18, "weight": 0.5, "width": 2.5}, {"source": 17, "target": 19, "weight": 0.5, "width": 2.5}, {"source": 17, "target": 22, "weight": 0.72, "width": 3.5999999999999996}, {"source": 18, "target": 19, "weight": 0.7, "width": 3.5}, {"source": 18, "target": 22, "weight": 0.5, "width": 2.5}, {"source": 19, "target": 22, "weight": 0.5, "width": 2.5}];

        // SVG设置
        const svg = d3.select("#network");
        const width = 1200;
        const height = 600;
        svg.attr("width", width).attr("height", height);

        // 创建缩放行为
        const zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on("zoom", (event) => {
                g.attr("transform", event.transform);
            });

        svg.call(zoom);

        // 创建主容器
        const g = svg.append("g");

        // 创建力导向图
        const simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links).id(d => d.id).distance(100))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(d => d.size + 5));

        // 创建连接线
        const link = g.append("g")
            .selectAll("line")
            .data(links)
            .enter().append("line")
            .attr("stroke", "#999")
            .attr("stroke-opacity", 0.6)
            .attr("stroke-width", d => d.width);

        // 创建节点
        const node = g.append("g")
            .selectAll("circle")
            .data(nodes)
            .enter().append("circle")
            .attr("r", d => d.size)
            .attr("fill", d => d.color)
            .attr("stroke", "#fff")
            .attr("stroke-width", 2)
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended))
            .on("click", showNodeInfo)
            .on("mouseover", showTooltip)
            .on("mouseout", hideTooltip);

        // 创建标签
        const label = g.append("g")
            .selectAll("text")
            .data(nodes)
            .enter().append("text")
            .text(d => d.label)
            .attr("font-size", "10px")
            .attr("text-anchor", "middle")
            .attr("dy", ".35em")
            .style("pointer-events", "none");

        // 更新位置
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);

            label
                .attr("x", d => d.x)
                .attr("y", d => d.y);
        });

        // 拖拽函数
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // 显示节点信息
        function showNodeInfo(event, d) {
            const infoPanel = document.getElementById('infoPanel');
            infoPanel.innerHTML = `
                <h4>文档详情</h4>
                <p><strong>名称:</strong> ${d.full_name}</p>
                <p><strong>重要性:</strong> ${d.importance}/10</p>
                <p><strong>分类:</strong> ${d.category}</p>
                <p><strong>主题:</strong> ${d.topics.join(', ') || '无'}</p>
                <p><strong>连接数:</strong> ${links.filter(l => l.source.id === d.id || l.target.id === d.id).length}</p>
            `;
        }

        // 显示提示框
        function showTooltip(event, d) {
            const tooltip = document.getElementById('tooltip');
            tooltip.style.opacity = 1;
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY - 10) + 'px';
            tooltip.innerHTML = `
                <strong>${d.full_name}</strong><br/>
                重要性: ${d.importance}/10<br/>
                分类: ${d.category}<br/>
                主题: ${d.topics.join(', ') || '无'}
            `;
        }

        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = 0;
        }

        // 过滤函数
        function filterByCategory(category) {
            // 更新按钮状态
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (category === 'all') {
                node.style('opacity', 1);
                label.style('opacity', 1);
                link.style('opacity', 0.6);
            } else {
                node.style('opacity', d => d.category === category ? 1 : 0.2);
                label.style('opacity', d => d.category === category ? 1 : 0.2);
                link.style('opacity', d =>
                    (d.source.category === category || d.target.category === category) ? 0.6 : 0.1
                );
            }
        }

        function filterByImportance(minImportance) {
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            node.style('opacity', d => d.importance >= minImportance ? 1 : 0.2);
            label.style('opacity', d => d.importance >= minImportance ? 1 : 0.2);
            link.style('opacity', d =>
                (d.source.importance >= minImportance || d.target.importance >= minImportance) ? 0.6 : 0.1
            );
        }

        function resetZoom() {
            svg.transition().duration(750).call(
                zoom.transform,
                d3.zoomIdentity
            );
        }
    </script>
</body>
</html>