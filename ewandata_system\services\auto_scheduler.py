"""
自动化调度器
整合多渠道信息采集、AI分析和知识库存储
"""

import asyncio
import logging
import schedule
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
import json

logger = logging.getLogger(__name__)

class AutoScheduler:
    """自动化调度器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化自动调度器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.is_running = False
        self.last_run_time = None
        
        # 初始化各个服务
        self.collector = None
        self.analyzer = None
        self.knowledge_base = None
        
        self._initialize_services()
        
        logger.info("自动化调度器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            "schedule": {
                "daily_collection_time": "09:00",  # 每日采集时间
                "collection_interval_hours": 6,    # 采集间隔（小时）
                "analysis_delay_minutes": 5,       # 分析延迟（分钟）
                "max_items_per_run": 100           # 每次最大处理条目
            },
            "storage": {
                "auto_save": True,
                "backup_enabled": True,
                "retention_days": 30
            },
            "notifications": {
                "enabled": True,
                "high_value_threshold": 8.0,
                "trend_alert_threshold": 7.0
            }
        }
        
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"配置加载失败，使用默认配置: {e}")
        
        return default_config
    
    def _initialize_services(self):
        """初始化各个服务"""
        try:
            # 初始化信息采集器
            from .multi_channel_collector import MultiChannelCollector
            self.collector = MultiChannelCollector()
            logger.info("✅ 信息采集器初始化成功")
            
            # 初始化智能分析器
            from .intelligent_analyzer import IntelligentAnalyzer
            self.analyzer = IntelligentAnalyzer()
            logger.info("✅ 智能分析器初始化成功")
            
            # 初始化知识库
            from ..storage.knowledge_base import KnowledgeBase
            self.knowledge_base = KnowledgeBase("E:/Ewandata")
            logger.info("✅ 知识库初始化成功")
            
        except Exception as e:
            logger.error(f"服务初始化失败: {e}")
    
    async def run_daily_collection(self):
        """执行每日信息采集和分析"""
        logger.info("🚀 开始每日信息采集和分析...")
        
        try:
            start_time = datetime.now()
            
            # 1. 信息采集
            logger.info("📡 步骤1: 多渠道信息采集")
            collected_items = await self.collector.collect_all_channels()
            
            if not collected_items:
                logger.warning("未采集到任何信息")
                return
            
            # 限制处理数量
            max_items = self.config['schedule']['max_items_per_run']
            if len(collected_items) > max_items:
                collected_items = collected_items[:max_items]
                logger.info(f"限制处理数量为 {max_items} 条")
            
            # 2. 智能分析
            logger.info("🧠 步骤2: AI智能分析")
            analyzed_items = await self.analyzer.analyze_information_batch(collected_items)
            
            # 3. 趋势分析
            logger.info("📊 步骤3: 趋势分析")
            trend_analysis = self.analyzer.analyze_trends(analyzed_items)
            
            # 4. 存储到知识库
            logger.info("💾 步骤4: 存储到知识库")
            stored_count = await self._store_to_knowledge_base(analyzed_items)
            
            # 5. 保存分析结果
            if self.config['storage']['auto_save']:
                self.analyzer.save_analysis_results(analyzed_items, trend_analysis)
            
            # 6. 生成通知
            if self.config['notifications']['enabled']:
                await self._generate_notifications(analyzed_items, trend_analysis)
            
            # 记录运行信息
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            run_summary = {
                "timestamp": start_time.isoformat(),
                "duration_seconds": duration,
                "collected_items": len(collected_items),
                "analyzed_items": len(analyzed_items),
                "stored_items": stored_count,
                "average_value_score": trend_analysis.get('average_value_score', 0),
                "high_value_items": trend_analysis.get('high_value_items', 0)
            }
            
            self._save_run_summary(run_summary)
            self.last_run_time = start_time
            
            logger.info(f"✅ 每日采集分析完成!")
            logger.info(f"   采集: {len(collected_items)} 条")
            logger.info(f"   分析: {len(analyzed_items)} 条")
            logger.info(f"   存储: {stored_count} 条")
            logger.info(f"   耗时: {duration:.1f} 秒")
            logger.info(f"   平均价值: {trend_analysis.get('average_value_score', 0):.1f}/10")
            
        except Exception as e:
            logger.error(f"❌ 每日采集分析失败: {e}")
    
    async def _store_to_knowledge_base(self, analyzed_items: List[Dict[str, Any]]) -> int:
        """存储分析结果到知识库"""
        stored_count = 0
        
        for item in analyzed_items:
            try:
                # 构建文档数据
                doc_data = {
                    "title": item.get('title', ''),
                    "content": self._build_content(item),
                    "source": item.get('source', ''),
                    "url": item.get('link', ''),
                    "keywords": item.get('ai_keywords', []),
                    "summary": item.get('ai_summary', ''),
                    "classification": item.get('ai_classification', {}),
                    "value_prediction": item.get('value_prediction', {}),
                    "sentiment_analysis": item.get('sentiment_analysis', {}),
                    "named_entities": item.get('named_entities', []),
                    "collected_time": item.get('collected_time', ''),
                    "analysis_time": item.get('analysis_timestamp', ''),
                    "content_type": item.get('content_type', 'news'),
                    "ai_enhanced": True
                }
                
                # 存储到知识库
                doc_id = self.knowledge_base.store_document(doc_data)
                if doc_id:
                    stored_count += 1
                
            except Exception as e:
                logger.error(f"存储失败 {item.get('title', '')}: {e}")
        
        return stored_count
    
    def _build_content(self, item: Dict[str, Any]) -> str:
        """构建文档内容"""
        content_parts = []
        
        if item.get('title'):
            content_parts.append(f"标题: {item['title']}")
        
        if item.get('summary'):
            content_parts.append(f"摘要: {item['summary']}")
        
        if item.get('ai_summary'):
            content_parts.append(f"AI摘要: {item['ai_summary']}")
        
        if item.get('ai_keywords'):
            content_parts.append(f"关键词: {', '.join(item['ai_keywords'])}")
        
        classification = item.get('ai_classification', {})
        if classification:
            content_parts.append(f"分类: {classification.get('category', '')}")
            if classification.get('domain'):
                content_parts.append(f"领域: {classification['domain']}")
        
        value_pred = item.get('value_prediction', {})
        if value_pred:
            content_parts.append(f"价值评分: {value_pred.get('overall_score', 0)}/10")
        
        return '\n\n'.join(content_parts)
    
    async def _generate_notifications(self, analyzed_items: List[Dict[str, Any]], 
                                    trend_analysis: Dict[str, Any]):
        """生成通知"""
        notifications = []
        
        # 高价值信息通知
        high_value_threshold = self.config['notifications']['high_value_threshold']
        high_value_items = [
            item for item in analyzed_items 
            if item.get('value_prediction', {}).get('overall_score', 0) >= high_value_threshold
        ]
        
        if high_value_items:
            notifications.append({
                "type": "high_value",
                "title": f"发现 {len(high_value_items)} 条高价值信息",
                "items": [item['title'] for item in high_value_items[:3]]
            })
        
        # 趋势性信息通知
        trend_threshold = self.config['notifications']['trend_alert_threshold']
        trend_items = [
            item for item in analyzed_items 
            if item.get('value_prediction', {}).get('trend', 0) >= trend_threshold
        ]
        
        if trend_items:
            notifications.append({
                "type": "trend_alert",
                "title": f"发现 {len(trend_items)} 条趋势性信息",
                "items": [item['title'] for item in trend_items[:3]]
            })
        
        # 保存通知
        if notifications:
            self._save_notifications(notifications)
    
    def _save_notifications(self, notifications: List[Dict[str, Any]]):
        """保存通知"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        notification_file = Path(f"data/notifications_{timestamp}.json")
        notification_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(notification_file, 'w', encoding='utf-8') as f:
            json.dump(notifications, f, ensure_ascii=False, indent=2)
        
        logger.info(f"通知已保存: {notification_file}")
    
    def _save_run_summary(self, summary: Dict[str, Any]):
        """保存运行摘要"""
        summary_file = Path("data/run_summaries.jsonl")
        summary_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(summary_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(summary, ensure_ascii=False) + '\n')
    
    def setup_schedule(self):
        """设置定时任务"""
        # 每日定时采集
        daily_time = self.config['schedule']['daily_collection_time']
        schedule.every().day.at(daily_time).do(self._run_daily_job)
        
        # 定期采集
        interval_hours = self.config['schedule']['collection_interval_hours']
        schedule.every(interval_hours).hours.do(self._run_interval_job)
        
        logger.info(f"定时任务已设置:")
        logger.info(f"  每日采集: {daily_time}")
        logger.info(f"  定期采集: 每 {interval_hours} 小时")
    
    def _run_daily_job(self):
        """运行每日任务"""
        asyncio.run(self.run_daily_collection())
    
    def _run_interval_job(self):
        """运行定期任务"""
        # 可以设置为轻量级采集
        asyncio.run(self.run_daily_collection())
    
    def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行")
            return
        
        self.setup_schedule()
        self.is_running = True
        
        logger.info("🚀 自动化调度器启动")
        
        try:
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("收到停止信号")
        finally:
            self.stop_scheduler()
    
    def stop_scheduler(self):
        """停止调度器"""
        self.is_running = False
        if self.knowledge_base:
            self.knowledge_base.close()
        logger.info("✅ 自动化调度器已停止")
    
    async def run_manual_collection(self):
        """手动运行一次采集分析"""
        logger.info("🔧 手动执行信息采集分析...")
        await self.run_daily_collection()
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            "is_running": self.is_running,
            "last_run_time": self.last_run_time.isoformat() if self.last_run_time else None,
            "next_run_time": schedule.next_run().isoformat() if schedule.jobs else None,
            "scheduled_jobs": len(schedule.jobs),
            "services_initialized": {
                "collector": self.collector is not None,
                "analyzer": self.analyzer is not None,
                "knowledge_base": self.knowledge_base is not None
            }
        }


async def main():
    """主函数 - 用于测试"""
    scheduler = AutoScheduler()
    
    try:
        # 手动运行一次测试
        await scheduler.run_manual_collection()
        
        # 显示状态
        status = scheduler.get_status()
        print("调度器状态:", json.dumps(status, ensure_ascii=False, indent=2))
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
    finally:
        scheduler.stop_scheduler()


if __name__ == "__main__":
    asyncio.run(main())
