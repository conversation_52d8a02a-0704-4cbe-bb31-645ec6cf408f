{"id": "doc_25c38159_1751561715", "timestamp": "2025-07-04T00:55:15.552772", "document": {"title": "同步测试文档_1751561715", "content": "这是一个用于测试GitHub同步功能的文档。\n                \n创建时间: 2025-07-04T00:55:15.547521\n测试目的: 验证本地数据库与GitHub仓库的同步\n                \n内容包括:\n1. 数据库存储测试\n2. Markdown文件生成测试  \n3. JSON文件生成测试\n4. GitHub同步验证\n                \n系统状态: 正常运行\n", "file_path": "sync_test_1751561715.md", "file_hash": "25c38159550f4acbc71bce68fc4a0676", "type": "markdown", "size": 1024, "tags": ["同步测试", "GitHub", "数据库"], "keywords": ["同步", "测试", "GitHub", "数据库"], "summary": "用于验证GitHub同步功能的测试文档", "importance_score": 0.9}}