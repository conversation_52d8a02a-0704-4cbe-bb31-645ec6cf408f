"""
Ewandata系统配置文件
"""

import os
from pathlib import Path

class Settings:
    """系统配置类"""

    # 基础路径配置
    BASE_DIR = Path("E:/Ewandata")
    SYSTEM_DIR = BASE_DIR / "ewandata_system"
    DATA_DIR = BASE_DIR / "data"
    TEMP_DIR = BASE_DIR / "temp"
    LOGS_DIR = BASE_DIR / "logs"

    # 临时记文件夹路径
    TEMP_FOLDER = Path("C:/Users/<USER>/Desktop/临时记")

    # AI模型配置
    MODEL_CONFIG = {
        "phi3_mini": {
            "model_name": "microsoft/Phi-3-mini-4k-instruct",
            "max_memory_gb": 5,
            "torch_dtype": "float16",
            "trust_remote_code": True
        },
        "embedding": {
            "model_name": "sentence-transformers/all-MiniLM-L6-v2",
            "max_memory_gb": 1
        }
    }

    # 数据库配置
    DATABASE_CONFIG = {
        "sqlite_db": DATA_DIR / "metadata.db",
        "vector_db": DATA_DIR / "vectors",
        "knowledge_base": DATA_DIR / "knowledge_base"
    }

    # GitHub配置
    GITHUB_CONFIG = {
        "repo_url": "https://github.com/EwanCosmos/Ewandata.git",
        "branch": "main",
        "sync_interval": 3600,  # 1小时
        "auto_sync": True
    }

    # 服务配置
    SERVICE_CONFIG = {
        "api_host": "localhost",
        "api_port": 8000,
        "web_port": 8501,
        "debug": True
    }

    # 文档处理配置
    DOCUMENT_CONFIG = {
        "supported_formats": [
            ".pdf", ".docx", ".txt", ".md", ".rtf",
            ".png", ".jpg", ".jpeg", ".gif",
            ".csv", ".xlsx", ".json"
        ],
        "max_file_size_mb": 100,
        "ocr_language": "ch",
        "batch_size": 10
    }

    # 日志配置
    LOGGING_CONFIG = {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file": LOGS_DIR / "ewandata.log",
        "max_size_mb": 10,
        "backup_count": 5
    }

    @classmethod
    def ensure_directories(cls):
        """确保所有必要目录存在"""
        directories = [
            cls.BASE_DIR,
            cls.DATA_DIR,
            cls.DATA_DIR / "raw",
            cls.DATA_DIR / "processed",
            cls.DATA_DIR / "knowledge_base",
            cls.DATA_DIR / "vectors",
            cls.TEMP_DIR,
            cls.LOGS_DIR,
            cls.TEMP_FOLDER
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    @classmethod
    def get_model_cache_dir(cls):
        """获取模型缓存目录"""
        cache_dir = cls.DATA_DIR / "model_cache"
        cache_dir.mkdir(exist_ok=True)
        return cache_dir


# 创建全局配置实例
settings = Settings()

# 确保目录存在
settings.ensure_directories()