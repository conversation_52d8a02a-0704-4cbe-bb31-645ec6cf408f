{"test_timestamp": "2025-07-04T23:31:04.022187", "test_type": "complete_workflow", "test_summary": {"total_files": 3, "successful_files": 3, "failed_files": 0, "success_rate": 100.0, "average_processing_time": 0.0010159810384114583}, "detailed_results": [{"file": "测试文档1.txt", "success": true, "processing_time": 0.0011532306671142578, "keywords_count": 10, "summary_length": 99, "classification": "项目管理", "output_files": ["data\\processed\\测试文档1_processed.json", "data\\processed\\测试文档1_processed.md"]}, {"file": "代码示例.py", "success": true, "processing_time": 0.0014655590057373047, "keywords_count": 10, "summary_length": 203, "classification": "技术文档", "output_files": ["data\\processed\\代码示例_processed.json", "data\\processed\\代码示例_processed.md"]}, {"file": "项目笔记.md", "success": true, "processing_time": 0.0004291534423828125, "keywords_count": 10, "summary_length": 203, "classification": "技术文档", "output_files": ["data\\processed\\项目笔记_processed.json", "data\\processed\\项目笔记_processed.md"]}], "workflow_steps": ["文件创建", "AI内容分析", "关键词提取", "摘要生成", "内容分类", "JSON输出生成", "Markdown输出生成", "原文件清理（模拟）"]}