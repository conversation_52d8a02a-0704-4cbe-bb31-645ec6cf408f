"""
AI集成功能测试
验证大模型集成后的智能文档处理能力
"""

import sys
import time
import json
from pathlib import Path
from datetime import datetime

# 添加系统路径
sys.path.append('ewandata_system')

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

def print_step(step, description):
    print(f"\n[步骤 {step}] {description}")
    print("-" * 60)

def test_ai_service():
    """测试AI服务基础功能"""
    print_header("🧠 AI服务基础功能测试")
    
    try:
        from services.ai_service import get_ai_service
        
        print_step(1, "初始化AI服务")
        ai_service = get_ai_service()
        
        if not ai_service.initialize_model():
            print("❌ AI模型初始化失败")
            return False
        
        print("✅ AI模型初始化成功")
        
        # 测试文本
        test_content = """
        Ewandata智能知识管理系统技术方案
        
        本文档详细介绍了基于RTX 4070 GPU的个人AI数字助手系统。
        系统采用Microsoft Phi-3-mini大语言模型，提供智能文档处理能力。
        
        核心功能包括：
        1. 自动文档分析和摘要生成
        2. 智能关键词提取和分类
        3. 项目协同机会识别
        4. 知识图谱构建和关联分析
        
        技术栈：
        - Python 3.9+ 
        - FastAPI Web框架
        - ChromaDB向量数据库
        - Streamlit前端界面
        - PyTorch深度学习框架
        
        系统优势：
        - 完全本地化部署，数据安全可控
        - GPU加速推理，响应速度快
        - 支持多种文档格式处理
        - 智能化项目管理和协同分析
        """
        
        print_step(2, "测试关键词提取")
        keywords = ai_service.extract_keywords(test_content, max_keywords=8)
        print(f"提取的关键词: {keywords}")
        
        print_step(3, "测试摘要生成")
        summary = ai_service.generate_summary(test_content, max_length=150)
        print(f"生成的摘要: {summary}")
        
        print_step(4, "测试内容分类")
        classification = ai_service.classify_content(test_content)
        print(f"分类结果: {json.dumps(classification, ensure_ascii=False, indent=2)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入AI服务失败: {e}")
        return False
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        return False

def test_enhanced_document_processor():
    """测试增强文档处理器"""
    print_header("📄 增强文档处理器测试")
    
    try:
        from processors.enhanced_document_processor import create_enhanced_processor
        
        print_step(1, "创建增强文档处理器")
        processor = create_enhanced_processor(enable_ai=True)
        print("✅ 增强文档处理器创建成功")
        
        # 创建测试文件
        print_step(2, "创建测试文档")
        test_files = []
        
        # 技术文档
        tech_doc = Path("C:/Users/<USER>/Desktop/临时记/ai_test_tech.md")
        tech_content = """# AI项目开发指南

## 项目概述
本项目旨在开发一个基于深度学习的智能推荐系统。

## 技术栈
- Python 3.9
- TensorFlow 2.x
- FastAPI
- Redis缓存
- PostgreSQL数据库

## 核心算法
使用协同过滤和深度神经网络相结合的混合推荐算法。

## 部署方案
采用Docker容器化部署，支持Kubernetes集群管理。
"""
        
        # 项目计划
        plan_doc = Path("C:/Users/<USER>/Desktop/临时记/ai_test_plan.txt")
        plan_content = """项目管理计划 - Q1季度目标

重要任务：
1. 完成用户画像系统开发
2. 优化推荐算法性能
3. 集成A/B测试框架
4. 部署生产环境

技术债务：
- 重构数据处理模块
- 优化数据库查询性能
- 完善单元测试覆盖率

风险评估：
- 数据质量问题可能影响推荐效果
- 第三方API稳定性需要监控
- 团队人员变动风险
"""
        
        # 学习笔记
        note_doc = Path("C:/Users/<USER>/Desktop/临时记/ai_test_note.json")
        note_content = {
            "title": "机器学习算法学习笔记",
            "date": "2025-01-03",
            "topics": [
                "监督学习",
                "无监督学习", 
                "强化学习",
                "深度学习"
            ],
            "key_concepts": {
                "梯度下降": "优化算法的核心",
                "反向传播": "神经网络训练的基础",
                "正则化": "防止过拟合的技术"
            },
            "applications": [
                "图像识别",
                "自然语言处理",
                "推荐系统",
                "语音识别"
            ]
        }
        
        # 写入测试文件
        test_files_data = [
            (tech_doc, tech_content),
            (plan_doc, plan_content),
            (note_doc, json.dumps(note_content, ensure_ascii=False, indent=2))
        ]
        
        for file_path, content in test_files_data:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            test_files.append(file_path)
            print(f"✅ 创建测试文件: {file_path.name}")
        
        # 处理测试文件
        print_step(3, "AI增强文档处理")
        processed_results = []
        
        for file_path in test_files:
            print(f"\n处理文件: {file_path.name}")
            result = processor.process_file(str(file_path))
            
            if "error" not in result:
                print(f"✅ 处理成功")
                print(f"   AI处理: {result.get('ai_processed', False)}")
                print(f"   关键词: {result.get('keywords', [])[:5]}")
                print(f"   摘要: {result.get('summary', '无')[:100]}...")
                
                classification = result.get('classification', {})
                print(f"   分类: {classification.get('category', '未分类')}")
                print(f"   重要性: {classification.get('importance', 0)}/10")
                
                processed_results.append(result)
            else:
                print(f"❌ 处理失败: {result['error']}")
        
        # 分析文档关系
        if processed_results:
            print_step(4, "文档关系分析")
            relationship_analysis = processor.analyze_content_relationships(processed_results)
            
            if "error" not in relationship_analysis:
                print("✅ 关系分析完成")
                print(f"   文档数量: {relationship_analysis.get('total_documents', 0)}")
                print(f"   唯一关键词: {len(relationship_analysis.get('unique_keywords', []))}")
                print(f"   唯一主题: {len(relationship_analysis.get('unique_topics', []))}")
                print(f"   分析结果: {relationship_analysis.get('relationship_analysis', '')[:200]}...")
            else:
                print(f"❌ 关系分析失败: {relationship_analysis['error']}")
        
        # 清理测试文件
        print_step(5, "清理测试文件")
        for file_path in test_files:
            if file_path.exists():
                file_path.unlink()
                print(f"🗑️ 删除: {file_path.name}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入增强处理器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 增强处理器测试失败: {e}")
        return False

def test_project_ai_analysis():
    """测试项目AI分析功能"""
    print_header("🤝 项目AI协同分析测试")
    
    try:
        from services.ai_service import get_ai_service
        
        ai_service = get_ai_service()
        
        # 模拟项目信息
        project_info = {
            "name": "智能推荐系统",
            "tech_stack": ["Python", "TensorFlow", "FastAPI", "Redis"],
            "description": "基于深度学习的个性化推荐系统",
            "team_size": 5,
            "status": "开发中"
        }
        
        print_step(1, "AI项目协同分析")
        analysis = ai_service.analyze_project_collaboration(project_info)
        
        print("✅ 项目分析完成")
        print(f"分析结果: {analysis.get('analysis', '')[:300]}...")
        print(f"协同评分: {analysis.get('collaboration_score', 0)}/10")
        
        return True
        
    except Exception as e:
        print(f"❌ 项目AI分析失败: {e}")
        return False

def test_end_to_end_workflow():
    """测试端到端AI工作流"""
    print_header("🔄 端到端AI工作流测试")
    
    try:
        # 模拟完整的文档处理流程
        print_step(1, "模拟文档上传")
        
        # 创建临时文档
        temp_doc = Path("C:/Users/<USER>/Desktop/临时记/ai_workflow_test.md")
        temp_content = """# Ewandata系统AI集成测试

## 测试目标
验证AI大模型集成后的完整工作流程。

## 测试内容
1. 文档自动处理和分析
2. 智能关键词提取
3. 自动摘要生成
4. 内容分类和标签
5. 知识关联分析

## 预期结果
系统应该能够：
- 自动识别文档类型和主题
- 提取有意义的关键词
- 生成准确的摘要
- 进行智能分类
- 建立知识关联

## 技术验证
- AI模型推理速度
- 处理结果准确性
- 系统稳定性
- 资源使用效率
"""
        
        temp_doc.parent.mkdir(parents=True, exist_ok=True)
        with open(temp_doc, 'w', encoding='utf-8') as f:
            f.write(temp_content)
        
        print(f"✅ 创建测试文档: {temp_doc.name}")
        
        print_step(2, "AI增强处理")
        from processors.enhanced_document_processor import create_enhanced_processor
        
        processor = create_enhanced_processor(enable_ai=True)
        result = processor.process_file(str(temp_doc))
        
        if "error" not in result:
            print("✅ AI处理成功")
            
            # 显示详细结果
            print(f"\n📊 处理结果详情:")
            print(f"   文件大小: {result.get('size_mb', 0):.3f} MB")
            print(f"   字符数: {result.get('char_count', 0)}")
            print(f"   AI增强: {result.get('ai_processed', False)}")
            
            if result.get('ai_processed'):
                print(f"\n🧠 AI分析结果:")
                print(f"   关键词: {', '.join(result.get('keywords', []))}")
                print(f"   摘要: {result.get('summary', '无')}")
                
                classification = result.get('classification', {})
                print(f"   类别: {classification.get('category', '未分类')}")
                print(f"   主题: {', '.join(classification.get('topics', []))}")
                print(f"   重要性: {classification.get('importance', 0)}/10")
                print(f"   标签: {', '.join(classification.get('tags', []))}")
        else:
            print(f"❌ AI处理失败: {result['error']}")
        
        print_step(3, "清理测试文件")
        if temp_doc.exists():
            temp_doc.unlink()
            print(f"🗑️ 删除测试文件: {temp_doc.name}")
        
        return "error" not in result and result.get('ai_processed', False)
        
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print_header("🧠 Ewandata AI集成功能全面测试")
    
    print("本测试将验证AI大模型集成后的智能化能力:")
    print("1. AI服务基础功能")
    print("2. 增强文档处理器")
    print("3. 项目AI协同分析")
    print("4. 端到端AI工作流")
    
    tests = [
        ("AI服务基础功能", test_ai_service),
        ("增强文档处理器", test_enhanced_document_processor),
        ("项目AI协同分析", test_project_ai_analysis),
        ("端到端AI工作流", test_end_to_end_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n⏳ 开始测试: {test_name}")
        start_time = time.time()
        
        try:
            success = test_func()
            end_time = time.time()
            duration = end_time - start_time
            
            results.append((test_name, success, duration))
            status = "✅ 通过" if success else "❌ 失败"
            print(f"📊 {test_name}: {status} (耗时: {duration:.1f}秒)")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            results.append((test_name, False, duration))
            print(f"❌ {test_name}: 异常 - {str(e)} (耗时: {duration:.1f}秒)")
    
    # 显示最终结果
    print_header("📋 AI集成测试结果")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success, _ in results if success)
    total_time = sum(duration for _, _, duration in results)
    
    print(f"📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    print(f"⏱️ 总耗时: {total_time:.1f}秒")
    print(f"🎯 成功率: {(passed_tests/total_tests*100):.1f}%")
    
    print(f"\n📝 详细结果:")
    for test_name, success, duration in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status} ({duration:.1f}秒)")
    
    if passed_tests == total_tests:
        print(f"\n🎉 恭喜！AI集成功能全部正常！")
        print("✅ 系统现在具备完整的智能化能力")
        print("✅ 可以开始享受AI驱动的知识管理")
    elif passed_tests >= total_tests * 0.75:
        print(f"\n✅ 大部分AI功能正常，系统基本可用")
        print("建议检查失败的测试项目")
    else:
        print(f"\n⚠️ 多个AI功能测试失败")
        print("请检查AI模型安装和配置")

if __name__ == "__main__":
    main()
