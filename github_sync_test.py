"""
GitHub同步功能专项测试
解决依赖问题并验证同步功能
"""

import sys
import os
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime

# 添加系统路径
sys.path.append('ewandata_system')

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n[步骤 {step}] {description}")
    print("-" * 40)

def test_git_basic_operations():
    """测试基础Git操作"""
    print_header("🔧 Git基础操作测试")
    
    base_path = Path("E:/Ewandata")
    os.chdir(base_path)
    
    try:
        print_step(1, "检查Git仓库状态")
        
        # 检查是否是Git仓库
        if (base_path / ".git").exists():
            print("✅ 是Git仓库")
            
            # 检查远程仓库
            result = subprocess.run(['git', 'remote', '-v'], 
                                  capture_output=True, text=True, check=True)
            print(f"远程仓库配置:\n{result.stdout}")
            
            # 检查工作区状态
            status_result = subprocess.run(['git', 'status', '--porcelain'], 
                                         capture_output=True, text=True, check=True)
            
            if status_result.stdout.strip():
                print("⚠️ 工作区有未提交的变更:")
                changes = status_result.stdout.strip().split('\n')
                for change in changes[:10]:  # 只显示前10个
                    print(f"   {change}")
                if len(changes) > 10:
                    print(f"   ... 还有 {len(changes) - 10} 个变更")
            else:
                print("✅ 工作区干净")
            
            return True
        else:
            print("❌ 不是Git仓库")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Git操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_github_connectivity():
    """测试GitHub连接"""
    print_header("🌐 GitHub连接测试")
    
    try:
        print_step(1, "测试GitHub连接")
        
        # 测试fetch操作
        result = subprocess.run(['git', 'fetch', '--dry-run'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ GitHub连接正常")
            return True
        else:
            print(f"⚠️ GitHub连接问题:")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 连接超时，可能是网络问题")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_database_files():
    """测试数据库文件状态"""
    print_header("🗄️ 数据库文件测试")
    
    base_path = Path("E:/Ewandata")
    
    try:
        print_step(1, "检查数据库文件")
        
        # 检查知识库数据库
        kb_db = base_path / "data" / "knowledge_base.db"
        if kb_db.exists():
            size = kb_db.stat().st_size
            print(f"✅ knowledge_base.db: {size} bytes")
        else:
            print("❌ knowledge_base.db 不存在")
        
        # 检查项目数据库
        proj_db = base_path / "data" / "projects.db"
        if proj_db.exists():
            size = proj_db.stat().st_size
            print(f"✅ projects.db: {size} bytes")
        else:
            print("❌ projects.db 不存在")
        
        print_step(2, "检查知识库文件")
        
        # 检查Markdown文件
        kb_dir = base_path / "data" / "knowledge_base"
        if kb_dir.exists():
            md_files = list(kb_dir.glob("*.md"))
            print(f"✅ Markdown文件: {len(md_files)} 个")
            if md_files:
                print("   示例文件:")
                for file in md_files[:3]:
                    print(f"     - {file.name}")
        else:
            print("❌ knowledge_base目录不存在")
        
        # 检查JSON文件
        proc_dir = base_path / "data" / "processed"
        if proc_dir.exists():
            json_files = list(proc_dir.glob("*.json"))
            print(f"✅ JSON文件: {len(json_files)} 个")
            if json_files:
                print("   示例文件:")
                for file in json_files[:3]:
                    print(f"     - {file.name}")
        else:
            print("❌ processed目录不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库文件测试失败: {e}")
        return False

def test_sync_preparation():
    """测试同步准备"""
    print_header("🔄 同步准备测试")
    
    base_path = Path("E:/Ewandata")
    os.chdir(base_path)
    
    try:
        print_step(1, "准备同步文件")
        
        # 添加所有变更到暂存区
        subprocess.run(['git', 'add', '.'], check=True)
        print("✅ 文件已添加到暂存区")
        
        # 检查暂存区状态
        status_result = subprocess.run(['git', 'status', '--cached', '--porcelain'], 
                                     capture_output=True, text=True, check=True)
        
        if status_result.stdout.strip():
            print("📝 暂存区文件:")
            staged_files = status_result.stdout.strip().split('\n')
            for file in staged_files[:10]:  # 只显示前10个
                print(f"   {file}")
            if len(staged_files) > 10:
                print(f"   ... 还有 {len(staged_files) - 10} 个文件")
        else:
            print("ℹ️ 暂存区为空")
        
        print_step(2, "创建提交")
        
        # 创建提交
        commit_message = f"自动测试提交 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        commit_result = subprocess.run(['git', 'commit', '-m', commit_message], 
                                     capture_output=True, text=True)
        
        if commit_result.returncode == 0:
            print(f"✅ 提交创建成功: {commit_message}")
            print(f"   {commit_result.stdout.strip()}")
        else:
            print(f"ℹ️ 提交结果: {commit_result.stdout.strip()}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 同步准备失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_github_push():
    """测试GitHub推送（可选）"""
    print_header("📤 GitHub推送测试")
    
    try:
        print_step(1, "检查推送状态")
        
        # 检查是否有需要推送的提交
        status_result = subprocess.run(['git', 'status', '-b', '--porcelain'], 
                                     capture_output=True, text=True, check=True)
        
        status_lines = status_result.stdout.strip().split('\n')
        branch_line = status_lines[0] if status_lines else ""
        
        if "ahead" in branch_line:
            print("📤 有提交需要推送到GitHub")
            print(f"   状态: {branch_line}")
            
            # 询问是否推送（在实际使用中可以自动推送）
            print("\n⚠️ 注意: 这将推送到GitHub仓库")
            print("   在生产环境中，这里会自动推送")
            print("   当前为测试模式，跳过实际推送")
            
            return True
        else:
            print("ℹ️ 没有需要推送的提交")
            return True
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 推送测试失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print_header("🚀 GitHub同步功能专项测试")
    
    print("本测试将验证:")
    print("1. Git基础操作")
    print("2. GitHub连接状态")
    print("3. 数据库文件状态")
    print("4. 同步准备流程")
    print("5. GitHub推送准备")
    
    # 测试列表
    tests = [
        ("Git基础操作", test_git_basic_operations),
        ("GitHub连接", test_github_connectivity),
        ("数据库文件", test_database_files),
        ("同步准备", test_sync_preparation),
        ("GitHub推送", test_github_push)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n⏳ 开始测试: {test_name}")
        time.sleep(1)
        
        try:
            success = test_func()
            results.append((test_name, success))
            status = "✅ 通过" if success else "❌ 失败"
            print(f"📊 {test_name}: {status}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")
            results.append((test_name, False))
        
        time.sleep(1)
    
    # 显示最终结果
    print_header("📋 测试结果汇总")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    
    print(f"📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
    
    print("\n详细结果:")
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有GitHub同步测试通过！")
        print("系统已准备好进行GitHub同步操作。")
    elif passed_tests >= total_tests * 0.8:
        print("\n✅ 大部分测试通过，系统基本可用。")
        print("建议解决失败的测试项目。")
    else:
        print("\n⚠️ 多个测试失败，建议检查系统配置。")
    
    print("\n💡 下一步操作建议:")
    print("1. 如果所有测试通过，可以安全地进行GitHub同步")
    print("2. 如果有测试失败，请根据错误信息进行修复")
    print("3. 定期运行此测试以确保同步功能正常")


if __name__ == "__main__":
    main()
