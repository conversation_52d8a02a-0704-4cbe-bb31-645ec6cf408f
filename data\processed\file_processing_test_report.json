{"test_timestamp": "2025-07-04T23:28:56.259850", "test_summary": {"total_files": 11, "successful_files": 11, "failed_files": 0, "success_rate": 100.0, "average_processing_time": 0.0019019517031582918}, "detailed_results": [{"file": "ai调用迅雷下载.txt", "success": true, "processing_time": 0.004357337951660156, "keywords_count": 10, "summary_length": 129, "classification": "技术文档"}, {"file": "blogger.txt", "success": true, "processing_time": 0.0019388198852539062, "keywords_count": 10, "summary_length": 79, "classification": "技术文档"}, {"file": "data流程cursor生成.txt", "success": true, "processing_time": 0.0016443729400634766, "keywords_count": 10, "summary_length": 128, "classification": "技术文档"}, {"file": "Ewandata项目需求英文描述.txt", "success": true, "processing_time": 0.0018260478973388672, "keywords_count": 10, "summary_length": 84, "classification": "技术文档"}, {"file": "ima网址信息.txt", "success": true, "processing_time": 0.0006289482116699219, "keywords_count": 3, "summary_length": 15, "classification": "其他"}, {"file": "logo设计.txt", "success": true, "processing_time": 0.0006480216979980469, "keywords_count": 3, "summary_length": 29, "classification": "其他"}, {"file": "qwen cli.txt", "success": true, "processing_time": 0.006226539611816406, "keywords_count": 10, "summary_length": 69, "classification": "技术文档"}, {"file": "type Engligh改成人工智能学习工具.txt", "success": true, "processing_time": 0.0008232593536376953, "keywords_count": 6, "summary_length": 88, "classification": "学习笔记"}, {"file": "你猜我做.txt", "success": true, "processing_time": 0.001249551773071289, "keywords_count": 10, "summary_length": 106, "classification": "项目管理"}, {"file": "博客.txt", "success": true, "processing_time": 0.0005803108215332031, "keywords_count": 1, "summary_length": 9, "classification": "其他"}, {"file": "厨房agent.txt", "success": true, "processing_time": 0.0009982585906982422, "keywords_count": 10, "summary_length": 101, "classification": "项目管理"}], "test_files_info": [{"name": ".txt", "size_bytes": 0, "type": ""}, {"name": "1.png", "size_bytes": 96146, "type": ".png"}, {"name": "10.png", "size_bytes": 145794, "type": ".png"}, {"name": "11.png", "size_bytes": 299956, "type": ".png"}, {"name": "11110.png", "size_bytes": 68189, "type": ".png"}, {"name": "12.png", "size_bytes": 252131, "type": ".png"}, {"name": "13.png", "size_bytes": 162004, "type": ".png"}, {"name": "14.png", "size_bytes": 171310, "type": ".png"}, {"name": "15.png", "size_bytes": 94659, "type": ".png"}, {"name": "16.png", "size_bytes": 100241, "type": ".png"}, {"name": "17.png", "size_bytes": 151317, "type": ".png"}, {"name": "18.png", "size_bytes": 153397, "type": ".png"}, {"name": "19.png", "size_bytes": 165352, "type": ".png"}, {"name": "1_1_桌面文件包", "size_bytes": 0, "type": ""}, {"name": "2.png", "size_bytes": 150553, "type": ".png"}, {"name": "20.png", "size_bytes": 141732, "type": ".png"}, {"name": "21.png", "size_bytes": 183931, "type": ".png"}, {"name": "22.png", "size_bytes": 162109, "type": ".png"}, {"name": "23.png", "size_bytes": 286566, "type": ".png"}, {"name": "24.png", "size_bytes": 144086, "type": ".png"}, {"name": "25.png", "size_bytes": 134936, "type": ".png"}, {"name": "26.png", "size_bytes": 162992, "type": ".png"}, {"name": "27.png", "size_bytes": 114635, "type": ".png"}, {"name": "28.png", "size_bytes": 78095, "type": ".png"}, {"name": "29.png", "size_bytes": 238285, "type": ".png"}, {"name": "3.png", "size_bytes": 78981, "type": ".png"}, {"name": "30.png", "size_bytes": 134715, "type": ".png"}, {"name": "31.png", "size_bytes": 525894, "type": ".png"}, {"name": "32.png", "size_bytes": 131703, "type": ".png"}, {"name": "33.png", "size_bytes": 142312, "type": ".png"}, {"name": "34.png", "size_bytes": 183494, "type": ".png"}, {"name": "35.png", "size_bytes": 141771, "type": ".png"}, {"name": "36.png", "size_bytes": 483634, "type": ".png"}, {"name": "37.png", "size_bytes": 111567, "type": ".png"}, {"name": "38.png", "size_bytes": 49025, "type": ".png"}, {"name": "39.png", "size_bytes": 144296, "type": ".png"}, {"name": "4.png", "size_bytes": 136552, "type": ".png"}, {"name": "40.png", "size_bytes": 114432, "type": ".png"}, {"name": "5.png", "size_bytes": 122349, "type": ".png"}, {"name": "6.png", "size_bytes": 63020, "type": ".png"}, {"name": "7.png", "size_bytes": 117144, "type": ".png"}, {"name": "8.png", "size_bytes": 135042, "type": ".png"}, {"name": "9.png", "size_bytes": 157217, "type": ".png"}, {"name": "ai调用迅雷下载.txt", "size_bytes": 5834, "type": ".txt"}, {"name": "blogger.txt", "size_bytes": 5442, "type": ".txt"}, {"name": "data流程.png", "size_bytes": 37780, "type": ".png"}, {"name": "data流程cursor生成.txt", "size_bytes": 981, "type": ".txt"}, {"name": "Ewandata.png", "size_bytes": 30023, "type": ".png"}, {"name": "Ewandata项目需求英文描述.txt", "size_bytes": 6695, "type": ".txt"}, {"name": "fetch", "size_bytes": 0, "type": ""}, {"name": "ima网址信息.txt", "size_bytes": 19, "type": ".txt"}, {"name": "logo设计.txt", "size_bytes": 59, "type": ".txt"}, {"name": "qwen cli.txt", "size_bytes": 10472, "type": ".txt"}, {"name": "type Engligh改成人工智能学习工具.txt", "size_bytes": 222, "type": ".txt"}, {"name": "你猜我做.txt", "size_bytes": 1076, "type": ".txt"}, {"name": "博客.txt", "size_bytes": 29, "type": ".txt"}, {"name": "厨房agent.txt", "size_bytes": 1743, "type": ".txt"}, {"name": "厨房agent有啥吃啥计划.docx", "size_bytes": 11446, "type": ".docx"}, {"name": "微信截图_20250630191549.png", "size_bytes": 352516, "type": ".png"}, {"name": "微信截图_20250702235253.png", "size_bytes": 305341, "type": ".png"}]}