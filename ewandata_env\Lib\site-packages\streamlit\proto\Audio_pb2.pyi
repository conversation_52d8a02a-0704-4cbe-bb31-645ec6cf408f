"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import streamlit.proto.WidthConfig_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class Audio(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URL_FIELD_NUMBER: builtins.int
    START_TIME_FIELD_NUMBER: builtins.int
    END_TIME_FIELD_NUMBER: builtins.int
    LOOP_FIELD_NUMBER: builtins.int
    AUTOPLAY_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    WIDTH_CONFIG_FIELD_NUMBER: builtins.int
    url: builtins.str
    start_time: builtins.int
    """The currentTime attribute of the HTML <audio> tag's <source> subtag."""
    end_time: builtins.int
    """The time at which the audio should stop playing. If not specified, plays to the end."""
    loop: builtins.bool
    """Indicates whether the audio should start over from the beginning once it ends."""
    autoplay: builtins.bool
    id: builtins.str
    @property
    def width_config(self) -> streamlit.proto.WidthConfig_pb2.WidthConfig: ...
    def __init__(
        self,
        *,
        url: builtins.str = ...,
        start_time: builtins.int = ...,
        end_time: builtins.int = ...,
        loop: builtins.bool = ...,
        autoplay: builtins.bool = ...,
        id: builtins.str = ...,
        width_config: streamlit.proto.WidthConfig_pb2.WidthConfig | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_width_config", b"_width_config", "width_config", b"width_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_width_config", b"_width_config", "autoplay", b"autoplay", "end_time", b"end_time", "id", b"id", "loop", b"loop", "start_time", b"start_time", "url", b"url", "width_config", b"width_config"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_width_config", b"_width_config"]) -> typing.Literal["width_config"] | None: ...

global___Audio = Audio
