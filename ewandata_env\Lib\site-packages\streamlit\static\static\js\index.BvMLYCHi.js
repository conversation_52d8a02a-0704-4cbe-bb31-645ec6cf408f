import{n as h,r as i,bg as T,j as p,l as b}from"./index.C1NIn1Y2.js";const L=h("iframe",{target:"eymnmwl0"})(({theme:r})=>({colorScheme:"normal",border:"none",padding:r.spacing.none,margin:r.spacing.none,width:"100%",aspectRatio:"16 / 9"})),N=b.getLogger("Video"),U={width:"100%"};function R({element:r,endpoints:s,elementMgr:l}){const n=i.useRef(null),{type:y,url:f,startTime:a,subtitles:d,endTime:o,loop:c,autoplay:m,muted:E}=r,S=i.useMemo(()=>{if(!r.id)return!0;const e=l.getElementState(r.id,"preventAutoplay");return e||l.setElementState(r.id,"preventAutoplay",!0),e??!1},[r.id,l]),v=i.useMemo(()=>JSON.stringify(d?d.map(e=>s.buildMediaURL(`${e.url}`)):[]),[d,s]);i.useEffect(()=>{const e=JSON.parse(v);e.length!==0&&e.forEach(t=>{s.checkSourceUrlResponse(t,"Video Subtitle")})},[v,s]),i.useEffect(()=>{n.current&&(n.current.currentTime=a)},[a]),i.useEffect(()=>{const e=n.current,t=()=>{e&&(e.currentTime=r.startTime)};return e&&e.addEventListener("loadedmetadata",t),()=>{e&&e.removeEventListener("loadedmetadata",t)}},[r]),i.useEffect(()=>{const e=n.current;if(!e)return;let t=!1;const u=()=>{o>0&&e.currentTime>=o&&(c?(e.currentTime=a||0,e.play()):t||(t=!0,e.pause()))};return o>0&&e.addEventListener("timeupdate",u),()=>{e&&o>0&&e.removeEventListener("timeupdate",u)}},[o,c,a]),i.useEffect(()=>{const e=n.current;if(!e)return;const t=()=>{c&&(e.currentTime=a||0,e.play())};return e.addEventListener("ended",t),()=>{e&&e.removeEventListener("ended",t)}},[c,a]);const g=e=>{const t=new URL(e);if(a&&!isNaN(a)&&t.searchParams.append("start",a.toString()),o&&!isNaN(o)&&t.searchParams.append("end",o.toString()),c){t.searchParams.append("loop","1");const u=t.pathname.split("/").pop();u&&t.searchParams.append("playlist",u)}return m&&t.searchParams.append("autoplay","1"),E&&t.searchParams.append("mute","1"),t.toString()};if(y===T.Type.YOUTUBE_IFRAME)return p(L,{className:"stVideo","data-testid":"stVideo",title:f,src:g(f),allow:"autoplay; encrypted-media",allowFullScreen:!0});const V=e=>{const t=e.currentTarget.src;N.error(`Client Error: Video source error - ${t}`),s.sendClientErrorToHost("Video","Video source failed to load","onerror triggered",t)};return p("video",{className:"stVideo","data-testid":"stVideo",ref:n,controls:!0,muted:E,autoPlay:m&&!S,src:s.buildMediaURL(f),style:U,crossOrigin:void 0,onError:V,children:d&&d.map((e,t)=>p("track",{kind:"captions",src:s.buildMediaURL(`${e.url}`),label:`${e.label}`,default:t===0,"data-testid":"stVideoSubtitle"},t))})}const A=i.memo(R);export{A as default};
