{"metadata": {"generated_time": "2025-07-05T00:30:44.861427", "total_documents": 28, "total_categories": 4, "total_topics": 4, "total_keywords": 144, "generator": "Ewandata混合AI系统", "image_documents": 10, "last_updated": "2025-07-05T01:07:14.197903"}, "statistics": {"documents_by_category": {"其他": 8, "技术文档": 8, "学习笔记": 1, "项目管理": 6}, "documents_by_topic": {"人工智能": 13, "编程": 8, "学习": 1, "管理": 6}, "keyword_frequency": {"file": 6, "word处理错误": 5, "docx": 8, "not": 5, "zip": 5, "import": 3, "word文档": 3, "python": 2, "4070": 3, "def": 2, "我想到一些问题需要补充提醒一下你": 2, "既然是智能体": 2, "就一定要区别于传统的视频教学app": 2, "一定要和用户之间有客户舒服的较为密切的交互": 2, "比如先问他今天想吃啥": 2, "冰箱里有啥": 2, "更想吃什么口味的食物": 2}, "importance_distribution": {}}, "documents": {"29条工程提示词简介及案例_processed": {"id": "29条工程提示词简介及案例_processed", "name": "29条工程提示词简介及案例.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\桌面文件包\\29条工程提示词简介及案例.docx", "processed_time": "2025-07-05T00:24:56.968580", "file_type": ".docx", "size_chars": 53, "size_bytes": 0, "keywords": ["file", "word处理错误", "29条工程提示词简介及案例", "docx", "not", "zip"], "summary": "[Word处理错误] 29条工程提示词简介及案例. docx: File is not a zip file", "category": "其他", "topics": [], "importance": 1, "tags": [], "related_documents": ["ima网址信息_processed", "qwen cli_processed", "进销存记忆_processed", "厨房agent有啥吃啥计划_improved_processed", "brain记忆文档_improved_processed", "区块链大模型构想_processed", "项目结构树_processed", "博客_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "ai自动化记忆_processed": {"id": "ai自动化记忆_processed", "name": "ai自动化记忆.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\ai自动化记忆.docx", "processed_time": "2025-07-05T00:24:56.947916", "file_type": ".docx", "size_chars": 47, "size_bytes": 0, "keywords": ["file", "word处理错误", "ai自动化记忆", "docx", "not", "zip"], "summary": "[Word处理错误] ai自动化记忆. docx: File is not a zip file", "category": "其他", "topics": ["人工智能"], "importance": 1, "tags": ["AI"], "related_documents": ["以太坊信息_improved_processed", "个人简介_improved_processed", "代码示例_processed", "qwen cli_processed", "进销存记忆_processed", "blogger_processed", "短视频ai新闻prompt完美版_improved_processed", "区块链大模型构想_processed", "项目笔记_processed", "29条工程提示词简介及案例_processed", "Ewandata项目需求英文描述_processed", "测试文档1_processed", "type Engligh改成人工智能学习工具_processed", "博客_processed", "ima网址信息_processed", "厨房agent有啥吃啥计划_improved_processed", "brain记忆文档_improved_processed", "项目结构树_processed", "logo设计_processed"]}, "blogger_processed": {"id": "blogger_processed", "name": "blogger.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\blogger.txt", "processed_time": "2025-07-04T23:28:56.136192", "file_type": ".txt", "size_chars": 3037, "size_bytes": 5442, "keywords": ["whisper", "import", "model", "transcript", "video", "large", "result", "mp4", "ollama", "ffmpeg"], "summary": "有几个类似Monica的AI工具可以将YouTube视频转换为博客内容：\nBlogify. ai - 这是一个专门将YouTube视频转换成博客文章的AI工具", "category": "技术文档", "topics": ["编程", "人工智能"], "importance": 10, "tags": ["技术", "AI"], "related_documents": ["个人简介_improved_processed", "代码示例_processed", "data流程cursor生成_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "短视频ai新闻prompt完美版_improved_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "type Engligh改成人工智能学习工具_processed", "项目笔记_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "brain记忆文档_improved_processed": {"id": "brain记忆文档_improved_processed", "name": "brain记忆文档.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\brain记忆文档.docx", "processed_time": "2025-07-05T00:28:42.885920", "file_type": ".docx", "size_chars": 1302, "size_bytes": 15379, "keywords": ["three", "react", "drei", "fiber", "node_modules", "word文档", "brain记忆文档", "docx", "以下是您当前项目的关键信息梳理及执行流程概述", "供新ai对话框快速了解已完成的服务进度"], "summary": "[Word文档] brain记忆文档. docx\n\n以下是您当前项目的关键信息梳理及执行流程概述，供新AI对话框快速了解已完成的服务进度、用户的使用习惯，以及继续完善项目的任务进度：\n项目进展\n项目名称: BrainLight", "category": "技术文档", "topics": ["编程", "人工智能"], "importance": 10, "tags": ["技术", "AI"], "related_documents": ["个人简介_improved_processed", "代码示例_processed", "data流程cursor生成_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "厨房agent有啥吃啥计划_improved_processed", "进销存记忆_processed", "blogger_processed", "测试文档1_processed", "短视频ai新闻prompt完美版_improved_processed", "type Engligh改成人工智能学习工具_processed", "区块链大模型构想_processed", "项目笔记_processed", "项目结构树_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed", "logo设计_processed"]}, "data流程cursor生成_processed": {"id": "data流程cursor生成_processed", "name": "data流程cursor生成.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\data流程cursor生成.txt", "processed_time": "2025-07-04T23:28:56.154984", "file_type": ".txt", "size_chars": 392, "size_bytes": 981, "keywords": ["系统架构设计", "部署方式", "python", "轻量级容器化", "我建议使用python作为主要开发语言", "结合docker进行部分服务的容器化", "主要应用使用python开发", "便于快速迭代和调试", "将llama模型服务和数据库等稳定组件容器化", "确保环境一致性"], "summary": "系统架构设计\n1. 部署方式：Python + 轻量级容器化\n我建议使用Python作为主要开发语言，结合Docker进行部分服务的容器化：\n主要应用使用Python开发，便于快速迭代和调试\n将Llama模型服务和数据库等稳定组件容器化，确保环境一致性\n2", "category": "技术文档", "topics": ["编程"], "importance": 4, "tags": ["技术"], "related_documents": ["个人简介_improved_processed", "代码示例_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "blogger_processed", "brain记忆文档_improved_processed", "项目笔记_processed"]}, "Ewandata项目需求英文描述_processed": {"id": "Ewandata项目需求英文描述_processed", "name": "Ewandata项目需求英文描述.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\Ewandata项目需求英文描述.txt", "processed_time": "2025-07-04T23:28:56.169607", "file_type": ".txt", "size_chars": 2796, "size_bytes": 6695, "keywords": ["ewandata", "4070", "phi", "主题分类", "http", "localhost", "帮我创建一个本地知识库项目", "名字叫ewandata路径在e", "它的主要作用就是成为一个知道我所有数字信息的ai数字系统", "也是我最好的私人助理和工作搭档"], "summary": "帮我创建一个本地知识库项目，名字叫Ewandata路径在E：Ewandata. 它的主要作用就是成为一个知道我所有数字信息的AI数字系统，也是我最好的私人助理和工作搭档", "category": "技术文档", "topics": ["编程", "人工智能"], "importance": 10, "tags": ["技术", "AI"], "related_documents": ["个人简介_improved_processed", "代码示例_processed", "data流程cursor生成_processed", "qwen cli_processed", "blogger_processed", "短视频ai新闻prompt完美版_improved_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "type Engligh改成人工智能学习工具_processed", "项目笔记_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "ima网址信息_processed": {"id": "ima网址信息_processed", "name": "ima网址信息.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\ima网址信息.txt", "processed_time": "2025-07-04T23:28:56.183880", "file_type": ".txt", "size_chars": 19, "size_bytes": 19, "keywords": ["https", "ima", "com"], "summary": "https://ima. qq", "category": "其他", "topics": [], "importance": 1, "tags": [], "related_documents": ["进销存记忆_processed", "区块链大模型构想_processed", "博客_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed", "logo设计_processed"]}, "logo设计_processed": {"id": "logo设计_processed", "name": "logo设计.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\logo设计.txt", "processed_time": "2025-07-04T23:28:56.195096", "file_type": ".txt", "size_chars": 29, "size_bytes": 59, "keywords": ["logo自动设计ai", "写字做logo", "写需求描述做logo"], "summary": "logo自动设计ai，写字做logo，写需求描述做logo", "category": "其他", "topics": ["人工智能"], "importance": 1, "tags": ["AI"], "related_documents": ["个人简介_improved_processed", "ima网址信息_processed", "代码示例_processed", "qwen cli_processed", "Ewandata项目需求英文描述_processed", "blogger_processed", "短视频ai新闻prompt完美版_improved_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "进销存记忆_processed", "type Engligh改成人工智能学习工具_processed", "项目笔记_processed", "区块链大模型构想_processed", "博客_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed"]}, "qwen cli_processed": {"id": "qwen cli_processed", "name": "qwen cli.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\qwen cli.txt", "processed_time": "2025-07-04T23:28:56.213889", "file_type": ".txt", "size_chars": 7491, "size_bytes": 10472, "keywords": ["llama3", "def", "file", "print", "python", "import", "path", "get", "params", "json"], "summary": "### 🌟 项目目标\n我们希望构建一个类似 Gemini CLI 的工具，但使用你本地的 LLaMA3-8B 模型. 这个工具将能够：\n1", "category": "技术文档", "topics": ["编程", "人工智能"], "importance": 10, "tags": ["技术", "AI"], "related_documents": ["个人简介_improved_processed", "代码示例_processed", "data流程cursor生成_processed", "Ewandata项目需求英文描述_processed", "进销存记忆_processed", "blogger_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "短视频ai新闻prompt完美版_improved_processed", "type Engligh改成人工智能学习工具_processed", "区块链大模型构想_processed", "项目笔记_processed", "项目结构树_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed", "logo设计_processed"]}, "type Engligh改成人工智能学习工具_processed": {"id": "type Engligh改成人工智能学习工具_processed", "name": "type Engligh改成人工智能学习工具.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\type Engligh改成人工智能学习工具.txt", "processed_time": "2025-07-04T23:28:56.227875", "file_type": ".txt", "size_chars": 89, "size_bytes": 222, "keywords": ["将英语学习工具esay", "word变化成人工智能知识的学习工具", "学习模式不变", "只是更好内容", "当然还能拿来学习编程基础", "以及配合ewanknowleage的元认知学习法一起来使用"], "summary": "将英语学习工具Esay word变化成人工智能知识的学习工具，学习模式不变，只是更好内容. 当然还能拿来学习编程基础，以及配合Ewanknowleage的元认知学习法一起来使用", "category": "学习笔记", "topics": ["学习", "人工智能"], "importance": 2, "tags": ["笔记", "AI"], "related_documents": ["个人简介_improved_processed", "代码示例_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "blogger_processed", "短视频ai新闻prompt完美版_improved_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "项目笔记_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "个人简介_improved_processed": {"id": "个人简介_improved_processed", "name": "个人简介.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\个人简介.docx", "processed_time": "2025-07-05T00:28:42.917508", "file_type": ".docx", "size_chars": 1645, "size_bytes": 16098, "keywords": ["2021", "个人简介", "2023", "ewan", "cosmos", "ai时代已经来临", "碳基生命无法与硅基生命竞争", "关键在于如何学好", "用好ai", "人类相较于ai的本质优势在于灵气与智慧"], "summary": "[Word文档] 个人简介. docx\n\n个人简介\n姓名：王宇（Ewan Cosmos）\n理念：AI时代已经来临，碳基生命无法与硅基生命竞争，关键在于如何学好、用好AI", "category": "技术文档", "topics": ["编程", "人工智能"], "importance": 10, "tags": ["技术", "AI"], "related_documents": ["代码示例_processed", "data流程cursor生成_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "blogger_processed", "短视频ai新闻prompt完美版_improved_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "type Engligh改成人工智能学习工具_processed", "项目笔记_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "代码示例_processed": {"id": "代码示例_processed", "name": "代码示例.py", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\代码示例.py", "processed_time": "2025-07-04T23:31:02.978442", "file_type": ".py", "size_chars": 1589, "size_bytes": 1876, "keywords": ["self", "request", "task_type", "await", "return", "def", "local_models", "import", "dict", "any"], "summary": "\"\"\"\n混合AI系统核心代码示例\n演示智能任务路由和模型管理\n\"\"\"\n\nimport asyncio\nimport torch\nfrom typing import Dict, Any\n\nclass HybridAIManager:\n    \"\"\"混合AI管理器\"\"\"\n    \n    def __init__(self):\n        self. local_models = {}\n    ...", "category": "技术文档", "topics": ["编程", "人工智能"], "importance": 10, "tags": ["技术", "AI"], "related_documents": ["个人简介_improved_processed", "data流程cursor生成_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "blogger_processed", "短视频ai新闻prompt完美版_improved_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "type Engligh改成人工智能学习工具_processed", "项目笔记_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "以太坊信息_improved_processed": {"id": "以太坊信息_improved_processed", "name": "以太坊信息.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\以太坊信息.docx", "processed_time": "2025-07-05T00:28:42.933418", "file_type": ".docx", "size_chars": 1693, "size_bytes": 11492, "keywords": ["100", "eth", "gas", "base", "limit", "word文档", "以太坊信息", "docx", "available", "accounts"], "summary": "[Word文档] 以太坊信息. docx\n\nAvailable Accounts\n==================\n(0) ****************************************** (100 ETH)\n(1) ****************************************** (100 ETH)\n(2) 0x0d08Ec4F806c58F28bD0...", "category": "其他", "topics": ["人工智能"], "importance": 10, "tags": ["AI"], "related_documents": ["个人简介_improved_processed", "代码示例_processed", "qwen cli_processed", "进销存记忆_processed", "blogger_processed", "短视频ai新闻prompt完美版_improved_processed", "区块链大模型构想_processed", "项目笔记_processed", "29条工程提示词简介及案例_processed", "Ewandata项目需求英文描述_processed", "测试文档1_processed", "type Engligh改成人工智能学习工具_processed", "博客_processed", "ima网址信息_processed", "厨房agent有啥吃啥计划_improved_processed", "brain记忆文档_improved_processed", "项目结构树_processed", "ai自动化记忆_processed", "logo设计_processed"]}, "你猜我做_processed": {"id": "你猜我做_processed", "name": "你猜我做.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\你猜我做.txt", "processed_time": "2025-07-04T23:28:56.241167", "file_type": ".txt", "size_chars": 370, "size_bytes": 1076, "keywords": ["我现在正在阿里百炼创建一个自己的应用智能体", "这次的创建目标如下", "主题有啥吃啥", "不知道今天吃点啥", "你就拿手机输入文字或拍图片把家里有的食材通通上传", "我来把他们变成你今天美味的食物搭配", "分为低质和高热量2种选择", "食谱选择米其林大师和中式专业食谱", "流程简洁清晰", "食材和配料的用量要已g为单位"], "summary": "我现在正在阿里百炼创建一个自己的应用智能体，这次的创建目标如下：1. 主题有啥吃啥，不知道今天吃点啥，你就拿手机输入文字或拍图片把家里有的食材通通上传，我来把他们变成你今天美味的食物搭配，分为低质和高热量2种选择", "category": "项目管理", "topics": ["管理"], "importance": 4, "tags": ["项目"], "related_documents": ["厨房agent_processed", "短视频ai新闻prompt完美版_improved_processed", "厨房agent有啥吃啥计划_improved_processed", "测试文档1_processed", "项目结构树_processed"]}, "区块链大模型构想_processed": {"id": "区块链大模型构想_processed", "name": "区块链大模型构想.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\区块链大模型构想.docx", "processed_time": "2025-07-05T00:24:56.952975", "file_type": ".docx", "size_chars": 48, "size_bytes": 0, "keywords": ["file", "word处理错误", "区块链大模型构想", "docx", "not", "zip"], "summary": "[Word处理错误] 区块链大模型构想. docx: File is not a zip file", "category": "其他", "topics": [], "importance": 1, "tags": [], "related_documents": ["ima网址信息_processed", "qwen cli_processed", "进销存记忆_processed", "厨房agent有啥吃啥计划_improved_processed", "brain记忆文档_improved_processed", "项目结构树_processed", "博客_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed", "logo设计_processed"]}, "博客_processed": {"id": "博客_processed", "name": "博客.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\博客.txt", "processed_time": "2025-07-04T23:28:56.251520", "file_type": ".txt", "size_chars": 10, "size_bytes": 29, "keywords": ["豆包可以生成博客了"], "summary": "豆包可以生成博客了", "category": "其他", "topics": [], "importance": 1, "tags": [], "related_documents": ["ima网址信息_processed", "进销存记忆_processed", "区块链大模型构想_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed", "logo设计_processed"]}, "厨房agent_processed": {"id": "厨房agent_processed", "name": "厨房agent.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\厨房agent.txt", "processed_time": "2025-07-04T23:28:56.255531", "file_type": ".txt", "size_chars": 601, "size_bytes": 1743, "keywords": ["我想到一些问题需要补充提醒一下你", "既然是智能体", "就一定要区别于传统的视频教学app", "一定要和用户之间有客户舒服的较为密切的交互", "比如先问他今天想吃啥", "冰箱里有啥", "更想吃什么口味的食物", "愿意去采购啥", "这样的互动来确认我们的食材操作空间", "然后要考虑到一般的家庭厨房调味料不会齐全"], "summary": "我想到一些问题需要补充提醒一下你：1. 既然是智能体，就一定要区别于传统的视频教学app，一定要和用户之间有客户舒服的较为密切的交互，比如先问他今天想吃啥，冰箱里有啥，更想吃什么口味的食物，愿意去采购啥", "category": "项目管理", "topics": ["管理"], "importance": 7, "tags": ["项目"], "related_documents": ["你猜我做_processed", "短视频ai新闻prompt完美版_improved_processed", "厨房agent有啥吃啥计划_improved_processed", "测试文档1_processed", "项目结构树_processed"]}, "厨房agent有啥吃啥计划_improved_processed": {"id": "厨房agent有啥吃啥计划_improved_processed", "name": "厨房agent有啥吃啥计划.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\厨房agent有啥吃啥计划.docx", "processed_time": "2025-07-05T00:28:42.862333", "file_type": ".docx", "size_chars": 633, "size_bytes": 11446, "keywords": ["word文档", "厨房agent有啥吃啥计划", "docx", "我想到一些问题需要补充提醒一下你", "既然是智能体", "就一定要区别于传统的视频教学app", "一定要和用户之间有客户舒服的较为密切的交互", "比如先问他今天想吃啥", "冰箱里有啥", "更想吃什么口味的食物"], "summary": "[Word文档] 厨房agent有啥吃啥计划. docx\n\n我想到一些问题需要补充提醒一下你：1", "category": "项目管理", "topics": ["管理"], "importance": 7, "tags": ["项目"], "related_documents": ["厨房agent_processed", "你猜我做_processed", "进销存记忆_processed", "短视频ai新闻prompt完美版_improved_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "区块链大模型构想_processed", "项目结构树_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed"]}, "测试文档1_processed": {"id": "测试文档1_processed", "name": "测试文档1.txt", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\测试文档1.txt", "processed_time": "2025-07-04T23:31:02.466001", "file_type": ".txt", "size_chars": 416, "size_bytes": 864, "keywords": ["ewandata混合ai系统测试文档", "这是一个用于测试文件监控和ai处理能力的示例文档", "系统概述", "ewandata是一个创新的混合ai架构智能知识管理系统", "专为rtx", "4070", "gpu优化设计", "核心特性", "本地多模型协同", "qwen2"], "summary": "Ewandata混合AI系统测试文档\n\n这是一个用于测试文件监控和AI处理能力的示例文档. 系统概述：\nEwandata是一个创新的混合AI架构智能知识管理系统，专为RTX 4070 GPU优化设计", "category": "项目管理", "topics": ["管理", "人工智能"], "importance": 6, "tags": ["项目", "AI"], "related_documents": ["个人简介_improved_processed", "厨房agent_processed", "你猜我做_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "代码示例_processed", "厨房agent有啥吃啥计划_improved_processed", "短视频ai新闻prompt完美版_improved_processed", "blogger_processed", "brain记忆文档_improved_processed", "type Engligh改成人工智能学习工具_processed", "项目笔记_processed", "项目结构树_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "短视频ai新闻prompt完美版_improved_processed": {"id": "短视频ai新闻prompt完美版_improved_processed", "name": "短视频ai新闻prompt完美版.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\短视频ai新闻prompt完美版.docx", "processed_time": "2025-07-05T00:28:42.977748", "file_type": ".docx", "size_chars": 1774, "size_bytes": 16021, "keywords": ["星际之门", "心理学应用", "人性的弱点", "business", "insider", "一旦agi实现", "agents元年", "的建成", "agents", "human"], "summary": "[Word文档] 短视频ai新闻prompt完美版. docx\n\n结构化Prompt：增强版\n角色：\n“你是一位精通人工智能动态的科技媒体创作者，熟悉全球AI竞争与技术趋势，同时深入研究卡耐基的《人性的弱点》、勒庞的《乌合之众》等心理学经典，擅长通过人性洞察提升内容的感染力和逻辑深度", "category": "项目管理", "topics": ["管理", "人工智能"], "importance": 10, "tags": ["项目", "AI"], "related_documents": ["个人简介_improved_processed", "厨房agent_processed", "你猜我做_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "代码示例_processed", "厨房agent有啥吃啥计划_improved_processed", "blogger_processed", "测试文档1_processed", "brain记忆文档_improved_processed", "type Engligh改成人工智能学习工具_processed", "项目笔记_processed", "项目结构树_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "进销存记忆_processed": {"id": "进销存记忆_processed", "name": "进销存记忆.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\进销存记忆.docx", "processed_time": "2025-07-05T00:24:56.957540", "file_type": ".docx", "size_chars": 45, "size_bytes": 0, "keywords": ["file", "word处理错误", "进销存记忆", "docx", "not", "zip"], "summary": "[Word处理错误] 进销存记忆. docx: File is not a zip file", "category": "其他", "topics": [], "importance": 1, "tags": [], "related_documents": ["ima网址信息_processed", "qwen cli_processed", "厨房agent有啥吃啥计划_improved_processed", "brain记忆文档_improved_processed", "区块链大模型构想_processed", "项目结构树_processed", "博客_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed", "logo设计_processed"]}, "项目笔记_processed": {"id": "项目笔记_processed", "name": "项目笔记.md", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\项目笔记.md", "processed_time": "2025-07-04T23:31:03.515030", "file_type": ".md", "size_chars": 715, "size_bytes": 1128, "keywords": ["bitsandbytesconfig", "true", "30s", "项目开发笔记", "今日工作总结", "2025", "完成的任务", "混合ai架构设计和实现", "rtx", "4070"], "summary": "# 项目开发笔记\n\n## 今日工作总结 (2025-01-03)\n\n### 完成的任务\n- [x] 混合AI架构设计和实现\n- [x] RTX 4070 GPU优化配置\n- [x] 智能任务路由机制开发\n- [x] 成本优化器实现\n- [x] 文件监控服务测试\n\n### 技术要点\n\n#### 模型量化优化\n使用4-bit量化技术将7B参数模型压缩到4GB显存：\n```python\nfrom tra...", "category": "技术文档", "topics": ["编程", "人工智能"], "importance": 9, "tags": ["技术", "AI"], "related_documents": ["个人简介_improved_processed", "代码示例_processed", "data流程cursor生成_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "blogger_processed", "短视频ai新闻prompt完美版_improved_processed", "测试文档1_processed", "brain记忆文档_improved_processed", "type Engligh改成人工智能学习工具_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "logo设计_processed"]}, "项目结构树_processed": {"id": "项目结构树_processed", "name": "项目结构树.docx", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\提示词\\项目结构树.docx", "processed_time": "2025-07-05T00:24:56.963768", "file_type": ".docx", "size_chars": 45, "size_bytes": 0, "keywords": ["file", "word处理错误", "项目结构树", "docx", "not", "zip"], "summary": "[Word处理错误] 项目结构树. docx: File is not a zip file", "category": "项目管理", "topics": ["管理"], "importance": 1, "tags": ["项目"], "related_documents": ["厨房agent_processed", "你猜我做_processed", "qwen cli_processed", "进销存记忆_processed", "厨房agent有啥吃啥计划_improved_processed", "brain记忆文档_improved_processed", "测试文档1_processed", "短视频ai新闻prompt完美版_improved_processed", "区块链大模型构想_processed", "ai自动化记忆_processed", "以太坊信息_improved_processed", "29条工程提示词简介及案例_processed"]}, "image_FgnXjQgVApiXzexA0t1Hk36LBbxX": {"id": "image_FgnXjQgVApiXzexA0t1Hk36LBbxX", "name": "FgnXjQgVApiXzexA0t1Hk36LBbxX.png", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\占冰强老师笔记\\FgnXjQgVApiXzexA0t1Hk36LBbxX.png", "processed_time": "2025-07-05T01:07:14.152731", "file_type": "image", "size_chars": 223, "size_bytes": 573601, "keywords": [], "summary": "[模拟OCR结果] 图像内容\n文件名: FgnXjQgVApiXzexA0t1Hk36LBbxX\n文件大小: 560. 2 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。", "category": "图像文档", "topics": ["图像处理", "图表图解"], "importance": 6, "tags": ["OCR", "图像"], "content_type": "diagram", "ocr_confidence": 0.8, "extracted_text": "[模拟OCR结果] 图像内容\n文件名: FgnXjQgVApiXzexA0t1Hk36LBbxX\n文件大小: 560.2 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。\n", "related_documents": []}, "image_微信截图_20250222193800": {"id": "image_微信截图_20250222193800", "name": "微信截图_20250222193800.png", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\1_1_桌面文件包\\桌面文件包\\占冰强老师笔记\\微信截图_20250222193800.png", "processed_time": "2025-07-05T01:07:14.164440", "file_type": "image", "size_chars": 214, "size_bytes": 537378, "keywords": [], "summary": "[模拟OCR结果] 图像内容\n文件名: 微信截图_20250222193800\n文件大小: 524. 8 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。", "category": "图像文档", "topics": ["图像处理", "图表图解"], "importance": 6, "tags": ["OCR", "图像"], "content_type": "diagram", "ocr_confidence": 0.8, "extracted_text": "[模拟OCR结果] 图像内容\n文件名: 微信截图_20250222193800\n文件大小: 524.8 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。\n", "related_documents": []}, "image_31": {"id": "image_31", "name": "31.png", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\31.png", "processed_time": "2025-07-05T01:07:14.172130", "file_type": "image", "size_chars": 197, "size_bytes": 525894, "keywords": [], "summary": "[模拟OCR结果] 图像内容\n文件名: 31\n文件大小: 513. 6 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。", "category": "图像文档", "topics": ["图像处理", "图表图解"], "importance": 5, "tags": ["OCR", "图像"], "content_type": "diagram", "ocr_confidence": 0.8, "extracted_text": "[模拟OCR结果] 图像内容\n文件名: 31\n文件大小: 513.6 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。\n", "related_documents": []}, "image_36": {"id": "image_36", "name": "36.png", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\36.png", "processed_time": "2025-07-05T01:07:14.179741", "file_type": "image", "size_chars": 197, "size_bytes": 483634, "keywords": [], "summary": "[模拟OCR结果] 图像内容\n文件名: 36\n文件大小: 472. 3 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。", "category": "图像文档", "topics": ["图像处理", "图表图解"], "importance": 5, "tags": ["OCR", "图像"], "content_type": "diagram", "ocr_confidence": 0.8, "extracted_text": "[模拟OCR结果] 图像内容\n文件名: 36\n文件大小: 472.3 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。\n", "related_documents": []}, "image_微信截图_20250630191549": {"id": "image_微信截图_20250630191549", "name": "微信截图_20250630191549.png", "original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\微信截图_20250630191549.png", "processed_time": "2025-07-05T01:07:14.188004", "file_type": "image", "size_chars": 214, "size_bytes": 352516, "keywords": [], "summary": "[模拟OCR结果] 图像内容\n文件名: 微信截图_20250630191549\n文件大小: 344. 3 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。", "category": "图像文档", "topics": ["图像处理", "图表图解"], "importance": 6, "tags": ["OCR", "图像"], "content_type": "diagram", "ocr_confidence": 0.8, "extracted_text": "[模拟OCR结果] 图像内容\n文件名: 微信截图_20250630191549\n文件大小: 344.3 KB\n\n这是一个图像文件，可能包含：\n- 文档扫描件\n- 照片中的文字\n- 图表或图解\n- 其他视觉内容\n\n模拟提取的文本内容：\n根据文件特征分析，此图像可能包含文字信息。\n建议使用专业OCR工具进行准确识别。\n\n注意：这是模拟OCR结果，实际内容可能不同。\n请安装Tesseract OCR引擎以获得真实的文字识别结果。\n", "related_documents": []}}, "relationships": {"keyword_based": {}, "topic_based": {}, "category_based": {}}, "cross_references": {}, "search_index": {"by_keyword": {"file": ["29条工程提示词简介及案例_processed", "ai自动化记忆_processed", "qwen cli_processed", "区块链大模型构想_processed", "进销存记忆_processed", "项目结构树_processed"], "word处理错误": ["29条工程提示词简介及案例_processed", "ai自动化记忆_processed", "区块链大模型构想_processed", "进销存记忆_processed", "项目结构树_processed"], "29条工程提示词简介及案例": ["29条工程提示词简介及案例_processed"], "docx": ["29条工程提示词简介及案例_processed", "ai自动化记忆_processed", "brain记忆文档_improved_processed", "以太坊信息_improved_processed", "区块链大模型构想_processed", "厨房agent有啥吃啥计划_improved_processed", "进销存记忆_processed", "项目结构树_processed"], "not": ["29条工程提示词简介及案例_processed", "ai自动化记忆_processed", "区块链大模型构想_processed", "进销存记忆_processed", "项目结构树_processed"], "zip": ["29条工程提示词简介及案例_processed", "ai自动化记忆_processed", "区块链大模型构想_processed", "进销存记忆_processed", "项目结构树_processed"], "ai自动化记忆": ["ai自动化记忆_processed"], "whisper": ["blogger_processed"], "import": ["blogger_processed", "qwen cli_processed", "代码示例_processed"], "model": ["blogger_processed"], "transcript": ["blogger_processed"], "video": ["blogger_processed"], "large": ["blogger_processed"], "result": ["blogger_processed"], "mp4": ["blogger_processed"], "ollama": ["blogger_processed"], "ffmpeg": ["blogger_processed"], "three": ["brain记忆文档_improved_processed"], "react": ["brain记忆文档_improved_processed"], "drei": ["brain记忆文档_improved_processed"], "fiber": ["brain记忆文档_improved_processed"], "node_modules": ["brain记忆文档_improved_processed"], "word文档": ["brain记忆文档_improved_processed", "以太坊信息_improved_processed", "厨房agent有啥吃啥计划_improved_processed"], "brain记忆文档": ["brain记忆文档_improved_processed"], "以下是您当前项目的关键信息梳理及执行流程概述": ["brain记忆文档_improved_processed"], "供新ai对话框快速了解已完成的服务进度": ["brain记忆文档_improved_processed"], "系统架构设计": ["data流程cursor生成_processed"], "部署方式": ["data流程cursor生成_processed"], "python": ["data流程cursor生成_processed", "qwen cli_processed"], "轻量级容器化": ["data流程cursor生成_processed"], "我建议使用python作为主要开发语言": ["data流程cursor生成_processed"], "结合docker进行部分服务的容器化": ["data流程cursor生成_processed"], "主要应用使用python开发": ["data流程cursor生成_processed"], "便于快速迭代和调试": ["data流程cursor生成_processed"], "将llama模型服务和数据库等稳定组件容器化": ["data流程cursor生成_processed"], "确保环境一致性": ["data流程cursor生成_processed"], "ewandata": ["Ewandata项目需求英文描述_processed"], "4070": ["Ewandata项目需求英文描述_processed", "测试文档1_processed", "项目笔记_processed"], "phi": ["Ewandata项目需求英文描述_processed"], "主题分类": ["Ewandata项目需求英文描述_processed"], "http": ["Ewandata项目需求英文描述_processed"], "localhost": ["Ewandata项目需求英文描述_processed"], "帮我创建一个本地知识库项目": ["Ewandata项目需求英文描述_processed"], "名字叫ewandata路径在e": ["Ewandata项目需求英文描述_processed"], "它的主要作用就是成为一个知道我所有数字信息的ai数字系统": ["Ewandata项目需求英文描述_processed"], "也是我最好的私人助理和工作搭档": ["Ewandata项目需求英文描述_processed"], "https": ["ima网址信息_processed"], "ima": ["ima网址信息_processed"], "com": ["ima网址信息_processed"], "logo自动设计ai": ["logo设计_processed"], "写字做logo": ["logo设计_processed"], "写需求描述做logo": ["logo设计_processed"], "llama3": ["qwen cli_processed"], "def": ["qwen cli_processed", "代码示例_processed"], "print": ["qwen cli_processed"], "path": ["qwen cli_processed"], "get": ["qwen cli_processed"], "params": ["qwen cli_processed"], "json": ["qwen cli_processed"], "将英语学习工具esay": ["type Engligh改成人工智能学习工具_processed"], "word变化成人工智能知识的学习工具": ["type Engligh改成人工智能学习工具_processed"], "学习模式不变": ["type Engligh改成人工智能学习工具_processed"], "只是更好内容": ["type Engligh改成人工智能学习工具_processed"], "当然还能拿来学习编程基础": ["type Engligh改成人工智能学习工具_processed"], "以及配合ewanknowleage的元认知学习法一起来使用": ["type Engligh改成人工智能学习工具_processed"], "2021": ["个人简介_improved_processed"], "个人简介": ["个人简介_improved_processed"], "2023": ["个人简介_improved_processed"], "ewan": ["个人简介_improved_processed"], "cosmos": ["个人简介_improved_processed"], "ai时代已经来临": ["个人简介_improved_processed"], "碳基生命无法与硅基生命竞争": ["个人简介_improved_processed"], "关键在于如何学好": ["个人简介_improved_processed"], "用好ai": ["个人简介_improved_processed"], "人类相较于ai的本质优势在于灵气与智慧": ["个人简介_improved_processed"], "self": ["代码示例_processed"], "request": ["代码示例_processed"], "task_type": ["代码示例_processed"], "await": ["代码示例_processed"], "return": ["代码示例_processed"], "local_models": ["代码示例_processed"], "dict": ["代码示例_processed"], "any": ["代码示例_processed"], "100": ["以太坊信息_improved_processed"], "eth": ["以太坊信息_improved_processed"], "gas": ["以太坊信息_improved_processed"], "base": ["以太坊信息_improved_processed"], "limit": ["以太坊信息_improved_processed"], "以太坊信息": ["以太坊信息_improved_processed"], "available": ["以太坊信息_improved_processed"], "accounts": ["以太坊信息_improved_processed"], "我现在正在阿里百炼创建一个自己的应用智能体": ["你猜我做_processed"], "这次的创建目标如下": ["你猜我做_processed"], "主题有啥吃啥": ["你猜我做_processed"], "不知道今天吃点啥": ["你猜我做_processed"], "你就拿手机输入文字或拍图片把家里有的食材通通上传": ["你猜我做_processed"], "我来把他们变成你今天美味的食物搭配": ["你猜我做_processed"], "分为低质和高热量2种选择": ["你猜我做_processed"], "食谱选择米其林大师和中式专业食谱": ["你猜我做_processed"], "流程简洁清晰": ["你猜我做_processed"], "食材和配料的用量要已g为单位": ["你猜我做_processed"], "区块链大模型构想": ["区块链大模型构想_processed"], "豆包可以生成博客了": ["博客_processed"], "我想到一些问题需要补充提醒一下你": ["厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed"], "既然是智能体": ["厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed"], "就一定要区别于传统的视频教学app": ["厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed"], "一定要和用户之间有客户舒服的较为密切的交互": ["厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed"], "比如先问他今天想吃啥": ["厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed"], "冰箱里有啥": ["厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed"], "更想吃什么口味的食物": ["厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed"], "愿意去采购啥": ["厨房agent_processed"], "这样的互动来确认我们的食材操作空间": ["厨房agent_processed"], "然后要考虑到一般的家庭厨房调味料不会齐全": ["厨房agent_processed"], "厨房agent有啥吃啥计划": ["厨房agent有啥吃啥计划_improved_processed"], "ewandata混合ai系统测试文档": ["测试文档1_processed"], "这是一个用于测试文件监控和ai处理能力的示例文档": ["测试文档1_processed"], "系统概述": ["测试文档1_processed"], "ewandata是一个创新的混合ai架构智能知识管理系统": ["测试文档1_processed"], "专为rtx": ["测试文档1_processed"], "gpu优化设计": ["测试文档1_processed"], "核心特性": ["测试文档1_processed"], "本地多模型协同": ["测试文档1_processed"], "qwen2": ["测试文档1_processed"], "星际之门": ["短视频ai新闻prompt完美版_improved_processed"], "心理学应用": ["短视频ai新闻prompt完美版_improved_processed"], "人性的弱点": ["短视频ai新闻prompt完美版_improved_processed"], "business": ["短视频ai新闻prompt完美版_improved_processed"], "insider": ["短视频ai新闻prompt完美版_improved_processed"], "一旦agi实现": ["短视频ai新闻prompt完美版_improved_processed"], "agents元年": ["短视频ai新闻prompt完美版_improved_processed"], "的建成": ["短视频ai新闻prompt完美版_improved_processed"], "agents": ["短视频ai新闻prompt完美版_improved_processed"], "human": ["短视频ai新闻prompt完美版_improved_processed"], "进销存记忆": ["进销存记忆_processed"], "bitsandbytesconfig": ["项目笔记_processed"], "true": ["项目笔记_processed"], "30s": ["项目笔记_processed"], "项目开发笔记": ["项目笔记_processed"], "今日工作总结": ["项目笔记_processed"], "2025": ["项目笔记_processed"], "完成的任务": ["项目笔记_processed"], "混合ai架构设计和实现": ["项目笔记_processed"], "rtx": ["项目笔记_processed"], "项目结构树": ["项目结构树_processed"]}, "by_topic": {"人工智能": ["ai自动化记忆_processed", "blogger_processed", "brain记忆文档_improved_processed", "Ewandata项目需求英文描述_processed", "logo设计_processed", "qwen cli_processed", "type Engligh改成人工智能学习工具_processed", "个人简介_improved_processed", "代码示例_processed", "以太坊信息_improved_processed", "测试文档1_processed", "短视频ai新闻prompt完美版_improved_processed", "项目笔记_processed"], "编程": ["blogger_processed", "brain记忆文档_improved_processed", "data流程cursor生成_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "个人简介_improved_processed", "代码示例_processed", "项目笔记_processed"], "学习": ["type Engligh改成人工智能学习工具_processed"], "管理": ["你猜我做_processed", "厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed", "测试文档1_processed", "短视频ai新闻prompt完美版_improved_processed", "项目结构树_processed"]}, "by_category": {"其他": ["29条工程提示词简介及案例_processed", "ai自动化记忆_processed", "ima网址信息_processed", "logo设计_processed", "以太坊信息_improved_processed", "区块链大模型构想_processed", "博客_processed", "进销存记忆_processed"], "技术文档": ["blogger_processed", "brain记忆文档_improved_processed", "data流程cursor生成_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "个人简介_improved_processed", "代码示例_processed", "项目笔记_processed"], "学习笔记": ["type Engligh改成人工智能学习工具_processed"], "项目管理": ["你猜我做_processed", "厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed", "测试文档1_processed", "短视频ai新闻prompt完美版_improved_processed", "项目结构树_processed"]}, "by_importance": {"low": ["29条工程提示词简介及案例_processed", "ai自动化记忆_processed", "data流程cursor生成_processed", "ima网址信息_processed", "logo设计_processed", "type Engligh改成人工智能学习工具_processed", "你猜我做_processed", "区块链大模型构想_processed", "博客_processed", "进销存记忆_processed", "项目结构树_processed"], "high": ["blogger_processed", "brain记忆文档_improved_processed", "Ewandata项目需求英文描述_processed", "qwen cli_processed", "个人简介_improved_processed", "代码示例_processed", "以太坊信息_improved_processed", "厨房agent_processed", "厨房agent有啥吃啥计划_improved_processed", "短视频ai新闻prompt完美版_improved_processed", "项目笔记_processed"], "medium": ["测试文档1_processed"]}}}