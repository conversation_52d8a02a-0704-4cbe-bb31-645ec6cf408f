graph TB
    %% 文档关系网络图

    %% 定义样式
    classDef highImportance fill:#ff6b6b,stroke:#d63031,stroke-width:3px,color:#fff
    classDef mediumImportance fill:#fdcb6e,stroke:#e17055,stroke-width:2px,color:#2d3436
    classDef lowImportance fill:#74b9ff,stroke:#0984e3,stroke-width:1px,color:#2d3436
    classDef category fill:#6c5ce7,stroke:#5f3dc4,stroke-width:2px,color:#fff
    classDef topic fill:#00b894,stroke:#00a085,stroke-width:2px,color:#fff
    classDef keyword fill:#fd79a8,stroke:#e84393,stroke-width:2px,color:#fff

    %% 分类节点
    CAT_其他["📁 其他<br/>8个文档"]
    CAT_技术文档["📁 技术文档<br/>8个文档"]
    CAT_项目管理["📁 项目管理<br/>6个文档"]

    %% 主题节点
    TOPIC_人工智能["🏷️ 人工智能<br/>13个文档"]
    TOPIC_编程["🏷️ 编程<br/>8个文档"]
    TOPIC_管理["🏷️ 管理<br/>6个文档"]

    %% 文档节点
    DOC_blogger_txt["📄 blogger.txt<br/>重要性: 10/10"]
    DOC_brain记忆文档_docx["📄 brain记忆文档.docx<br/>重要性: 10/10"]
    DOC_Ewandata项目需求英文描述_txt["📄 Ewandata项目需求英文描述.txt<br/>重要性: 10/10"]
    DOC_qwen_cli_txt["📄 qwen cli.txt<br/>重要性: 10/10"]
    DOC_个人简介_docx["📄 个人简介.docx<br/>重要性: 10/10"]
    DOC_代码示例_py["📄 代码示例.py<br/>重要性: 10/10"]
    DOC_以太坊信息_docx["📄 以太坊信息.docx<br/>重要性: 10/10"]
    DOC_厨房agent_txt["📄 厨房agent.txt<br/>重要性: 7/10"]
    DOC_厨房agent有啥吃啥计划_docx["📄 厨房agent有啥吃啥计划.docx<br/>重要性: 7/10"]
    DOC_短视频ai新闻prompt完美版_doc["📄 短视频ai新闻prompt完美版.doc...<br/>重要性: 10/10"]
    DOC_项目笔记_md["📄 项目笔记.md<br/>重要性: 9/10"]

    %% 关系连接
    DOC_以太坊信息_docx --> CAT_其他
    DOC_blogger_txt --> CAT_技术文档
    DOC_brain记忆文档_docx --> CAT_技术文档
    DOC_Ewandata项目需求英文描述_txt --> CAT_技术文档
    DOC_qwen_cli_txt --> CAT_技术文档
    DOC_个人简介_docx --> CAT_技术文档
    DOC_代码示例_py --> CAT_技术文档
    DOC_项目笔记_md --> CAT_技术文档
    DOC_厨房agent_txt --> CAT_项目管理
    DOC_厨房agent有啥吃啥计划_docx --> CAT_项目管理
    DOC_短视频ai新闻prompt完美版_doc --> CAT_项目管理
    DOC_blogger_txt --> TOPIC_人工智能
    DOC_brain记忆文档_docx --> TOPIC_人工智能
    DOC_Ewandata项目需求英文描述_txt --> TOPIC_人工智能
    DOC_qwen_cli_txt --> TOPIC_人工智能
    DOC_个人简介_docx --> TOPIC_人工智能
    DOC_代码示例_py --> TOPIC_人工智能
    DOC_以太坊信息_docx --> TOPIC_人工智能
    DOC_短视频ai新闻prompt完美版_doc --> TOPIC_人工智能
    DOC_项目笔记_md --> TOPIC_人工智能
    DOC_blogger_txt --> TOPIC_编程
    DOC_brain记忆文档_docx --> TOPIC_编程
    DOC_Ewandata项目需求英文描述_txt --> TOPIC_编程
    DOC_qwen_cli_txt --> TOPIC_编程
    DOC_个人简介_docx --> TOPIC_编程
    DOC_代码示例_py --> TOPIC_编程
    DOC_项目笔记_md --> TOPIC_编程
    DOC_厨房agent_txt --> TOPIC_管理
    DOC_厨房agent有啥吃啥计划_docx --> TOPIC_管理
    DOC_短视频ai新闻prompt完美版_doc --> TOPIC_管理

    %% 应用样式
    class DOC_blogger_txt highImportance
    class DOC_brain记忆文档_docx highImportance
    class DOC_Ewandata项目需求英文描述_txt highImportance
    class DOC_qwen_cli_txt highImportance
    class DOC_个人简介_docx highImportance
    class DOC_代码示例_py highImportance
    class DOC_以太坊信息_docx highImportance
    class DOC_厨房agent_txt mediumImportance
    class DOC_厨房agent有啥吃啥计划_docx mediumImportance
    class DOC_短视频ai新闻prompt完美版_doc highImportance
    class DOC_项目笔记_md highImportance
    class CAT_其他 category
    class CAT_技术文档 category
    class CAT_项目管理 category
    class TOPIC_人工智能 topic
    class TOPIC_编程 topic
    class TOPIC_管理 topic
