"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class HeightConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USE_STRETCH_FIELD_NUMBER: builtins.int
    USE_CONTENT_FIELD_NUMBER: builtins.int
    PIXEL_HEIGHT_FIELD_NUMBER: builtins.int
    use_stretch: builtins.bool
    use_content: builtins.bool
    pixel_height: builtins.int
    def __init__(
        self,
        *,
        use_stretch: builtins.bool = ...,
        use_content: builtins.bool = ...,
        pixel_height: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["height_spec", b"height_spec", "pixel_height", b"pixel_height", "use_content", b"use_content", "use_stretch", b"use_stretch"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["height_spec", b"height_spec", "pixel_height", b"pixel_height", "use_content", b"use_content", "use_stretch", b"use_stretch"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["height_spec", b"height_spec"]) -> typing.Literal["use_stretch", "use_content", "pixel_height"] | None: ...

global___HeightConfig = HeightConfig
