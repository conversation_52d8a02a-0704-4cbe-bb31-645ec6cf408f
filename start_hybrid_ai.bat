@echo off
echo 🚀 启动Ewandata混合AI智能知识管理系统...
cd /d "%~dp0"

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo 检查GPU状态...
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}'); print(f'GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"CPU模式\"}')"

echo 启动混合AI系统...
python -c "
import asyncio
import sys
sys.path.append('ewandata_system')

async def main():
    try:
        from services.hybrid_ai_manager import get_hybrid_ai_manager
        
        print('🚀 Ewandata混合AI系统启动')
        print('支持本地多模型 + 外部AI智能路由')
        print('按 Ctrl+C 停止系统')
        
        manager = get_hybrid_ai_manager()
        
        # 显示系统状态
        status = await manager.get_system_status()
        print(f'GPU: {status[\"gpu_status\"].get(\"device_name\", \"CPU模式\")}')
        print(f'可用模型: {len(status[\"model_status\"].get(\"models\", {}))}')
        
        # 启动交互式会话
        print('\\n输入问题开始对话 (输入 \"quit\" 退出):')
        
        while True:
            user_input = input('\\n用户: ').strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                break
            
            if not user_input:
                continue
            
            request = {
                'content': user_input,
                'max_tokens': 500
            }
            
            print('AI: 正在思考...')
            result = await manager.process_request(request)
            
            if result['success']:
                print(f'AI: {result[\"result\"][\"content\"]}')
                print(f'(处理方式: {result[\"metadata\"][\"processed_by\"]}, '
                      f'耗时: {result[\"metadata\"][\"response_time\"]:.1f}秒)')
            else:
                print(f'AI: 抱歉，处理失败: {result[\"error\"]}')
    
    except KeyboardInterrupt:
        print('\\n系统停止')
    except Exception as e:
        print(f'系统启动失败: {e}')
        print('请检查依赖安装和配置')
    finally:
        if 'manager' in locals():
            await manager.shutdown()

if __name__ == '__main__':
    asyncio.run(main())
"

pause
