"""
增强的文档处理器
集成AI大模型，提供智能文档分析能力
"""

import logging
from typing import Dict, Any
from pathlib import Path
from datetime import datetime

# 导入原始文档处理器
from .document_processor import DocumentProcessor
from ..services.ai_service import get_ai_service

logger = logging.getLogger(__name__)

class EnhancedDocumentProcessor(DocumentProcessor):
    """增强的文档处理器，集成AI能力"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化增强文档处理器"""
        super().__init__(config)
        self.ai_service = get_ai_service()
        self.enable_ai = config.get('enable_ai', True) if config else True
        
        logger.info(f"增强文档处理器初始化完成，AI功能: {'启用' if self.enable_ai else '禁用'}")
    
    def process_file(self, file_path: str) -> Dict[str, Any]:
        """处理文件（增强版）"""
        # 首先使用基础处理器处理文件
        result = super().process_file(file_path)
        
        if "error" in result:
            return result
        
        # 如果启用AI且有内容，进行AI增强处理
        if self.enable_ai and "content" in result and result["content"]:
            logger.info(f"开始AI增强处理: {Path(file_path).name}")
            
            try:
                # 获取文档内容
                content = result["content"]
                
                # AI增强处理
                ai_data = self.ai_service.enhance_document_data(content, result)
                
                # 合并AI处理结果
                result.update(ai_data)
                
                # 添加AI处理标记
                result["ai_processed"] = True
                result["ai_processing_time"] = datetime.now().isoformat()
                
                logger.info(f"✅ AI增强处理完成: {Path(file_path).name}")
                
            except Exception as e:
                logger.error(f"❌ AI增强处理失败: {e}")
                result["ai_processed"] = False
                result["ai_error"] = str(e)
        else:
            result["ai_processed"] = False
            if not self.enable_ai:
                result["ai_disabled"] = True
        
        return result
    
    def batch_process_files(self, file_paths: list) -> Dict[str, Any]:
        """批量处理文件"""
        results = {}
        total_files = len(file_paths)
        
        logger.info(f"开始批量处理 {total_files} 个文件")
        
        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"处理进度: {i}/{total_files} - {Path(file_path).name}")
            
            try:
                result = self.process_file(file_path)
                results[str(file_path)] = result
                
            except Exception as e:
                logger.error(f"批量处理失败 {file_path}: {e}")
                results[str(file_path)] = {"error": str(e)}
        
        logger.info(f"批量处理完成: {total_files} 个文件")
        return results
    
    def analyze_content_relationships(self, documents: list) -> Dict[str, Any]:
        """分析文档间的关系"""
        if not self.enable_ai:
            return {"error": "AI功能未启用"}
        
        try:
            # 提取所有文档的关键词和主题
            all_keywords = []
            all_topics = []
            doc_summaries = []
            
            for doc in documents:
                if "keywords" in doc:
                    all_keywords.extend(doc["keywords"])
                
                if "classification" in doc and "topics" in doc["classification"]:
                    all_topics.extend(doc["classification"]["topics"])
                
                if "summary" in doc:
                    doc_summaries.append(doc["summary"])
            
            # 使用AI分析关系
            analysis_prompt = f"""
            请分析以下文档集合的关系和模式：
            
            关键词: {', '.join(set(all_keywords))}
            主题: {', '.join(set(all_topics))}
            文档数量: {len(documents)}
            
            请识别：
            1. 共同主题和模式
            2. 知识关联点
            3. 潜在的知识图谱结构
            4. 建议的组织方式
            """
            
            analysis = self.ai_service.generate_response(analysis_prompt, max_new_tokens=400)
            
            return {
                "total_documents": len(documents),
                "unique_keywords": list(set(all_keywords)),
                "unique_topics": list(set(all_topics)),
                "relationship_analysis": analysis,
                "analysis_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"关系分析失败: {e}")
            return {"error": str(e)}
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            "processor_type": "Enhanced with AI",
            "ai_enabled": self.enable_ai,
            "ai_model": self.ai_service.model_name if self.ai_service else "未加载",
            "supported_formats": list(self.supported_formats.keys()),
            "ai_initialized": self.ai_service.is_initialized if self.ai_service else False
        }


def create_enhanced_processor(enable_ai: bool = True) -> EnhancedDocumentProcessor:
    """创建增强文档处理器实例"""
    config = {
        "enable_ai": enable_ai,
        "max_file_size_mb": 100,
        "supported_formats": ['.txt', '.md', '.json', '.csv', '.html']
    }
    
    return EnhancedDocumentProcessor(config)


def test_enhanced_processor():
    """测试增强文档处理器"""
    print("🧠 测试增强文档处理器...")
    
    # 创建测试文件
    test_file = Path("test_ai_document.txt")
    test_content = """
    人工智能技术发展报告
    
    本报告分析了当前人工智能技术的发展趋势，包括：
    1. 大语言模型的突破性进展
    2. 计算机视觉在各行业的应用
    3. 自然语言处理技术的商业化
    4. 机器学习算法的优化
    
    重点关注了深度学习、神经网络、数据挖掘等核心技术。
    同时讨论了AI在医疗、金融、教育等领域的实际应用案例。
    
    未来发展方向包括：
    - 多模态AI系统
    - 边缘计算与AI结合
    - AI安全与伦理
    - 人机协作模式创新
    """
    
    # 写入测试文件
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    try:
        # 创建处理器
        processor = create_enhanced_processor(enable_ai=True)
        
        # 处理文件
        result = processor.process_file(str(test_file))
        
        # 显示结果
        print(f"文件处理结果:")
        print(f"  AI处理: {result.get('ai_processed', False)}")
        print(f"  关键词: {result.get('keywords', [])}")
        print(f"  摘要: {result.get('summary', '无')}")
        print(f"  分类: {result.get('classification', {})}")
        
        # 获取统计信息
        stats = processor.get_processing_stats()
        print(f"\n处理器统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
    finally:
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()


if __name__ == "__main__":
    test_enhanced_processor()
