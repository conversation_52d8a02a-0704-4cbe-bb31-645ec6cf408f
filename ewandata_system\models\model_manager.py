"""
Ewandata AI模型管理器
负责管理和配置本地AI模型，优化RTX 4070 12GB GPU使用
"""

import torch
import logging
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    pipeline,
    BitsAndBytesConfig
)
from sentence_transformers import SentenceTransformer
import gc
from typing import Dict, Any, Optional, List
import os
import json
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelManager:
    """AI模型管理器 - RTX 4070 12GB优化版本"""

    def __init__(self, config_path: str = None):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.models = {}
        self.tokenizers = {}
        self.pipelines = {}

        # GPU内存管理
        self.max_gpu_memory = 12 * 1024 * 1024 * 1024  # 12GB
        self.reserved_memory = 2 * 1024 * 1024 * 1024   # 预留2GB

        # 模型配置
        self.model_configs = {
            "phi3_mini": {
                "model_name": "microsoft/Phi-3-mini-4k-instruct",
                "max_memory": 5 * 1024 * 1024 * 1024,  # 5GB
                "torch_dtype": torch.float16,
                "trust_remote_code": True
            },
            "embedding": {
                "model_name": "sentence-transformers/all-MiniLM-L6-v2",
                "max_memory": 1 * 1024 * 1024 * 1024,  # 1GB
                "device": self.device
            }
        }

        logger.info(f"模型管理器初始化完成，设备: {self.device}")
        if self.device == "cuda":
            logger.info(f"GPU信息: {torch.cuda.get_device_name()}")
            logger.info(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")

    def load_phi3_model(self) -> bool:
        """加载Phi-3-mini模型"""
        try:
            model_name = self.model_configs["phi3_mini"]["model_name"]
            logger.info(f"开始加载Phi-3模型: {model_name}")

            # 配置量化以节省内存
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )

            # 加载tokenizer
            self.tokenizers["phi3"] = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True
            )

            # 加载模型
            self.models["phi3"] = AutoModelForCausalLM.from_pretrained(
                model_name,
                quantization_config=quantization_config,
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )

            logger.info("Phi-3模型加载成功")
            return True

        except Exception as e:
            logger.error(f"Phi-3模型加载失败: {str(e)}")
            return False

    def load_embedding_model(self) -> bool:
        """加载文本嵌入模型"""
        try:
            model_name = self.model_configs["embedding"]["model_name"]
            logger.info(f"开始加载嵌入模型: {model_name}")

            self.models["embedding"] = SentenceTransformer(
                model_name,
                device=self.device
            )

            logger.info("嵌入模型加载成功")
            return True

        except Exception as e:
            logger.error(f"嵌入模型加载失败: {str(e)}")
            return False

    def generate_text(self, prompt: str, max_length: int = 200, temperature: float = 0.7) -> str:
        """使用Phi-3生成文本"""
        if "phi3" not in self.models:
            logger.error("Phi-3模型未加载")
            return ""

        try:
            # 准备输入
            inputs = self.tokenizers["phi3"](prompt, return_tensors="pt").to(self.device)

            # 生成文本
            with torch.no_grad():
                outputs = self.models["phi3"].generate(
                    inputs.input_ids,
                    max_length=max_length,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizers["phi3"].eos_token_id,
                    attention_mask=inputs.attention_mask
                )

            # 解码输出
            generated_text = self.tokenizers["phi3"].decode(
                outputs[0],
                skip_special_tokens=True
            )

            # 移除原始prompt
            if generated_text.startswith(prompt):
                generated_text = generated_text[len(prompt):].strip()

            return generated_text

        except Exception as e:
            logger.error(f"文本生成失败: {str(e)}")
            return ""

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """获取文本嵌入向量"""
        if "embedding" not in self.models:
            logger.error("嵌入模型未加载")
            return []

        try:
            embeddings = self.models["embedding"].encode(texts)
            return embeddings.tolist()

        except Exception as e:
            logger.error(f"获取嵌入向量失败: {str(e)}")
            return []

    def analyze_content(self, content: str) -> Dict[str, Any]:
        """综合内容分析"""
        result = {
            "summary": "",
            "keywords": [],
            "topics": [],
            "importance_score": 0.5,
            "embeddings": []
        }

        try:
            # 生成摘要
            if len(content) > 100:
                summary_prompt = f"请为以下内容生成简洁的摘要（不超过100字）：\n{content[:1000]}"
                result["summary"] = self.generate_text(summary_prompt, max_length=150)
            else:
                result["summary"] = content

            # 提取关键词
            keywords_prompt = f"从以下内容中提取5-8个关键词，用逗号分隔：\n{content[:500]}"
            keywords_text = self.generate_text(keywords_prompt, max_length=100)
            if keywords_text:
                result["keywords"] = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]

            # 主题分类
            topics_prompt = f"为以下内容分类，选择1-3个最相关的主题标签：\n{content[:300]}"
            topics_text = self.generate_text(topics_prompt, max_length=80)
            if topics_text:
                result["topics"] = [topic.strip() for topic in topics_text.split(',') if topic.strip()]

            # 重要性评分
            importance_prompt = f"评估以下内容的重要性，给出0-1之间的分数：\n{content[:200]}"
            importance_text = self.generate_text(importance_prompt, max_length=50)
            try:
                # 尝试从生成的文本中提取数字
                import re
                score_match = re.search(r'0\.\d+|1\.0|0|1', importance_text)
                if score_match:
                    result["importance_score"] = float(score_match.group())
            except:
                result["importance_score"] = 0.5

            # 获取嵌入向量
            result["embeddings"] = self.get_embeddings([content[:1000]])[0] if content else []

            logger.info("内容分析完成")
            return result

        except Exception as e:
            logger.error(f"内容分析失败: {str(e)}")
            return result

    def get_memory_usage(self) -> Dict[str, Any]:
        """获取GPU内存使用情况"""
        if self.device == "cuda":
            return {
                "allocated": torch.cuda.memory_allocated() / 1024**3,
                "cached": torch.cuda.memory_reserved() / 1024**3,
                "max_allocated": torch.cuda.max_memory_allocated() / 1024**3,
                "total": torch.cuda.get_device_properties(0).total_memory / 1024**3
            }
        return {"message": "CPU模式，无GPU内存信息"}

    def clear_cache(self):
        """清理GPU缓存"""
        if self.device == "cuda":
            torch.cuda.empty_cache()
            gc.collect()
            logger.info("GPU缓存已清理")

    def unload_model(self, model_name: str):
        """卸载指定模型"""
        if model_name in self.models:
            del self.models[model_name]
            if model_name in self.tokenizers:
                del self.tokenizers[model_name]
            self.clear_cache()
            logger.info(f"模型 {model_name} 已卸载")

    def initialize_all_models(self) -> bool:
        """初始化所有模型"""
        logger.info("开始初始化所有AI模型...")

        success = True

        # 加载嵌入模型（较小，先加载）
        if not self.load_embedding_model():
            success = False

        # 加载Phi-3模型
        if not self.load_phi3_model():
            success = False

        if success:
            logger.info("所有模型初始化成功")
            logger.info(f"当前GPU内存使用: {self.get_memory_usage()}")
        else:
            logger.error("部分模型初始化失败")

        return success


# 全局模型管理器实例
model_manager = None

def get_model_manager() -> ModelManager:
    """获取全局模型管理器实例"""
    global model_manager
    if model_manager is None:
        model_manager = ModelManager()
    return model_manager


if __name__ == "__main__":
    # 测试代码
    manager = ModelManager()

    print("开始测试模型管理器...")

    # 初始化模型
    if manager.initialize_all_models():
        print("✅ 模型初始化成功")

        # 测试文本生成
        test_prompt = "人工智能的发展趋势是什么？"
        result = manager.generate_text(test_prompt)
        print(f"生成结果: {result}")

        # 测试内容分析
        test_content = "人工智能是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。"
        analysis = manager.analyze_content(test_content)
        print(f"分析结果: {analysis}")

        # 显示内存使用
        memory_info = manager.get_memory_usage()
        print(f"内存使用: {memory_info}")

    else:
        print("❌ 模型初始化失败")