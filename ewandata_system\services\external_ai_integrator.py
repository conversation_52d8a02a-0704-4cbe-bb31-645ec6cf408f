"""
外部AI集成器
通过浏览器自动化和API调用集成高级AI服务
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import random
import time
from pathlib import Path

logger = logging.getLogger(__name__)

class ExternalAIIntegrator:
    """外部AI集成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化外部AI集成器
        
        Args:
            config: 外部AI配置
        """
        self.config = config
        self.providers = {}
        self.usage_stats = {}
        
        self._initialize_providers()
        
        logger.info("外部AI集成器初始化完成")
    
    def _initialize_providers(self):
        """初始化AI提供商"""
        for provider_config in self.config.get('providers', []):
            provider_name = provider_config['name']
            
            if provider_name == 'browser_automation':
                # 使用内嵌的BrowserAIAutomation类
                self.providers[provider_name] = BrowserAIAutomation(provider_config)
            elif provider_name == 'n8n_workflow':
                # 使用内嵌的N8nWorkflowIntegration类
                self.providers[provider_name] = N8nWorkflowIntegration(provider_config)
            elif provider_name == 'free_api_pool':
                # 使用内嵌的FreeAPIPool类
                self.providers[provider_name] = FreeAPIPool(provider_config)
            
            # 初始化使用统计
            self.usage_stats[provider_name] = {
                "daily_usage": 0,
                "total_usage": 0,
                "last_reset": datetime.now().date(),
                "success_rate": 1.0,
                "average_response_time": 0
            }
        
        logger.info(f"初始化了 {len(self.providers)} 个AI提供商")
    
    async def process(self, provider_name: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用指定提供商处理请求
        
        Args:
            provider_name: 提供商名称
            request: 请求内容
            
        Returns:
            处理结果
        """
        if provider_name not in self.providers:
            return {
                "success": False,
                "error": f"未知提供商: {provider_name}",
                "cost": 0
            }
        
        # 检查使用限制
        if not self._check_usage_limit(provider_name):
            return {
                "success": False,
                "error": f"提供商 {provider_name} 已达到每日使用限制",
                "cost": 0
            }
        
        start_time = time.time()
        
        try:
            provider = self.providers[provider_name]
            result = await provider.process_request(request)
            
            # 更新使用统计
            self._update_usage_stats(provider_name, True, time.time() - start_time)
            
            return result
            
        except Exception as e:
            logger.error(f"提供商 {provider_name} 处理失败: {e}")
            
            # 更新失败统计
            self._update_usage_stats(provider_name, False, time.time() - start_time)
            
            return {
                "success": False,
                "error": str(e),
                "cost": 0
            }
    
    def _check_usage_limit(self, provider_name: str) -> bool:
        """检查使用限制"""
        stats = self.usage_stats.get(provider_name, {})
        
        # 重置每日计数
        today = datetime.now().date()
        if stats.get('last_reset') != today:
            stats['daily_usage'] = 0
            stats['last_reset'] = today
        
        # 检查每日限制
        provider_config = None
        for config in self.config.get('providers', []):
            if config['name'] == provider_name:
                provider_config = config
                break
        
        if provider_config:
            daily_limit = provider_config.get('daily_limit', 100)
            return stats['daily_usage'] < daily_limit
        
        return True
    
    def _update_usage_stats(self, provider_name: str, success: bool, response_time: float):
        """更新使用统计"""
        stats = self.usage_stats[provider_name]
        
        stats['daily_usage'] += 1
        stats['total_usage'] += 1
        
        # 更新成功率
        total_requests = stats['total_usage']
        current_success_rate = stats['success_rate']
        new_success_rate = (current_success_rate * (total_requests - 1) + (1 if success else 0)) / total_requests
        stats['success_rate'] = new_success_rate
        
        # 更新平均响应时间
        current_avg_time = stats['average_response_time']
        new_avg_time = (current_avg_time * (total_requests - 1) + response_time) / total_requests
        stats['average_response_time'] = new_avg_time
    
    async def get_best_provider(self, task_type: str, complexity_score: float) -> str:
        """
        根据任务类型和复杂度选择最佳提供商
        
        Args:
            task_type: 任务类型
            complexity_score: 复杂度分数
            
        Returns:
            最佳提供商名称
        """
        available_providers = []
        
        for provider_name, provider in self.providers.items():
            if self._check_usage_limit(provider_name):
                stats = self.usage_stats[provider_name]
                
                # 计算提供商评分
                score = stats['success_rate'] * 0.6 + (1 - stats['average_response_time'] / 60) * 0.4
                
                available_providers.append({
                    'name': provider_name,
                    'score': score,
                    'type': provider.get_type() if hasattr(provider, 'get_type') else 'unknown'
                })
        
        if not available_providers:
            return 'browser_automation'  # 默认提供商
        
        # 根据任务类型和复杂度选择
        if complexity_score > 0.8:
            # 高复杂度任务优先选择高级AI
            premium_providers = [p for p in available_providers if 'gpt' in p['name'] or 'claude' in p['name']]
            if premium_providers:
                return max(premium_providers, key=lambda x: x['score'])['name']
        
        # 选择评分最高的可用提供商
        return max(available_providers, key=lambda x: x['score'])['name']
    
    async def get_status(self) -> Dict[str, Any]:
        """获取外部AI状态"""
        status = {
            "total_providers": len(self.providers),
            "available_providers": 0,
            "providers": {}
        }
        
        for provider_name, stats in self.usage_stats.items():
            provider_status = {
                "daily_usage": stats['daily_usage'],
                "total_usage": stats['total_usage'],
                "success_rate": round(stats['success_rate'], 3),
                "average_response_time": round(stats['average_response_time'], 2),
                "available": self._check_usage_limit(provider_name)
            }
            
            status["providers"][provider_name] = provider_status
            
            if provider_status["available"]:
                status["available_providers"] += 1
        
        return status
    
    async def shutdown(self):
        """关闭外部AI集成器"""
        logger.info("关闭外部AI集成器...")
        
        for provider_name, provider in self.providers.items():
            if hasattr(provider, 'shutdown'):
                await provider.shutdown()
        
        logger.info("外部AI集成器已关闭")


class BrowserAIAutomation:
    """浏览器AI自动化"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化浏览器自动化"""
        self.config = config
        self.browser = None
        self.page = None
        self.ai_services = {
            'chatgpt': {
                'url': 'https://chat.openai.com/',
                'input_selector': 'textarea[placeholder*="Message"]',
                'send_selector': 'button[data-testid="send-button"]',
                'response_selector': '.markdown'
            },
            'claude': {
                'url': 'https://claude.ai/',
                'input_selector': 'div[contenteditable="true"]',
                'send_selector': 'button[aria-label="Send Message"]',
                'response_selector': '.font-claude-message'
            },
            'grok': {
                'url': 'https://grok.x.ai/',
                'input_selector': 'textarea',
                'send_selector': 'button[type="submit"]',
                'response_selector': '.response-content'
            }
        }
        
        logger.info("浏览器AI自动化初始化完成")
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理请求
        
        Args:
            request: 请求内容
            
        Returns:
            处理结果
        """
        try:
            # 选择AI服务
            ai_service = request.get('preferred_ai', 'chatgpt')
            if ai_service not in self.ai_services:
                ai_service = 'chatgpt'
            
            # 启动浏览器
            await self._ensure_browser()
            
            # 导航到AI服务
            service_config = self.ai_services[ai_service]
            await self.page.goto(service_config['url'])
            
            # 等待页面加载
            await self.page.wait_for_timeout(3000)
            
            # 输入问题
            content = request.get('content', '')
            await self.page.fill(service_config['input_selector'], content)
            
            # 发送消息
            await self.page.click(service_config['send_selector'])
            
            # 等待响应
            await self.page.wait_for_timeout(5000)
            
            # 获取响应
            response_elements = await self.page.query_selector_all(service_config['response_selector'])
            
            if response_elements:
                # 获取最后一个响应
                last_response = await response_elements[-1].inner_text()
                
                return {
                    "success": True,
                    "content": last_response,
                    "source": f"browser_{ai_service}",
                    "cost": 0.0  # 免费使用
                }
            else:
                return {
                    "success": False,
                    "error": "未能获取AI响应",
                    "cost": 0.0
                }
                
        except Exception as e:
            logger.error(f"浏览器自动化失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "cost": 0.0
            }
    
    async def _ensure_browser(self):
        """确保浏览器已启动"""
        if not self.browser:
            try:
                from playwright.async_api import async_playwright
                
                self.playwright = await async_playwright().start()
                self.browser = await self.playwright.chromium.launch(
                    headless=True,
                    args=['--no-sandbox', '--disable-dev-shm-usage']
                )
                self.page = await self.browser.new_page()
                
                # 设置用户代理
                await self.page.set_extra_http_headers({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
                
            except ImportError:
                logger.error("Playwright未安装，请运行: pip install playwright")
                raise
    
    def get_type(self) -> str:
        """获取提供商类型"""
        return "browser_automation"
    
    async def shutdown(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()


class N8nWorkflowIntegration:
    """n8n工作流集成"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化n8n集成"""
        self.config = config
        self.base_url = config.get('base_url', 'http://localhost:5678')
        self.api_key = config.get('api_key', '')
        
        logger.info("n8n工作流集成初始化完成")
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        通过n8n工作流处理请求
        
        Args:
            request: 请求内容
            
        Returns:
            处理结果
        """
        try:
            import aiohttp
            
            # 构建工作流请求
            workflow_data = {
                "content": request.get('content', ''),
                "task_type": request.get('task_type', 'general'),
                "max_tokens": request.get('max_tokens', 512)
            }
            
            # 调用n8n webhook
            webhook_url = f"{self.base_url}/webhook/ai-processing"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=workflow_data,
                    headers={'Authorization': f'Bearer {self.api_key}'}
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "success": True,
                            "content": result.get('response', ''),
                            "source": "n8n_workflow",
                            "cost": result.get('cost', 0.01)  # 估算成本
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"n8n工作流调用失败: {response.status}",
                            "cost": 0.0
                        }
                        
        except Exception as e:
            logger.error(f"n8n工作流集成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "cost": 0.0
            }
    
    def get_type(self) -> str:
        """获取提供商类型"""
        return "api_workflow"


class FreeAPIPool:
    """免费API池"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化免费API池"""
        self.config = config
        self.api_endpoints = [
            {
                "name": "deepseek",
                "url": "https://api.deepseek.com/v1/chat/completions",
                "key": config.get('deepseek_key', ''),
                "free_quota": 1000000  # 免费额度
            },
            {
                "name": "groq",
                "url": "https://api.groq.com/openai/v1/chat/completions", 
                "key": config.get('groq_key', ''),
                "free_quota": 100000
            }
        ]
        
        logger.info("免费API池初始化完成")
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用免费API处理请求
        
        Args:
            request: 请求内容
            
        Returns:
            处理结果
        """
        # 随机选择一个可用的API
        available_apis = [api for api in self.api_endpoints if api['key']]
        
        if not available_apis:
            return {
                "success": False,
                "error": "没有可用的免费API",
                "cost": 0.0
            }
        
        api = random.choice(available_apis)
        
        try:
            import aiohttp
            
            # 构建API请求
            messages = [
                {"role": "user", "content": request.get('content', '')}
            ]
            
            payload = {
                "model": "deepseek-chat" if api['name'] == 'deepseek' else "llama3-8b-8192",
                "messages": messages,
                "max_tokens": request.get('max_tokens', 512),
                "temperature": request.get('temperature', 0.7)
            }
            
            headers = {
                "Authorization": f"Bearer {api['key']}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    api['url'],
                    json=payload,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        
                        return {
                            "success": True,
                            "content": content,
                            "source": f"free_api_{api['name']}",
                            "cost": 0.0  # 免费API
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"API调用失败: {response.status}",
                            "cost": 0.0
                        }
                        
        except Exception as e:
            logger.error(f"免费API调用失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "cost": 0.0
            }
    
    def get_type(self) -> str:
        """获取提供商类型"""
        return "free_api"


async def main():
    """测试函数"""
    config = {
        "providers": [
            {
                "name": "browser_automation",
                "type": "free",
                "priority": 1,
                "daily_limit": 50
            }
        ]
    }
    
    integrator = ExternalAIIntegrator(config)
    
    test_request = {
        "content": "请解释什么是人工智能",
        "task_type": "general",
        "max_tokens": 200
    }
    
    try:
        result = await integrator.process("browser_automation", test_request)
        print("处理结果:", result)
        
        status = await integrator.get_status()
        print("状态:", status)
        
    finally:
        await integrator.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
