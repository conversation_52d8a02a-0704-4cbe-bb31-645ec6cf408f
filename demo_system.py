"""
Ewandata 知识管理系统演示
完整的端到端功能演示
"""

import sys
import os
import time
from pathlib import Path

# 添加系统路径
sys.path.append('ewandata_system')

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n[步骤 {step}] {description}")
    print("-" * 40)

def demo_document_processing():
    """演示文档处理功能"""
    print_header("📚 文档处理引擎演示")

    try:
        from processors.document_processor import DocumentProcessor

        processor = DocumentProcessor()

        print_step(1, "初始化文档处理器")
        print(f"✅ 支持的格式: {', '.join(processor.get_supported_formats())}")

        print_step(2, "处理README文档")
        readme_file = "README.md"
        if os.path.exists(readme_file):
            result = processor.process_file(readme_file)

            if "error" not in result:
                print(f"✅ 文档处理成功:")
                print(f"   📄 文件名: {result.get('file_name', 'N/A')}")
                print(f"   📊 文件大小: {result.get('size_mb', 0):.2f} MB")
                print(f"   🔤 字符数: {result.get('char_count', 0):,}")
                print(f"   📝 单词数: {result.get('word_count', 0):,}")
                print(f"   🏷️ 文件类型: {result.get('type', 'N/A')}")

                if result.get('type') == 'markdown':
                    print(f"   📋 标题数量: {result.get('header_count', 0)}")

                # 显示内容预览
                content = result.get('content', '')
                if content:
                    preview = content[:150] + "..." if len(content) > 150 else content
                    print(f"   👀 内容预览: {preview}")
            else:
                print(f"❌ 处理失败: {result['error']}")
        else:
            print(f"❌ 文件不存在: {readme_file}")

        return True

    except Exception as e:
        print(f"❌ 文档处理演示失败: {str(e)}")
        return False

def demo_knowledge_base():
    """演示知识库功能"""
    print_header("🧠 知识库管理演示")

    try:
        from storage.knowledge_base import KnowledgeBase
        from processors.document_processor import DocumentProcessor

        print_step(1, "初始化知识库")
        kb = KnowledgeBase("E:/Ewandata")
        processor = DocumentProcessor()
        print("✅ 知识库初始化成功")

        print_step(2, "获取知识库统计")
        stats = kb.get_statistics()
        print(f"✅ 知识库统计:")
        print(f"   📚 文档总数: {stats.get('total_documents', 0)}")
        print(f"   💾 总大小: {stats.get('total_size_mb', 0):.2f} MB")
        print(f"   🆕 最近文档: {stats.get('recent_documents', 0)}")

        if stats.get('by_type'):
            print(f"   📊 文档类型分布:")
            for doc_type, count in list(stats['by_type'].items())[:5]:
                print(f"      {doc_type}: {count}")

        print_step(3, "演示文档存储")
        # 创建演示文档
        demo_doc = {
            'title': 'Ewandata系统演示文档',
            'content': '''这是Ewandata知识管理系统的演示文档。

系统主要功能包括：
1. 多格式文档处理 - 支持PDF、DOCX、Markdown等格式
2. AI内容分析 - 自动摘要、关键词提取、主题分类
3. 语义搜索 - 基于向量数据库的智能搜索
4. 项目跟踪 - 自动发现和分析项目关联性
5. GitHub集成 - 自动同步和版本控制

系统采用本地部署，充分利用RTX 4070 12GB GPU的计算能力。''',
            'file_path': 'demo_document.md',
            'file_hash': 'demo_hash_12345',
            'type': 'markdown',
            'size': 1024,
            'tags': ['演示', '知识管理', 'AI'],
            'keywords': ['Ewandata', '文档处理', '语义搜索', 'GPU'],
            'summary': 'Ewandata知识管理系统功能演示文档',
            'importance_score': 0.9
        }

        doc_id = kb.store_document(demo_doc)
        print(f"✅ 演示文档存储成功，ID: {doc_id}")

        print_step(4, "测试文档检索")
        stored_doc = kb.get_document(doc_id)
        if stored_doc:
            print(f"✅ 文档检索成功:")
            print(f"   📄 标题: {stored_doc['title']}")
            print(f"   🏷️ 标签: {', '.join(stored_doc['tags'])}")
            print(f"   ⭐ 重要性: {stored_doc['importance_score']}")

        print_step(5, "测试文本搜索")
        search_results = kb.search_text("知识管理", limit=3)
        print(f"✅ 搜索完成，找到 {len(search_results)} 个结果")

        for i, result in enumerate(search_results[:2], 1):
            print(f"   {i}. {result['title']}")
            print(f"      类型: {result['file_type']}")
            print(f"      重要性: {result['importance_score']}")

        kb.close()
        return True

    except Exception as e:
        print(f"❌ 知识库演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_project_tracking():
    """演示项目跟踪功能"""
    print_header("📁 项目跟踪系统演示")

    try:
        from services.project_tracker import ProjectTracker

        print_step(1, "初始化项目跟踪器")
        # 只扫描Ewandata目录，避免扫描整个E盘
        tracker = ProjectTracker("E:/Ewandata")
        print("✅ 项目跟踪器初始化成功")

        print_step(2, "扫描项目")
        print("🔍 正在扫描项目...")
        scan_result = tracker.scan_projects()

        if 'error' in scan_result:
            print(f"❌ 扫描失败: {scan_result['error']}")
            return False

        print(f"✅ 扫描完成:")
        print(f"   📊 发现项目: {scan_result['projects_found']}")
        print(f"   ⏱️ 扫描耗时: {scan_result['scan_duration']:.2f}秒")

        if scan_result['projects']:
            print(f"   📂 项目示例:")
            for project in scan_result['projects'][:3]:
                project_name = Path(project).name
                print(f"      - {project_name}")

        return True

    except Exception as e:
        print(f"❌ 项目跟踪演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_integration():
    """演示系统集成功能"""
    print_header("🔗 系统集成演示")

    try:
        from processors.document_processor import DocumentProcessor
        from storage.knowledge_base import KnowledgeBase

        print_step(1, "端到端工作流演示")

        # 初始化组件
        processor = DocumentProcessor()
        kb = KnowledgeBase("E:/Ewandata")

        # 处理实施方案文档
        plan_file = "Ewandata实施方案.md"
        if os.path.exists(plan_file):
            print(f"📄 处理文档: {plan_file}")

            # 1. 文档处理
            doc_data = processor.process_file(plan_file)

            if "error" not in doc_data:
                print(f"✅ 文档处理完成:")
                print(f"   大小: {doc_data.get('size_mb', 0):.2f} MB")
                print(f"   字符数: {doc_data.get('char_count', 0):,}")

                # 2. 存储到知识库
                doc_id = kb.store_document(doc_data)
                print(f"✅ 存储到知识库: {doc_id}")

                # 3. 验证存储
                stored_doc = kb.get_document(doc_id)
                if stored_doc:
                    print(f"✅ 存储验证成功")

                    # 4. 搜索测试
                    search_results = kb.search_text("AI模型", limit=2)
                    print(f"✅ 搜索测试: 找到 {len(search_results)} 个相关文档")

            else:
                print(f"❌ 文档处理失败: {doc_data['error']}")

        kb.close()
        return True

    except Exception as e:
        print(f"❌ 集成演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_system_status():
    """演示系统状态"""
    print_header("📊 系统状态概览")

    try:
        print_step(1, "环境信息")
        import torch
        print(f"✅ Python版本: {sys.version.split()[0]}")
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")

        if torch.cuda.is_available():
            print(f"✅ GPU设备: {torch.cuda.get_device_name()}")
            print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")

        print_step(2, "系统文件")
        system_files = [
            "ewandata_system/models/model_manager.py",
            "ewandata_system/processors/document_processor.py",
            "ewandata_system/storage/knowledge_base.py",
            "ewandata_system/services/project_tracker.py",
            "ewandata_system/services/github_sync.py"
        ]

        for file_path in system_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path) / 1024
                print(f"✅ {Path(file_path).name}: {size:.1f}KB")
            else:
                print(f"❌ {Path(file_path).name}: 不存在")

        print_step(3, "数据目录")
        data_dirs = [
            "data/knowledge_base",
            "data/processed",
            "data/vectors",
            "temp",
            "logs"
        ]

        for dir_path in data_dirs:
            if os.path.exists(dir_path):
                file_count = len(list(Path(dir_path).rglob('*')))
                print(f"✅ {dir_path}: {file_count} 个文件")
            else:
                print(f"❌ {dir_path}: 不存在")

        return True

    except Exception as e:
        print(f"❌ 系统状态检查失败: {str(e)}")
        return False

def main():
    """主演示函数"""
    print_header("🚀 Ewandata 知识管理系统完整演示")
    print("欢迎使用Ewandata本地知识管理系统！")
    print("本演示将展示系统的核心功能和集成能力。")

    # 演示模块列表
    demos = [
        ("系统状态检查", demo_system_status),
        ("文档处理引擎", demo_document_processing),
        ("知识库管理", demo_knowledge_base),
        ("项目跟踪系统", demo_project_tracking),
        ("系统集成功能", demo_integration)
    ]

    results = []

    for demo_name, demo_func in demos:
        print(f"\n⏳ 准备运行: {demo_name}")
        time.sleep(1)

        try:
            success = demo_func()
            results.append((demo_name, success))

            if success:
                print(f"✅ {demo_name} 演示完成")
            else:
                print(f"❌ {demo_name} 演示失败")

        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {str(e)}")
            results.append((demo_name, False))

        time.sleep(1)

    # 显示演示结果
    print_header("📋 演示结果汇总")

    total_demos = len(results)
    successful_demos = sum(1 for _, success in results if success)

    for demo_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{demo_name}: {status}")

    print(f"\n📊 总体结果: {successful_demos}/{total_demos} 个演示成功")

    if successful_demos == total_demos:
        print("\n🎉 恭喜！Ewandata知识管理系统所有功能演示成功！")
        print("系统已准备就绪，可以开始使用。")
    elif successful_demos > total_demos // 2:
        print("\n✅ 大部分功能正常工作，系统基本可用。")
        print("建议检查失败的模块并进行修复。")
    else:
        print("\n⚠️ 多个功能存在问题，建议进行系统检查。")

    print_header("🔚 演示结束")
    print("感谢使用Ewandata知识管理系统！")

if __name__ == "__main__":
    main()