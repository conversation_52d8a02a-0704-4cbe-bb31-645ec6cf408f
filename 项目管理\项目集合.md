# 项目集合

## 概述
本文档整合了E:\Projects目录中的所有项目信息，包括AI工具、开发框架、应用项目等，形成完整的项目档案。

## 目录
- [AI工具项目](#ai工具项目)
- [大模型项目](#大模型项目)
- [开发框架](#开发框架)
- [应用项目](#应用项目)
- [工具项目](#工具项目)

## AI工具项目

### 1. FastGPT - AI Agent构建平台
**原始路径**：E:/fsatgpt/FastGPT

#### 项目简介
FastGPT 是一个AI Agent构建平台，提供开箱即用的数据处理、模型调用、可视化Flow工作流编排等能力，支持复杂应用场景的快速实现。

#### 主要功能
- 可视化Flow工作流编排
- 多模型、多知识库混合调用
- 插件化工具与API集成
- 支持多种数据格式导入（txt、md、html、pdf、docx、csv、xlsx、url等）
- 支持OpenAPI、知识库API等多种接口
- 支持无代码、低代码开发

#### 技术栈与依赖
- Next.js + TypeScript + ChakraUI
- MongoDB、PostgreSQL（PG Vector）、Milvus
- 支持多云部署与本地部署

#### 启动方式
```bash
# 克隆项目
git clone https://github.com/labring/FastGPT.git
cd FastGPT
# 安装依赖
npm install
# 启动开发环境
npm run dev
```

#### 特色亮点
- 支持多模型、多知识库混合调用
- 可视化工作流，极大提升开发效率
- 丰富的插件与API生态

### 2. prompt-optimizer - 提示词优化器
**原始路径**：E:/prompt/prompt-optimizer

#### 项目简介
Prompt Optimizer 是一个面向AI提示词工程师和开发者的提示词优化与管理平台，支持多模型、多语言、历史记录、模板管理等功能，提供智能优化、对比测试、多模型集成等核心特性。

#### 主要功能
- 多模型提示词优化
- 模型与API密钥管理
- 优化历史与复用
- 模板库与自定义
- 响应式AI与多语言
- 智能优化与对比测试
- 高级参数配置
- 安全架构与隐私保护

#### 技术栈与依赖
- 前端：React + Vite
- 包管理：pnpm
- 部署：Docker + Vercel
- 插件：Chrome Extension
- 实时通信：Socket.io

#### 启动方式
1. **在线使用**：https://prompt.always200.com
2. **本地开发**：
   ```bash
   git clone https://github.com/linshenkx/prompt-optimizer.git
   cd prompt-optimizer
   pnpm install
   pnpm dev
   ```
3. **Docker部署**：
   ```bash
   docker run -d -p 80:80 --restart unless-stopped --name prompt-optimizer linshen/prompt-optimizer
   ```
4. **Chrome插件**：https://chromewebstore.google.com/detail/prompt-optimizer/cakkkhboolfnadechdlgdcnjammejlna

#### 特色亮点
- 支持OpenAI、Gemini、DeepSeek、智谱AI、SiliconFlow等主流AI模型
- 客户端处理，数据直接与AI服务商交互
- 本地加密存储历史记录和API密钥
- 支持密码保护功能
- 响应式布局和流畅交互动画

### 3. Gemini-cli - Google Gemini命令行AI工具
**原始路径**：E:/Gemini-cli

#### 项目简介
Gemini CLI 是Google 官方开发的命令行AI工作流工具，能够连接你的工具、理解代码并加速工作流程，支持查询和编辑大型代码库、从PDF或图片生成新应用、自动化操作任务等。

#### 主要功能
- 查询和编辑大型代码库（支持超100M token上下文窗口）
- 利用多模态能力从PDF或图片生成新应用
- 自动化操作任务（如查询API、处理复杂webhook等）
- 支持工具和MCP服务器连接新功能
- 内置Google搜索工具进行查询基础

#### 技术栈与依赖
- Node.js 18+
- TypeScript
- 支持Docker部署

#### 启动方式
1. **快速体验**：
   ```bash
   npx https://github.com/google-gemini/gemini-cli
   ```
2. **全局安装**：
   ```bash
   npm install -g @google/gemini-cli
   gemini
   ```
3. **认证**：使用个人Google账户登录，获得每分钟60次、每天1000次的模型请求配额

#### 特色亮点
- 官方支持，稳定可靠
- 支持多种认证方式（个人账户、API密钥、Google Workspace等）
- 丰富的命令式功能与工作流集成

## 大模型项目

### 4. LLaMA-Factory - 大模型微调与推理管理平台
**原始路径**：E:/llama_factory/LLaMA-Factory

#### 项目简介
LLaMA-Factory 是一个支持百余种主流大模型的统一高效微调与推理管理平台，支持多种微调方法、量化方式和推理后端，适合科研、企业和开发者快速定制和部署大模型。

#### 主要功能
- 支持LLaMA、LLaVA、Mistral、Qwen、DeepSeek、Yi、Gemma、ChatGLM、Phi等主流模型
- 支持多种微调方法（LoRA、QLoRA、DPO、KTO、ORPO等）
- 支持多种量化方式（AQM、AWQ、GPTQ、LLM.int8等）
- 支持多种推理后端（vLLM、SGLang、OpenAI风格API等）
- 支持可视化微调（LLaMA Board）、实验监控（TensorBoard、Wandb等）
- 丰富的实用技巧与生态集成

#### 技术栈与依赖
- Python 3
- PyTorch
- Transformers
- 支持Docker、Colab、PAI-DSW等多环境部署

#### 启动方式
1. 安装依赖：
   ```bash
   pip install llamafactory
   ```
2. 参考官方文档进行数据准备与配置
3. 运行微调/推理命令或使用Web UI

#### 特色亮点
- 支持百余种主流大模型的统一微调
- 多种微调方法与量化方式灵活组合
- 丰富的可视化与监控工具

### 5. qwq32B - 32B大模型项目
**原始路径**：E:/qwq32B

#### 项目简介
32B参数规模的大语言模型项目，提供高性能的推理和微调能力。

#### 主要功能
- 32B参数规模模型推理
- 支持多种微调方法
- 高性能计算优化
- 多GPU分布式训练

#### 技术栈与依赖
- PyTorch
- Transformers
- 分布式训练框架
- 高性能计算库

### 6. hunyuan_12 - 混元12B模型项目
**原始路径**：E:/hunyuan_12

#### 项目简介
腾讯混元12B大语言模型项目，提供中文理解和生成能力。

#### 主要功能
- 中文文本理解与生成
- 多轮对话支持
- 知识问答能力
- 代码生成与理解

### 7. kexuedajian - 科学大剑项目
**原始路径**：E:/kexuedajian

#### 项目简介
专注于科学计算和数学推理的大模型项目。

#### 主要功能
- 数学问题求解
- 科学计算支持
- 公式推导能力
- 学术论文理解

## 开发框架

### 8. knowledge-cosmos - 知识星图
**原始路径**：E:/2025.6.18之前项目研究/knowledge-cosmos

#### 项目简介
Knowledge Cosmos（知识星图）是一个基于元认知理论的跨年龄段学习工具，旨在帮助用户从人类通识的底层原理出发，深入理解和掌握知识，通过应用和联想逐步扩展认知边界，最终实现宇宙真理与现实世界的融合。

#### 主要功能
- 知识星图可视化：帮助用户直观理解知识结构和层次
- 公理探索：展示和探索各学科基础公理，支持深入学习和对比
- 新理论创建：组合公理，激发用户创新，生成新理论
- 知识路径构建：引导用户从基础到高级，构建个性化知识成长路径

#### 技术栈与依赖
- 前端：Next.js + React + D3 + Three.js
- 数据库：Neo4j（图数据库）
- 依赖包（部分）：
  - next
  - react
  - react-dom
  - d3
  - neo4j-driver
  - three

#### 启动方式
1. 安装依赖：
   ```bash
   npm install
   ```
2. 启动开发环境：
   ```bash
   npm run dev
   ```
3. 构建生产环境：
   ```bash
   npm run build
   npm start
   ```

#### 特色亮点
- 支持多学科知识的可视化与联想
- 强调元认知与批判性思维能力培养
- 支持AI驱动的知识发现与路径推荐

### 9. n8n - 工作流自动化平台
**原始路径**：E:/n8n

#### 项目简介
n8n是一个开源的工作流自动化平台，支持可视化工作流设计。

#### 主要功能
- 可视化工作流设计
- 多种节点类型支持
- API集成能力
- 自动化任务执行

#### 技术栈与依赖
- Node.js
- TypeScript
- Vue.js
- PostgreSQL

## 应用项目

### 10. DramaScriptAI - 剧本AI项目
**原始路径**：E:/DramaScriptAI

#### 项目简介
DramaScriptAI 是一个专注于剧本创作和AI辅助写作的项目，旨在利用人工智能技术辅助剧本创作、角色设定和情节发展。

#### 主要功能
- AI辅助剧本创作
- 角色设定与对话生成
- 情节发展与故事结构
- 剧本格式化和输出

#### 技术栈与依赖
- 待完善（项目处于初始开发阶段）

#### 启动方式
- 项目目前处于开发初期，具体启动方式待完善

#### 特色亮点
- 专注于剧本创作领域
- AI辅助写作功能
- 创新的创作工具

### 11. Twomonkey_Blogger_bark - 双猴博客项目
**原始路径**：E:/Twomonkey_Blogger_bark

#### 项目简介
基于Bark语音合成技术的博客内容创作项目。

#### 主要功能
- 文本转语音合成
- 博客内容生成
- 多媒体内容创作
- 自动化内容发布

### 12. 蛋仔 - 游戏项目
**原始路径**：E:/蛋仔

#### 项目简介
蛋仔主题的游戏开发项目。

#### 主要功能
- 游戏逻辑实现
- 用户交互设计
- 游戏数据管理
- 多平台适配

### 13. 贪吃蛇 - 经典游戏项目
**原始路径**：E:/贪吃蛇

#### 项目简介
经典贪吃蛇游戏的现代实现。

#### 主要功能
- 游戏核心逻辑
- 用户界面设计
- 分数系统
- 游戏状态管理

## 工具项目

### 14. openaiwhisper-large-v3-turbo - 语音识别工具
**原始路径**：E:/openaiwhisper-large-v3-turbo

#### 项目简介
基于OpenAI Whisper Large V3 Turbo模型的语音识别工具。

#### 主要功能
- 高精度语音转文字
- 多语言支持
- 实时语音识别
- 音频文件处理

### 15. prompt_rag - RAG提示词项目
**原始路径**：E:/prompt_rag

#### 项目简介
基于检索增强生成（RAG）的提示词优化项目。

#### 主要功能
- 知识库检索
- 上下文增强
- 提示词优化
- 多源信息融合

### 16. SQL_FastGPT - SQL查询工具
**原始路径**：E:/SQL_FastGPT

#### 项目简介
基于FastGPT的SQL查询和数据库管理工具。

#### 主要功能
- SQL查询生成
- 数据库连接管理
- 查询结果可视化
- 数据库结构分析

### 17. 格式转换工具
**原始路径**：E:/格式转换工具

#### 项目简介
多格式文件转换工具。

#### 主要功能
- 文档格式转换
- 图片格式转换
- 音频视频转换
- 批量处理能力

### 18. 暴喵加速器
**原始路径**：E:/暴喵加速器

#### 项目简介
网络加速和代理工具项目。

#### 主要功能
- 网络加速
- 代理服务
- 流量优化
- 连接管理

### 19. Trae - 开发工具
**原始路径**：E:/Trae

#### 项目简介
开发辅助工具项目。

#### 主要功能
- 代码生成
- 模板管理
- 开发效率提升
- 工具集成

### 20. henghengmao - 横横猫项目
**原始路径**：E:/henghengmao

#### 项目简介
横横猫主题的应用项目。

#### 主要功能
- 用户界面设计
- 功能模块开发
- 数据管理
- 用户体验优化

## 项目分类总结

### 按技术领域分类
- **AI/ML项目**：FastGPT、prompt-optimizer、LLaMA-Factory、qwq32B等
- **开发框架**：knowledge-cosmos、n8n等
- **应用项目**：DramaScriptAI、蛋仔、贪吃蛇等
- **工具项目**：格式转换工具、暴喵加速器、Trae等

### 按开发状态分类
- **成熟项目**：FastGPT、prompt-optimizer、LLaMA-Factory
- **开发中项目**：knowledge-cosmos、DramaScriptAI
- **初始阶段**：部分工具项目

### 按应用场景分类
- **AI开发工具**：模型训练、提示词优化、工作流自动化
- **内容创作**：剧本创作、博客生成、多媒体处理
- **游戏娱乐**：蛋仔、贪吃蛇等游戏项目
- **实用工具**：格式转换、网络加速、开发辅助

## 项目管理和维护建议

### 1. 项目优先级管理
- **高优先级**：FastGPT、prompt-optimizer、LLaMA-Factory
- **中优先级**：knowledge-cosmos、DramaScriptAI
- **低优先级**：工具类项目

### 2. 技术栈统一
- 前端：React + TypeScript
- 后端：Node.js/Python
- 数据库：MongoDB/PostgreSQL
- 部署：Docker + 云服务

### 3. 文档管理
- 统一README格式
- 完善API文档
- 建立项目wiki
- 定期更新维护

### 4. 版本控制
- 使用Git进行版本管理
- 建立分支策略
- 定期代码审查
- 自动化测试

---
*最后更新：2025-02-22*
*项目信息持续更新中* 