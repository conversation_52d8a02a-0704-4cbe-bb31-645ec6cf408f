# README.md

## 文档信息
- **文件路径**: E:\Ewandata\README.md
- **文件类型**: markdown
- **文件大小**: 0.00 MB
- **创建时间**: 2025-06-28T12:59:02.107303
- **修改时间**: 2025-07-03T23:31:02.201137

## 标签


## 关键词


## 摘要


## 内容
# 🧠 Ewandata 本地知识管理系统

> 基于RTX 4070 GPU的个人AI数字助手和知识管理系统

## 🎯 项目概述

Ewandata是一个完全本地化部署的智能知识管理系统，专为个人学习和项目管理设计。系统充分利用RTX 4070 12GB GPU的计算能力，提供AI驱动的文档处理、知识组织和项目跟踪功能。

## ✨ 核心功能

### 📚 智能知识库
- **多格式支持**: PDF、DOCX、图片、Markdown等格式自动处理
- **AI内容分析**: 自动摘要、关键词提取、主题分类
- **语义搜索**: 基于内容理解的精准搜索
- **知识关联**: 自动发现内容间的隐藏联系

### 📁 项目管理
- **自动发现**: 实时监控E盘所有项目变化
- **关联分析**: 识别项目间的技术栈重叠和协同机会
- **知识复用**: 跨项目的代码和经验复用建议

### 🔄 自动化工作流
- **文件监控**: 自动处理临时记文件夹的新内容
- **GitHub同步**: 自动同步处理后的内容到GitHub
- **本地清理**: 智能清理临时文件，保持系统整洁

## 🚀 快速开始

### 系统要求
- **硬件**: RTX 4070 12GB GPU (已满足)
- **软件**: Python 3.9+, CUDA 11.8+, Git 2.30+
- **存储**: 100GB+ SSD空间

### 一键安装
```bash
# 1. 双击运行快速启动脚本
quick_start.bat

# 2. 等待自动安装完成
# 系统会自动：
# - 检查环境依赖
# - 创建虚拟环境
# - 安装所需包
# - 创建目录结构
# - 配置临时记文件夹
```

### 启动系统
```bash
# 启动完整系统
start_system.bat

# 或分别启动各组件
python api/main.py          # API服务
streamlit run app/streamlit_app.py  # Web界面
python services/file_monitor.py    # 文件监控
```

## 📋 系统架构

```
Ewandata系统架构：
├── 数据输入层
│   ├── 临时记文件夹监控
│   ├── 多格式文档处理器
│   └── 网页内容抓取器
├── AI处理层
│   ├── Microsoft Phi-3-mini (主要推理)
│   ├── 文本嵌入模型 (语义理解)
│   └── OCR引擎 (图片文字识别)
├── 知识存储层
│   ├── 向量数据库 (ChromaDB)
│   ├── 关系数据库 (SQLite)
│   └── 文件系统 (Markdown + JSON)
└── 应用服务层
    ├── FastAPI后端服务
    ├── 项目跟踪服务
    └── GitHub同步服务
```

## 🎛️ 使用方式

### Web管理界面
访问 `http://localhost:8501` 使用图形化界面：
- 📊 系统概览仪表板
- 📚 知识库管理
- 🔍 智能搜索
- 📁 项目跟踪
- ⚙️ 系统设置

### API接口
访问 `http://localhost:8000/docs` 查看API文档：
- 文档上传和处理
- 智能搜索接口
- 项目信息获取
- 系统状态监控

### 自动化工作流
1. 将文件放入 `C:\Users\<USER>\Desktop\临时记`
2. 系统自动检测并处理
3. 生成Markdown和JSON格式
4. 自动同步到GitHub
5. 清理本地临时文件

## 📊 预期效果

### 效率提升
- ⚡ 节省90%手动整理时间
- 🎯 提高检索效率5倍
- 🔗 自动发现知识关联
- 📈 加速学习和项目开发

### 智能化管理
- 🤖 AI驱动的内容分析
- 🏷️ 自动分类和标签
- 📊 项目关联分析
- 💡 个性化推荐

## 📁 项目结构

```
E:\Ewandata\
├── ewandata_system\      # 系统核心代码
├── data\                 # 数据存储
├── temp\                 # 临时文件
├── logs\                 # 日志文件
├── requirements.txt      # 依赖列表
├── quick_start.bat       # 快速启动脚本
├── Ewandata实施方案.md   # 详细实施方案
└── README.md            # 本文件
```

## 🔧 技术栈

- **AI模型**: Microsoft Phi-3-mini (RTX 4070优化)
- **后端**: Python + FastAPI
- **前端**: Streamlit + React
- **数据库**: ChromaDB + SQLite
- **文档处理**: LangChain + PyPDF2
- **监控**: Watchdog + Schedule

## 📖 详细文档

- [📋 完整实施方案](./Ewandata实施方案.md) - 详细的系统设计和实施计划
- [🛠️ 使用指南](./使用指南.md) - 系统使用方法和最佳实践
- [🔧 开发文档](./docs/) - 开发者文档和API参考

## 🤝 贡献

欢迎提交Issue和Pull Request来改进系统！

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

---

**立即开始**: 运行 `quick_start.bat` 开始您的智能知识管理之旅！


---
*文档ID: doc_482a783c_1751558955*
*重要性评分: 0.50*
