"""
Ewandata混合AI系统工作流程追踪器
详细展示文件处理的完整流程和输出结构
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加系统路径
sys.path.append('ewandata_system')

def print_header(title, level=1):
    if level == 1:
        print(f"\n{'='*80}")
        print(f"  {title}")
        print('='*80)
    elif level == 2:
        print(f"\n{'─'*60}")
        print(f"  {title}")
        print('─'*60)
    else:
        print(f"\n[{title}]")

def print_file_tree(directory: Path, prefix="", max_depth=3, current_depth=0):
    """打印文件树结构"""
    if current_depth >= max_depth:
        return

    try:
        items = sorted(directory.iterdir(), key=lambda x: (x.is_file(), x.name.lower()))

        for i, item in enumerate(items):
            is_last = i == len(items) - 1
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item.name}")

            if item.is_dir() and current_depth < max_depth - 1:
                extension = "    " if is_last else "│   "
                print_file_tree(item, prefix + extension, max_depth, current_depth + 1)

    except PermissionError:
        print(f"{prefix}└── [权限不足]")

class WorkflowTracker:
    """工作流程追踪器"""

    def __init__(self):
        self.source_folder = Path(r"C:\Users\<USER>\Desktop\临时记")
        self.output_folder = Path("data/processed")
        self.knowledge_base = Path("data/knowledge_base")
        self.workflow_logs = Path("logs/workflow")

        # 创建必要目录
        for folder in [self.output_folder, self.knowledge_base, self.workflow_logs]:
            folder.mkdir(parents=True, exist_ok=True)

    def show_complete_workflow(self):
        """展示完整工作流程"""
        print_header("🔍 Ewandata混合AI系统完整工作流程追踪")

        print("""
📋 工作流程概览：
1️⃣ 源文件扫描和识别
2️⃣ 混合AI智能处理
3️⃣ 知识图谱构建
4️⃣ 输出文件生成
5️⃣ 工作流集成
6️⃣ 知识体系构建
        """)

        # 1. 源文件位置
        self.show_source_files()

        # 2. 处理流程
        self.show_processing_workflow()

        # 3. 输出文件结构
        self.show_output_structure()

        # 4. 知识体系关系
        self.show_knowledge_system()

        # 5. 工作流集成
        self.show_workflow_integration()

    def show_source_files(self):
        """展示源文件位置和结构"""
        print_header("1️⃣ 源文件位置和识别", 2)

        print(f"📁 监控文件夹: {self.source_folder}")

        if self.source_folder.exists():
            print("\n📂 文件夹结构:")
            print_file_tree(self.source_folder, max_depth=4)

            # 统计文件类型
            all_files = list(self.source_folder.rglob("*"))
            files_only = [f for f in all_files if f.is_file()]

            print(f"\n📊 文件统计:")
            print(f"   总文件数: {len(files_only)}")
            print(f"   总文件夹数: {len([f for f in all_files if f.is_dir()])}")

            # 按类型分组
            type_stats = {}
            for file_path in files_only:
                ext = file_path.suffix.lower() or '[无扩展名]'
                type_stats[ext] = type_stats.get(ext, 0) + 1

            print(f"\n📋 文件类型分布:")
            for ext, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
                print(f"   {ext}: {count} 个")
        else:
            print(f"❌ 源文件夹不存在: {self.source_folder}")
            print("💡 请确保临时记文件夹存在并包含待处理文件")

    def show_processing_workflow(self):
        """展示处理工作流程"""
        print_header("2️⃣ 混合AI处理工作流程", 2)

        workflow_steps = [
            {
                "step": "文件扫描",
                "component": "EnhancedFileMonitor",
                "location": "ewandata_system/services/enhanced_file_monitor.py",
                "function": "recursive_scan_files()",
                "description": "递归扫描所有子目录，识别支持的文件格式"
            },
            {
                "step": "格式处理",
                "component": "MultiFormatProcessor",
                "location": "ewandata_system/processors/multi_format_processor.py",
                "function": "process_file()",
                "description": "根据文件类型选择处理方法：文本/Office/图片OCR/压缩文件"
            },
            {
                "step": "任务分类",
                "component": "TaskClassifier",
                "location": "ewandata_system/services/task_classifier.py",
                "function": "classify()",
                "description": "分析文件内容，确定任务类型：代码分析/文档分析/快速响应/创意生成"
            },
            {
                "step": "复杂度评估",
                "component": "ComplexityEvaluator",
                "location": "ewandata_system/services/task_classifier.py",
                "function": "evaluate()",
                "description": "评估任务复杂度(0-1分数)，决定使用本地模型还是外部AI"
            },
            {
                "step": "路由决策",
                "component": "RouteDecider",
                "location": "ewandata_system/services/task_classifier.py",
                "function": "decide()",
                "description": "智能选择AI模型：Qwen2-7B/CodeLlama-7B/Phi-3-mini/外部AI"
            },
            {
                "step": "AI处理",
                "component": "HybridAIManager",
                "location": "ewandata_system/services/hybrid_ai_manager.py",
                "function": "process_request()",
                "description": "调用选定的AI模型进行内容分析、关键词提取、摘要生成"
            },
            {
                "step": "知识图谱",
                "component": "KnowledgeGraph",
                "location": "ewandata_system/services/knowledge_graph.py",
                "function": "add_document()",
                "description": "构建文档关系网络，分析文档间关联性"
            },
            {
                "step": "结果输出",
                "component": "EnhancedFileMonitor",
                "location": "ewandata_system/services/enhanced_file_monitor.py",
                "function": "_save_processing_results()",
                "description": "生成JSON和Markdown格式的处理结果"
            }
        ]

        print("🔄 处理流程详解:")
        for i, step in enumerate(workflow_steps, 1):
            print(f"\n{i}. {step['step']}")
            print(f"   📦 组件: {step['component']}")
            print(f"   📁 位置: {step['location']}")
            print(f"   ⚙️ 函数: {step['function']}")
            print(f"   📝 功能: {step['description']}")

        # AI模型分配策略
        print(f"\n🧠 AI模型分配策略:")
        model_mapping = {
            "代码分析": {
                "model": "CodeLlama-7B",
                "trigger": "文件扩展名: .py, .js, .html, .css等",
                "speciality": "代码理解、函数分析、语法检查"
            },
            "文档分析": {
                "model": "Qwen2-7B",
                "trigger": "中文文档、技术文档、一般文本",
                "speciality": "中文理解、关键词提取、内容分类"
            },
            "快速响应": {
                "model": "Phi-3-mini",
                "trigger": "文件大小 < 500字符",
                "speciality": "快速处理、简单问答"
            },
            "创意生成": {
                "model": "外部AI (ChatGPT/Claude)",
                "trigger": "包含'创意'、'故事'、'设计'等关键词",
                "speciality": "创意写作、复杂推理"
            },
            "复杂分析": {
                "model": "外部AI (ChatGPT/Claude)",
                "trigger": "文件大小 > 5000字符 或 复杂度 > 0.8",
                "speciality": "深度分析、复杂逻辑推理"
            }
        }

        for task_type, info in model_mapping.items():
            print(f"\n   📋 {task_type}:")
            print(f"      🤖 模型: {info['model']}")
            print(f"      🎯 触发条件: {info['trigger']}")
            print(f"      ⭐ 专长: {info['speciality']}")

    def show_output_structure(self):
        """展示输出文件结构"""
        print_header("3️⃣ 输出文件结构和位置", 2)

        print("📁 输出目录结构:")
        print(f"""
E:\\Ewandata\\
├── data/
│   ├── processed/                    # 处理结果文件
│   │   ├── [文件名]_[时间戳].json   # JSON格式结构化数据
│   │   ├── [文件名]_[时间戳].md     # Markdown格式报告
│   │   └── complete_workflow_test_report.json  # 测试报告
│   │
│   ├── knowledge_base/               # 知识库文件
│   │   ├── knowledge_index.json     # 知识库索引
│   │   ├── knowledge_catalog.md     # 知识库目录
│   │   └── documents/               # 文档存储
│   │
│   └── logs/                        # 日志文件
│       ├── ewandata.log            # 系统日志
│       └── workflow/               # 工作流日志
│
├── n8n_workflows/                   # n8n工作流配置
│   ├── file_processing_workflow.json
│   └── github_upload_workflow.json
│
└── ewandata_system/                 # 系统核心代码
    ├── services/                    # 核心服务
    ├── processors/                  # 文件处理器
    └── config/                      # 配置文件
        """)

        # 检查实际输出文件
        if self.output_folder.exists():
            print(f"\n📂 实际输出文件:")
            print_file_tree(self.output_folder, max_depth=2)

            # 分析输出文件
            output_files = list(self.output_folder.glob("*"))
            json_files = [f for f in output_files if f.suffix == '.json']
            md_files = [f for f in output_files if f.suffix == '.md']

            print(f"\n📊 输出文件统计:")
            print(f"   JSON文件: {len(json_files)} 个")
            print(f"   Markdown文件: {len(md_files)} 个")
            print(f"   总输出文件: {len(output_files)} 个")

        # 文件内容结构说明
        print(f"\n📋 输出文件内容结构:")

        json_structure = {
            "original_file": "原始文件路径",
            "processed_time": "处理时间戳",
            "file_info": {
                "name": "文件名",
                "type": "文件类型",
                "size_chars": "字符数",
                "size_bytes": "字节数"
            },
            "ai_analysis": {
                "keywords": ["关键词列表"],
                "summary": "AI生成摘要",
                "classification": {
                    "category": "分类类别",
                    "topics": ["主题列表"],
                    "importance": "重要性评分(1-10)",
                    "tags": ["标签列表"]
                }
            },
            "processing_metadata": {
                "ai_model_used": "使用的AI模型",
                "processing_time": "处理耗时(秒)",
                "response_time": "响应时间"
            }
        }

        print("   📄 JSON文件结构:")
        print(json.dumps(json_structure, ensure_ascii=False, indent=6))

        print(f"\n   📝 Markdown文件包含:")
        print(f"      • 文件基本信息 (名称、类型、大小、处理时间)")
        print(f"      • AI分析结果 (关键词、摘要、分类)")
        print(f"      • 处理元数据 (使用模型、耗时)")
        print(f"      • 内容预览 (前500字符)")

    def show_knowledge_system(self):
        """展示知识体系关系"""
        print_header("4️⃣ 知识体系结构和关系", 2)

        print("🔗 知识图谱组织结构:")

        knowledge_structure = {
            "documents": "文档节点存储",
            "keywords": "关键词到文档的映射",
            "categories": "分类到文档的映射",
            "relationships": "文档间关系网络",
            "topics": "主题聚合",
            "clusters": "主题聚类"
        }

        for component, description in knowledge_structure.items():
            print(f"   📊 {component}: {description}")

        print(f"\n🔍 关系分析机制:")
        print(f"   1. 关键词重叠度分析")
        print(f"   2. 主题相似性计算")
        print(f"   3. 内容语义关联")
        print(f"   4. 文档类型关系")

        print(f"\n📈 知识体系输出:")
        print(f"   📄 knowledge_index.json - 完整知识库索引")
        print(f"   📋 knowledge_catalog.md - 人类可读目录")
        print(f"   🔗 关系网络图 - 文档间关联关系")
        print(f"   🏷️ 主题聚类 - 按主题分组的文档集合")

        # 检查知识库文件
        if self.knowledge_base.exists():
            print(f"\n📂 知识库实际文件:")
            print_file_tree(self.knowledge_base, max_depth=2)

    def show_workflow_integration(self):
        """展示工作流集成"""
        print_header("5️⃣ n8n工作流集成", 2)

        print("🔄 自动化工作流:")

        workflows = {
            "文件处理工作流": {
                "触发器": "文件处理完成webhook",
                "条件": "重要性评分 > 7",
                "动作": "生成Markdown报告 → 上传到GitHub",
                "配置文件": "n8n_workflows/file_processing_workflow.json"
            },
            "GitHub上传工作流": {
                "触发器": "GitHub上传webhook",
                "动作": "提取数据 → API上传 → 返回结果",
                "配置文件": "n8n_workflows/github_upload_workflow.json"
            },
            "知识库更新工作流": {
                "触发器": "知识库更新webhook",
                "动作": "更新索引 → 生成目录 → 同步GitHub",
                "配置文件": "自动生成"
            }
        }

        for workflow_name, details in workflows.items():
            print(f"\n   🔧 {workflow_name}:")
            for key, value in details.items():
                print(f"      {key}: {value}")

        print(f"\n🌐 GitHub集成:")
        print(f"   📦 仓库: EwanCosmos/Ewandata")
        print(f"   🌿 分支: main")
        print(f"   📁 目标文件夹: knowledge_base/")
        print(f"   🔐 认证: GitHub Token")

        print(f"\n⚙️ API端点:")
        print(f"   POST /webhook/file-processed - 文件处理完成")
        print(f"   POST /webhook/github-upload - GitHub上传")
        print(f"   POST /webhook/knowledge-update - 知识库更新")

    def generate_workflow_diagram(self):
        """生成工作流程图"""
        print_header("6️⃣ 完整工作流程图", 2)

        print("""
🔄 Ewandata混合AI系统完整工作流程:

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  临时记文件夹    │───▶│  增强文件监控器   │───▶│  递归扫描文件    │
│ C:\\Users\\<USER>\\临时记│    │ EnhancedFileMonitor│    │ recursive_scan() │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   格式识别      │◀───│  多格式处理器     │◀───│   文件类型判断   │
│ 文本/Office/图片 │    │MultiFormatProcessor│    │ _get_file_type() │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   内容提取      │───▶│   任务分类器     │───▶│   复杂度评估     │
│ 文本/OCR/解析   │    │  TaskClassifier  │    │ComplexityEvaluator│
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   路由决策器     │───▶│  混合AI管理器    │
                       │  RouteDecider   │    │ HybridAIManager │
                       └─────────────────┘    └─────────────────┘
                                                         │
                    ┌────────────────────────────────────┼────────────────────────────────────┐
                    │                                    │                                    │
                    ▼                                    ▼                                    ▼
            ┌─────────────┐                    ┌─────────────┐                    ┌─────────────┐
            │  Qwen2-7B   │                    │CodeLlama-7B │                    │ Phi-3-mini  │
            │  中文理解    │                    │  代码分析   │                    │  快速响应   │
            └─────────────┘                    └─────────────┘                    └─────────────┘
                    │                                    │                                    │
                    └────────────────────────────────────┼────────────────────────────────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │   外部AI集成    │
                                               │ ChatGPT/Claude │
                                               └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   知识图谱      │◀───│   AI分析结果     │───▶│   结果输出      │
│ KnowledgeGraph  │    │ 关键词/摘要/分类  │    │ JSON + Markdown │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                                               │
         ▼                                               ▼
┌─────────────────┐                            ┌─────────────────┐
│  文档关联分析    │                            │  n8n工作流触发   │
│ 关系网络构建     │                            │ GitHub自动上传  │
└─────────────────┘                            └─────────────────┘
         │                                               │
         ▼                                               ▼
┌─────────────────┐                            ┌─────────────────┐
│  知识库索引     │                            │  知识库更新     │
│ 目录生成/聚类   │                            │ 版本控制/备份   │
└─────────────────┘                            └─────────────────┘
        """)

def main():
    """主函数"""
    tracker = WorkflowTracker()
    tracker.show_complete_workflow()
    tracker.generate_workflow_diagram()

    print_header("📋 快速查看指南")
    print("""
🔍 如何查看处理结果:

1️⃣ 查看处理后的文件:
   📁 位置: E:\\Ewandata\\data\\processed\\
   📄 JSON文件: 结构化数据，程序可读
   📝 MD文件: 格式化报告，人类可读

2️⃣ 查看知识库体系:
   📁 位置: E:\\Ewandata\\data\\knowledge_base\\
   📊 knowledge_index.json: 完整索引
   📋 knowledge_catalog.md: 目录结构

3️⃣ 查看工作流配置:
   📁 位置: E:\\Ewandata\\n8n_workflows\\
   ⚙️ 可直接导入n8n使用

4️⃣ 查看系统日志:
   📁 位置: E:\\Ewandata\\logs\\
   📝 详细的处理过程记录

5️⃣ 运行实时处理:
   🚀 python test_complete_solution.py
   🧪 python test_basic_functionality.py
    """)

if __name__ == "__main__":
    main()