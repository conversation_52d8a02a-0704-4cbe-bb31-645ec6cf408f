@echo off
chcp 65001 >nul
echo ========================================
echo    Ewandata 知识管理系统快速启动
echo ========================================
echo.

cd /d E:\Ewandata

echo [1/6] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.9+
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo [2/6] 检查CUDA环境...
nvidia-smi >nul 2>&1
if errorlevel 1 (
    echo ⚠️  CUDA环境未检测到，将使用CPU模式
) else (
    echo ✅ CUDA环境检查通过
)

echo.
echo [3/6] 创建虚拟环境...
if not exist "ewandata_env" (
    python -m venv ewandata_env
    echo ✅ 虚拟环境创建完成
) else (
    echo ✅ 虚拟环境已存在
)

echo.
echo [4/6] 激活虚拟环境并安装依赖...
call ewandata_env\Scripts\activate
echo 正在安装依赖包，这可能需要几分钟...
pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo [5/6] 创建必要目录结构...
if not exist "ewandata_system" mkdir ewandata_system
if not exist "data" mkdir data
if not exist "data\raw" mkdir data\raw
if not exist "data\processed" mkdir data\processed
if not exist "data\knowledge_base" mkdir data\knowledge_base
if not exist "data\vectors" mkdir data\vectors
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs
echo ✅ 目录结构创建完成

echo.
echo [6/6] 检查临时记文件夹...
set TEMP_FOLDER=C:\Users\<USER>\Desktop\临时记
if not exist "%TEMP_FOLDER%" (
    mkdir "%TEMP_FOLDER%"
    echo ✅ 临时记文件夹已创建: %TEMP_FOLDER%
) else (
    echo ✅ 临时记文件夹已存在: %TEMP_FOLDER%
)

echo.
echo ========================================
echo    🎉 Ewandata系统初始化完成！
echo ========================================
echo.
echo 下一步操作：
echo 1. 运行 'start_system.bat' 启动完整系统
echo 2. 或者手动启动各个组件：
echo    - API服务: python api/main.py
echo    - Web界面: streamlit run app/streamlit_app.py
echo    - 文件监控: python services/file_monitor.py
echo.
echo 系统访问地址：
echo - Web管理界面: http://localhost:8501
echo - API文档: http://localhost:8000/docs
echo.
pause