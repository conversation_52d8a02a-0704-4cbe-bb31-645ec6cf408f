"""
Ewandata 项目跟踪与关联分析系统
自动发现和分析E盘项目，识别项目间的关联性
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
import hashlib
import sqlite3
from collections import defaultdict, Counter
import threading
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProjectTracker:
    """项目跟踪与分析系统"""

    def __init__(self, monitor_path: str = "E:\\", config: Optional[Dict] = None):
        self.monitor_path = Path(monitor_path)
        self.config = config or {}

        # 项目识别模式
        self.project_patterns = {
            'python': {
                'files': ['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'],
                'dirs': ['venv', 'env', '__pycache__'],
                'extensions': ['.py']
            },
            'nodejs': {
                'files': ['package.json', 'yarn.lock', 'package-lock.json'],
                'dirs': ['node_modules', 'dist', 'build'],
                'extensions': ['.js', '.ts', '.jsx', '.tsx']
            },
            'java': {
                'files': ['pom.xml', 'build.gradle', 'build.xml'],
                'dirs': ['target', 'build', 'src'],
                'extensions': ['.java', '.class', '.jar']
            },
            'csharp': {
                'files': ['*.csproj', '*.sln', 'packages.config'],
                'dirs': ['bin', 'obj', 'packages'],
                'extensions': ['.cs', '.dll', '.exe']
            },
            'web': {
                'files': ['index.html', 'webpack.config.js', 'gulpfile.js'],
                'dirs': ['css', 'js', 'assets', 'static'],
                'extensions': ['.html', '.css', '.js', '.scss']
            },
            'data': {
                'files': ['*.ipynb', 'requirements.txt', 'environment.yml'],
                'dirs': ['data', 'notebooks', 'models'],
                'extensions': ['.ipynb', '.csv', '.json', '.pkl']
            },
            'general': {
                'files': ['README.md', '.git', 'LICENSE'],
                'dirs': ['.git', 'docs', 'documentation'],
                'extensions': ['.md', '.txt', '.rst']
            }
        }

        # 数据库配置
        self.db_path = Path("E:/Ewandata/data/projects.db")
        self.init_database()

        # 缓存
        self.projects_cache = {}
        self.last_scan_time = None

        logger.info(f"项目跟踪器初始化完成，监控路径: {self.monitor_path}")

    def init_database(self):
        """初始化项目数据库"""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        self.conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
        self.conn.row_factory = sqlite3.Row

        # 创建项目表
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT,
                path TEXT UNIQUE,
                type TEXT,
                size_mb REAL,
                file_count INTEGER,
                last_modified TIMESTAMP,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                git_repo BOOLEAN,
                git_remote TEXT,
                languages TEXT,
                dependencies TEXT,
                description TEXT,
                metadata TEXT
            )
        ''')

        # 创建项目关联表
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS project_relations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project1_id TEXT,
                project2_id TEXT,
                relation_type TEXT,
                similarity_score REAL,
                details TEXT,
                created_at TIMESTAMP,
                FOREIGN KEY (project1_id) REFERENCES projects (id),
                FOREIGN KEY (project2_id) REFERENCES projects (id)
            )
        ''')

        # 创建扫描历史表
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS scan_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                scan_time TIMESTAMP,
                projects_found INTEGER,
                scan_duration REAL,
                scan_path TEXT
            )
        ''')

        # 创建索引
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_projects_type ON projects(type)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_projects_modified ON projects(last_modified)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_relations_score ON project_relations(similarity_score)')

        self.conn.commit()
        logger.info("项目数据库初始化完成")

    def scan_projects(self, force_rescan: bool = False) -> Dict[str, Any]:
        """扫描项目"""
        start_time = time.time()

        if not force_rescan and self.last_scan_time:
            time_since_scan = time.time() - self.last_scan_time
            if time_since_scan < 3600:  # 1小时内不重复扫描
                logger.info("距离上次扫描不足1小时，跳过扫描")
                return self._get_scan_summary()

        logger.info(f"开始扫描项目: {self.monitor_path}")

        projects_found = 0
        projects_data = {}

        try:
            # 遍历目录
            for root, dirs, files in os.walk(self.monitor_path):
                # 跳过系统目录和隐藏目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in [
                    'System Volume Information', '$RECYCLE.BIN', 'Windows', 'Program Files',
                    'Program Files (x86)', 'ProgramData', 'Users'
                ]]

                root_path = Path(root)

                # 检查是否为项目目录
                if self._is_project_directory(root_path, files, dirs):
                    project_info = self._analyze_project(root_path, files, dirs)
                    if project_info:
                        projects_data[str(root_path)] = project_info
                        projects_found += 1

                        # 存储到数据库
                        self._store_project(project_info)

                # 限制扫描深度
                if len(root_path.parts) - len(self.monitor_path.parts) > 3:
                    dirs.clear()

            # 分析项目关联
            self._analyze_project_relations(projects_data)

            # 记录扫描历史
            scan_duration = time.time() - start_time
            self._record_scan_history(projects_found, scan_duration)

            self.projects_cache = projects_data
            self.last_scan_time = time.time()

            logger.info(f"扫描完成，发现 {projects_found} 个项目，耗时 {scan_duration:.2f}秒")

            return {
                'projects_found': projects_found,
                'scan_duration': scan_duration,
                'scan_time': datetime.now().isoformat(),
                'projects': list(projects_data.keys())
            }

        except Exception as e:
            logger.error(f"项目扫描失败: {e}")
            return {
                'error': str(e),
                'projects_found': 0,
                'scan_duration': time.time() - start_time
            }

    def _is_project_directory(self, path: Path, files: List[str], dirs: List[str]) -> bool:
        """判断是否为项目目录"""
        # 检查特征文件
        for project_type, patterns in self.project_patterns.items():
            for pattern_file in patterns['files']:
                if any(pattern_file in f or f.endswith(pattern_file.replace('*', '')) for f in files):
                    return True

            # 检查特征目录
            for pattern_dir in patterns['dirs']:
                if pattern_dir in dirs:
                    return True

        # 检查是否有足够的代码文件
        code_files = 0
        for file in files:
            if any(file.endswith(ext) for ext_list in [p['extensions'] for p in self.project_patterns.values()] for ext in ext_list):
                code_files += 1

        return code_files >= 3  # 至少3个代码文件

    def _analyze_project(self, path: Path, files: List[str], dirs: List[str]) -> Optional[Dict[str, Any]]:
        """分析项目详情"""
        try:
            # 基本信息
            stat = path.stat()
            project_info = {
                'id': self._generate_project_id(path),
                'name': path.name,
                'path': str(path),
                'created_at': datetime.now().isoformat(),
                'last_modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
            }

            # 检测项目类型
            project_info['type'] = self._detect_project_type(files, dirs)

            # 计算项目大小和文件数
            size_info = self._calculate_project_size(path)
            project_info.update(size_info)

            # Git信息
            git_info = self._get_git_info(path)
            project_info.update(git_info)

            # 语言分析
            project_info['languages'] = self._analyze_languages(path, files)

            # 依赖分析
            project_info['dependencies'] = self._analyze_dependencies(path, files)

            # 项目描述
            project_info['description'] = self._extract_description(path, files)

            return project_info

        except Exception as e:
            logger.error(f"分析项目失败 {path}: {e}")
            return None

    def _generate_project_id(self, path: Path) -> str:
        """生成项目ID"""
        path_str = str(path).lower()
        return hashlib.md5(path_str.encode()).hexdigest()[:12]

    def _detect_project_type(self, files: List[str], dirs: List[str]) -> str:
        """检测项目类型"""
        scores = defaultdict(int)

        for project_type, patterns in self.project_patterns.items():
            # 检查特征文件
            for pattern_file in patterns['files']:
                if any(pattern_file in f or f.endswith(pattern_file.replace('*', '')) for f in files):
                    scores[project_type] += 3

            # 检查特征目录
            for pattern_dir in patterns['dirs']:
                if pattern_dir in dirs:
                    scores[project_type] += 2

            # 检查文件扩展名
            for ext in patterns['extensions']:
                if any(f.endswith(ext) for f in files):
                    scores[project_type] += 1

        if scores:
            return max(scores.items(), key=lambda x: x[1])[0]
        return 'unknown'

    def _calculate_project_size(self, path: Path) -> Dict[str, Any]:
        """计算项目大小和文件数"""
        total_size = 0
        file_count = 0

        try:
            for file_path in path.rglob('*'):
                if file_path.is_file():
                    try:
                        total_size += file_path.stat().st_size
                        file_count += 1
                    except (OSError, PermissionError):
                        continue
        except (OSError, PermissionError):
            pass

        return {
            'size_mb': total_size / (1024 * 1024),
            'file_count': file_count
        }

    def _get_git_info(self, path: Path) -> Dict[str, Any]:
        """获取Git信息"""
        git_dir = path / '.git'
        if git_dir.exists():
            try:
                # 尝试获取远程URL
                config_file = git_dir / 'config'
                remote_url = ""
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'url =' in content:
                            for line in content.split('\n'):
                                if 'url =' in line:
                                    remote_url = line.split('=', 1)[1].strip()
                                    break

                return {
                    'git_repo': True,
                    'git_remote': remote_url
                }
            except:
                return {'git_repo': True, 'git_remote': ''}

        return {'git_repo': False, 'git_remote': ''}

    def _analyze_languages(self, path: Path, files: List[str]) -> List[str]:
        """分析项目使用的编程语言"""
        language_counts = Counter()

        language_extensions = {
            'Python': ['.py', '.pyw', '.pyx'],
            'JavaScript': ['.js', '.jsx', '.mjs'],
            'TypeScript': ['.ts', '.tsx'],
            'Java': ['.java'],
            'C#': ['.cs'],
            'C++': ['.cpp', '.cc', '.cxx', '.c++'],
            'C': ['.c', '.h'],
            'HTML': ['.html', '.htm'],
            'CSS': ['.css', '.scss', '.sass'],
            'PHP': ['.php'],
            'Ruby': ['.rb'],
            'Go': ['.go'],
            'Rust': ['.rs'],
            'Swift': ['.swift'],
            'Kotlin': ['.kt'],
            'Dart': ['.dart'],
            'R': ['.r', '.R'],
            'MATLAB': ['.m'],
            'Shell': ['.sh', '.bash', '.zsh'],
            'PowerShell': ['.ps1'],
            'SQL': ['.sql'],
            'JSON': ['.json'],
            'XML': ['.xml'],
            'YAML': ['.yml', '.yaml'],
            'Markdown': ['.md', '.markdown']
        }

        for file in files:
            for language, extensions in language_extensions.items():
                if any(file.lower().endswith(ext) for ext in extensions):
                    language_counts[language] += 1

        # 返回前5种语言
        return [lang for lang, _ in language_counts.most_common(5)]

    def _analyze_dependencies(self, path: Path, files: List[str]) -> List[str]:
        """分析项目依赖"""
        dependencies = []

        # Python依赖
        if 'requirements.txt' in files:
            try:
                req_file = path / 'requirements.txt'
                with open(req_file, 'r', encoding='utf-8') as f:
                    deps = [line.strip().split('==')[0].split('>=')[0].split('<=')[0]
                           for line in f if line.strip() and not line.startswith('#')]
                    dependencies.extend(deps[:10])  # 限制前10个
            except:
                pass

        # Node.js依赖
        if 'package.json' in files:
            try:
                pkg_file = path / 'package.json'
                with open(pkg_file, 'r', encoding='utf-8') as f:
                    import json
                    data = json.load(f)
                    if 'dependencies' in data:
                        dependencies.extend(list(data['dependencies'].keys())[:10])
            except:
                pass

        return dependencies

    def _extract_description(self, path: Path, files: List[str]) -> str:
        """提取项目描述"""
        # 尝试从README文件提取
        readme_files = [f for f in files if f.lower().startswith('readme')]
        if readme_files:
            try:
                readme_path = path / readme_files[0]
                with open(readme_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 提取第一段作为描述
                    lines = content.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and not line.startswith('#') and len(line) > 20:
                            return line[:200]  # 限制长度
            except:
                pass

        return f"项目位于 {path.name}"

    def _store_project(self, project_info: Dict[str, Any]):
        """存储项目到数据库"""
        try:
            self.conn.execute('''
                INSERT OR REPLACE INTO projects
                (id, name, path, type, size_mb, file_count, last_modified,
                 created_at, updated_at, git_repo, git_remote, languages,
                 dependencies, description, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                project_info['id'],
                project_info['name'],
                project_info['path'],
                project_info['type'],
                project_info['size_mb'],
                project_info['file_count'],
                project_info['last_modified'],
                project_info['created_at'],
                datetime.now().isoformat(),
                project_info['git_repo'],
                project_info['git_remote'],
                ','.join(project_info['languages']),
                ','.join(project_info['dependencies']),
                project_info['description'],
                json.dumps({k: v for k, v in project_info.items() if k not in [
                    'id', 'name', 'path', 'type', 'size_mb', 'file_count',
                    'last_modified', 'created_at', 'git_repo', 'git_remote',
                    'languages', 'dependencies', 'description'
                ]})
            ))
            self.conn.commit()
        except Exception as e:
            logger.error(f"存储项目失败: {e}")

    def _analyze_project_relations(self, projects_data: Dict[str, Any]):
        """分析项目关联性"""
        try:
            # 清除旧的关联数据
            self.conn.execute('DELETE FROM project_relations')

            project_list = list(projects_data.values())

            for i, project1 in enumerate(project_list):
                for j, project2 in enumerate(project_list[i+1:], i+1):
                    similarity = self._calculate_similarity(project1, project2)

                    if similarity > 0.3:  # 相似度阈值
                        relation_type = self._determine_relation_type(project1, project2)

                        self.conn.execute('''
                            INSERT INTO project_relations
                            (project1_id, project2_id, relation_type, similarity_score,
                             details, created_at)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ''', (
                            project1['id'],
                            project2['id'],
                            relation_type,
                            similarity,
                            json.dumps({
                                'common_languages': list(set(project1['languages']) & set(project2['languages'])),
                                'common_dependencies': list(set(project1['dependencies']) & set(project2['dependencies']))
                            }),
                            datetime.now().isoformat()
                        ))

            self.conn.commit()

        except Exception as e:
            logger.error(f"分析项目关联失败: {e}")

    def _calculate_similarity(self, project1: Dict, project2: Dict) -> float:
        """计算项目相似度"""
        score = 0.0

        # 类型相似性
        if project1['type'] == project2['type']:
            score += 0.3

        # 语言相似性
        common_languages = set(project1['languages']) & set(project2['languages'])
        if common_languages:
            score += 0.3 * (len(common_languages) / max(len(project1['languages']), len(project2['languages'])))

        # 依赖相似性
        common_deps = set(project1['dependencies']) & set(project2['dependencies'])
        if common_deps:
            score += 0.2 * (len(common_deps) / max(len(project1['dependencies']), len(project2['dependencies'])))

        # Git仓库相似性
        if project1['git_repo'] and project2['git_repo']:
            score += 0.1

        # 大小相似性
        size_ratio = min(project1['size_mb'], project2['size_mb']) / max(project1['size_mb'], project2['size_mb'])
        if size_ratio > 0.5:
            score += 0.1

        return min(score, 1.0)

    def _determine_relation_type(self, project1: Dict, project2: Dict) -> str:
        """确定关联类型"""
        if project1['type'] == project2['type']:
            return 'same_type'

        common_languages = set(project1['languages']) & set(project2['languages'])
        if common_languages:
            return 'shared_technology'

        common_deps = set(project1['dependencies']) & set(project2['dependencies'])
        if common_deps:
            return 'shared_dependencies'

        return 'general_similarity'

    def _record_scan_history(self, projects_found: int, scan_duration: float):
        """记录扫描历史"""
        try:
            self.conn.execute('''
                INSERT INTO scan_history (scan_time, projects_found, scan_duration, scan_path)
                VALUES (?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                projects_found,
                scan_duration,
                str(self.monitor_path)
            ))
            self.conn.commit()
        except Exception as e:
            logger.error(f"记录扫描历史失败: {e}")

    def _get_scan_summary(self) -> Dict[str, Any]:
        """获取扫描摘要"""
        try:
            cursor = self.conn.execute('''
                SELECT COUNT(*) as total_projects,
                       MAX(scan_time) as last_scan,
                       AVG(scan_duration) as avg_duration
                FROM scan_history
            ''')
            result = cursor.fetchone()

            return {
                'projects_found': result['total_projects'] if result else 0,
                'last_scan': result['last_scan'] if result else None,
                'avg_duration': result['avg_duration'] if result else 0,
                'scan_time': datetime.now().isoformat(),
                'projects': []
            }
        except Exception as e:
            logger.error(f"获取扫描摘要失败: {e}")
            return {
                'projects_found': 0,
                'scan_time': datetime.now().isoformat(),
                'projects': []
            }