# Ewandata 知识管理系统依赖包

# 核心AI框架
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
transformers>=4.30.0
accelerate>=0.20.0
sentence-transformers>=2.2.0

# Web框架和API
fastapi>=0.100.0
uvicorn>=0.22.0
streamlit>=1.25.0
pydantic>=2.0.0

# 数据库和存储
chromadb>=0.4.0

# 文档处理
langchain>=0.0.200
PyPDF2>=3.0.0
python-docx>=0.8.11
python-pptx>=0.6.21
openpyxl>=3.1.0
pillow>=10.0.0
paddleocr>=2.7.0

# 文件监控和处理
watchdog>=3.0.0
schedule>=1.2.0
gitpython>=3.1.0

# 数据处理和可视化
pandas>=2.0.0
numpy>=1.24.0
plotly>=5.15.0
matplotlib>=3.7.0

# 网络和爬虫
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.10.0

# 工具库
python-dotenv>=1.0.0
tqdm>=4.65.0
loguru>=0.7.0
pyyaml>=6.0