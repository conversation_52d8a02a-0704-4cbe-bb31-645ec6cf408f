"""
修复GitHub同步问题的脚本
解决知识库数据库缺失和文件同步问题
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加系统路径
sys.path.append('ewandata_system')

def fix_gitignore():
    """修复.gitignore文件，排除不必要的文件"""
    print("🔧 修复.gitignore文件...")
    
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
ewandata_env/
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/

# Database backups
*.db.backup
*.db.bak

# Large model files
models/*.bin
models/*.safetensors

# Test files
test_*.py
*_test.py
comprehensive_test_suite.py
github_sync_test.py
demo_system.py

# Augment files
.augment/
"""
    
    with open(".gitignore", "w", encoding="utf-8") as f:
        f.write(gitignore_content)
    
    print("✅ .gitignore文件已更新")

def initialize_knowledge_base():
    """初始化知识库数据库"""
    print("🔧 初始化知识库数据库...")
    
    try:
        from storage.knowledge_base import KnowledgeBase
        
        # 创建知识库实例，这会自动创建数据库
        kb = KnowledgeBase("E:/Ewandata")
        
        # 创建一个测试文档确保数据库正常工作
        test_doc = {
            'title': 'GitHub同步修复测试',
            'content': '''这是一个用于验证GitHub同步修复的测试文档。

## 修复内容
1. 重新初始化知识库数据库
2. 修复.gitignore文件
3. 清理不必要的文件
4. 确保数据库正确同步到GitHub

## 系统状态
- 知识库数据库: 已修复
- GitHub同步: 准备就绪
- 文件管理: 已优化

修复时间: 2025-01-03
''',
            'file_path': 'github_sync_fix_test.md',
            'file_hash': 'fix_test_12345',
            'type': 'markdown',
            'size': 1024,
            'tags': ['修复', 'GitHub', '同步', '测试'],
            'keywords': ['GitHub', '同步', '修复', '数据库'],
            'summary': 'GitHub同步功能修复验证文档',
            'importance_score': 0.9
        }
        
        doc_id = kb.store_document(test_doc)
        print(f"✅ 知识库数据库初始化成功，测试文档ID: {doc_id}")
        
        # 验证数据库文件存在
        db_path = Path("E:/Ewandata/data/knowledge_base.db")
        if db_path.exists():
            size = db_path.stat().st_size
            print(f"✅ 数据库文件已创建: {size} bytes")
        else:
            print("❌ 数据库文件创建失败")
            return False
        
        kb.close()
        return True
        
    except Exception as e:
        print(f"❌ 知识库初始化失败: {str(e)}")
        return False

def clean_git_cache():
    """清理Git缓存"""
    print("🔧 清理Git缓存...")
    
    try:
        os.chdir("E:/Ewandata")
        
        # 移除所有文件从Git跟踪
        subprocess.run(['git', 'rm', '-r', '--cached', '.'], 
                      capture_output=True, text=True)
        
        # 重新添加文件（会应用新的.gitignore）
        subprocess.run(['git', 'add', '.'], 
                      capture_output=True, text=True)
        
        print("✅ Git缓存已清理")
        return True
        
    except Exception as e:
        print(f"❌ Git缓存清理失败: {str(e)}")
        return False

def commit_changes():
    """提交修复后的变更"""
    print("🔧 提交修复后的变更...")
    
    try:
        os.chdir("E:/Ewandata")
        
        # 检查状态
        status_result = subprocess.run(['git', 'status', '--porcelain'], 
                                     capture_output=True, text=True, check=True)
        
        if status_result.stdout.strip():
            # 创建提交
            commit_message = "修复GitHub同步问题：初始化知识库数据库，优化.gitignore"
            commit_result = subprocess.run(['git', 'commit', '-m', commit_message], 
                                         capture_output=True, text=True)
            
            if commit_result.returncode == 0:
                print(f"✅ 提交成功: {commit_message}")
                print(f"   {commit_result.stdout.strip()}")
                return True
            else:
                print(f"⚠️ 提交结果: {commit_result.stdout.strip()}")
                return True  # 可能没有变更需要提交
        else:
            print("ℹ️ 没有变更需要提交")
            return True
            
    except Exception as e:
        print(f"❌ 提交失败: {str(e)}")
        return False

def verify_fix():
    """验证修复结果"""
    print("🔍 验证修复结果...")
    
    # 检查知识库数据库
    db_path = Path("E:/Ewandata/data/knowledge_base.db")
    if db_path.exists():
        size = db_path.stat().st_size
        print(f"✅ 知识库数据库: {size} bytes")
    else:
        print("❌ 知识库数据库仍然缺失")
        return False
    
    # 检查.gitignore
    gitignore_path = Path("E:/Ewandata/.gitignore")
    if gitignore_path.exists():
        print("✅ .gitignore文件存在")
    else:
        print("❌ .gitignore文件缺失")
        return False
    
    # 检查Git状态
    try:
        os.chdir("E:/Ewandata")
        status_result = subprocess.run(['git', 'status', '--porcelain'], 
                                     capture_output=True, text=True, check=True)
        
        changes = status_result.stdout.strip().split('\n') if status_result.stdout.strip() else []
        print(f"📊 Git状态: {len(changes)} 个变更")
        
        if len(changes) <= 10:  # 合理的变更数量
            print("✅ Git状态正常")
        else:
            print("⚠️ 仍有较多未提交变更")
            
    except Exception as e:
        print(f"❌ Git状态检查失败: {str(e)}")
        return False
    
    return True

def main():
    """主修复函数"""
    print("🚀 开始修复GitHub同步问题...")
    print("=" * 60)
    
    steps = [
        ("修复.gitignore文件", fix_gitignore),
        ("初始化知识库数据库", initialize_knowledge_base),
        ("清理Git缓存", clean_git_cache),
        ("提交修复变更", commit_changes),
        ("验证修复结果", verify_fix)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n⏳ {step_name}...")
        try:
            success = step_func()
            results.append((step_name, success))
            
            if success:
                print(f"✅ {step_name}: 成功")
            else:
                print(f"❌ {step_name}: 失败")
                
        except Exception as e:
            print(f"❌ {step_name}: 异常 - {str(e)}")
            results.append((step_name, False))
    
    # 显示修复结果
    print("\n" + "=" * 60)
    print("修复结果汇总:")
    print("=" * 60)
    
    total_steps = len(results)
    successful_steps = sum(1 for _, success in results if success)
    
    for step_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{step_name}: {status}")
    
    print(f"\n📊 总体结果: {successful_steps}/{total_steps} 步骤成功")
    
    if successful_steps == total_steps:
        print("\n🎉 GitHub同步问题修复完成！")
        print("现在可以安全地进行GitHub同步操作。")
        print("\n下一步操作:")
        print("1. 运行 'git push origin main' 推送到GitHub")
        print("2. 检查GitHub仓库确认数据库文件已上传")
        print("3. 验证知识库功能正常工作")
    elif successful_steps >= total_steps * 0.8:
        print("\n✅ 大部分问题已修复，系统基本可用。")
        print("建议检查失败的步骤并手动修复。")
    else:
        print("\n⚠️ 修复过程中遇到多个问题。")
        print("建议检查错误信息并重新运行修复脚本。")

if __name__ == "__main__":
    main()
