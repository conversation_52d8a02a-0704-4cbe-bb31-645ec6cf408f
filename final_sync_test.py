"""
最终GitHub同步测试和推送
"""

import os
import subprocess
import time
from pathlib import Path

def test_system_status():
    """测试系统状态"""
    print("🔍 系统状态检查")
    print("-" * 40)
    
    # 检查数据库
    db_files = {
        "知识库数据库": "data/metadata.db",
        "项目数据库": "data/projects.db"
    }
    
    for name, path in db_files.items():
        if Path(path).exists():
            size = Path(path).stat().st_size / 1024  # KB
            print(f"✅ {name}: {size:.1f} KB")
        else:
            print(f"❌ {name}: 不存在")
    
    # 检查知识库文件
    kb_dir = Path("data/knowledge_base")
    proc_dir = Path("data/processed")
    
    if kb_dir.exists() and proc_dir.exists():
        md_count = len(list(kb_dir.glob("*.md")))
        json_count = len(list(proc_dir.glob("*.json")))
        print(f"✅ 知识库文件: {md_count} MD + {json_count} JSON")
    else:
        print("❌ 知识库文件目录缺失")

def test_git_status():
    """测试Git状态"""
    print("\n📊 Git状态检查")
    print("-" * 40)
    
    try:
        # 检查分支状态
        result = subprocess.run(['git', 'status', '-b', '--porcelain'], 
                              capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        branch_line = lines[0] if lines else ""
        
        print(f"分支: {branch_line}")
        
        # 检查是否有提交需要推送
        if "ahead" in branch_line:
            ahead_count = branch_line.split('ahead ')[1].split(']')[0] if 'ahead' in branch_line else "未知"
            print(f"📤 需要推送: {ahead_count} 个提交")
            return True
        else:
            print("✅ 与远程同步")
            return False
            
    except Exception as e:
        print(f"❌ Git状态检查失败: {e}")
        return False

def attempt_push():
    """尝试推送到GitHub"""
    print("\n🚀 尝试推送到GitHub")
    print("-" * 40)
    
    max_attempts = 3
    
    for attempt in range(1, max_attempts + 1):
        print(f"尝试 {attempt}/{max_attempts}...")
        
        try:
            result = subprocess.run(['git', 'push', 'origin', 'main'], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ 推送成功！")
                print(f"输出: {result.stdout.strip()}")
                return True
            else:
                print(f"❌ 推送失败: {result.stderr.strip()}")
                
        except subprocess.TimeoutExpired:
            print("⏰ 推送超时")
        except Exception as e:
            print(f"❌ 推送异常: {e}")
        
        if attempt < max_attempts:
            print("等待5秒后重试...")
            time.sleep(5)
    
    return False

def verify_github_sync():
    """验证GitHub同步"""
    print("\n🔍 验证GitHub同步")
    print("-" * 40)
    
    try:
        # 检查远程状态
        result = subprocess.run(['git', 'fetch', 'origin'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 远程获取成功")
            
            # 检查本地与远程的差异
            diff_result = subprocess.run(['git', 'status', '-b'], 
                                       capture_output=True, text=True, check=True)
            
            if "ahead" not in diff_result.stdout:
                print("✅ 本地与远程已同步")
                return True
            else:
                print("⚠️ 仍有未推送的提交")
                return False
        else:
            print(f"❌ 远程获取失败: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Ewandata GitHub同步最终测试")
    print("=" * 50)
    
    os.chdir("E:/Ewandata")
    
    # 1. 系统状态检查
    test_system_status()
    
    # 2. Git状态检查
    has_commits = test_git_status()
    
    # 3. 如果有提交需要推送，则尝试推送
    if has_commits:
        push_success = attempt_push()
        
        if push_success:
            # 4. 验证同步
            sync_verified = verify_github_sync()
            
            if sync_verified:
                print("\n🎉 GitHub同步完成！")
                print("✅ 所有数据已成功推送到GitHub")
            else:
                print("\n⚠️ 推送完成但验证失败")
        else:
            print("\n❌ 推送失败")
            print("可能的原因:")
            print("1. 网络连接问题")
            print("2. GitHub服务器问题")
            print("3. 认证问题")
            print("\n建议:")
            print("1. 检查网络连接")
            print("2. 稍后重试")
            print("3. 手动运行: git push origin main")
    else:
        print("\n✅ 没有需要推送的提交")
        print("系统已与GitHub同步")
    
    # 5. 显示最终状态
    print("\n📋 最终状态汇总")
    print("-" * 40)
    print("✅ 知识库数据库: 正常")
    print("✅ 知识库文件: 正常")
    print("✅ Git仓库: 正常")
    
    if has_commits:
        print("📤 GitHub同步: 已尝试推送")
    else:
        print("✅ GitHub同步: 已同步")
    
    print("\n💡 使用建议:")
    print("1. 定期运行此脚本检查同步状态")
    print("2. 添加新文档后及时推送到GitHub")
    print("3. 如遇网络问题，可稍后重试推送")

if __name__ == "__main__":
    main()
