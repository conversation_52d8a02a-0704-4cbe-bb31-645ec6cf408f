# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Exception.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import WidthConfig_pb2 as streamlit_dot_proto_dot_WidthConfig__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fstreamlit/proto/Exception.proto\x1a!streamlit/proto/WidthConfig.proto\"\x9e\x01\n\tException\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1b\n\x13message_is_markdown\x18\x04 \x01(\x08\x12\x13\n\x0bstack_trace\x18\x03 \x03(\t\x12\x12\n\nis_warning\x18\x05 \x01(\x08\x12,\n\x0cwidth_config\x18\x06 \x01(\x0b\x32\x16.streamlit.WidthConfigB.\n\x1c\x63om.snowflake.apps.streamlitB\x0e\x45xceptionProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Exception_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\016ExceptionProto'
  _globals['_EXCEPTION']._serialized_start=71
  _globals['_EXCEPTION']._serialized_end=229
# @@protoc_insertion_point(module_scope)
