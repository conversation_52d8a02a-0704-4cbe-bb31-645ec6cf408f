"""
n8n工作流管理器
实现GitHub自动上传和工作流集成
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class N8nWorkflowManager:
    """n8n工作流管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化n8n工作流管理器
        
        Args:
            config: n8n配置
        """
        self.config = config
        self.base_url = config.get('base_url', 'http://localhost:5678')
        self.api_key = config.get('api_key', '')
        self.github_config = config.get('github', {})
        
        # 工作流端点
        self.workflows = {
            'file_processed': config.get('webhooks', {}).get('file_processed', '/webhook/file-processed'),
            'github_upload': config.get('webhooks', {}).get('github_upload', '/webhook/github-upload'),
            'knowledge_update': config.get('webhooks', {}).get('knowledge_update', '/webhook/knowledge-update')
        }
        
        logger.info("n8n工作流管理器初始化完成")
    
    async def trigger_file_processed(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        触发文件处理完成工作流
        
        Args:
            file_data: 文件处理数据
            
        Returns:
            工作流执行结果
        """
        try:
            webhook_url = f"{self.base_url}{self.workflows['file_processed']}"
            
            # 构建工作流数据
            workflow_data = {
                'event': 'file_processed',
                'timestamp': datetime.now().isoformat(),
                'file_info': file_data.get('file_info', {}),
                'ai_analysis': file_data.get('ai_analysis', {}),
                'processing_metadata': file_data.get('processing_metadata', {}),
                'github_config': self.github_config
            }
            
            # 调用n8n webhook
            result = await self._call_webhook(webhook_url, workflow_data)
            
            if result.get('success', False):
                logger.info(f"文件处理工作流触发成功: {file_data.get('file_info', {}).get('name', 'unknown')}")
            else:
                logger.warning(f"文件处理工作流触发失败: {result.get('error', 'unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"触发文件处理工作流失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def trigger_github_upload(self, upload_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        触发GitHub上传工作流
        
        Args:
            upload_data: 上传数据
            
        Returns:
            上传结果
        """
        try:
            webhook_url = f"{self.base_url}{self.workflows['github_upload']}"
            
            # 构建GitHub上传数据
            workflow_data = {
                'event': 'github_upload',
                'timestamp': datetime.now().isoformat(),
                'repository': self.github_config.get('repository', 'EwanCosmos/Ewandata'),
                'branch': self.github_config.get('branch', 'main'),
                'files': upload_data.get('files', []),
                'commit_message': upload_data.get('commit_message', f"Auto upload - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"),
                'github_token': self.github_config.get('token', ''),
                'target_folder': upload_data.get('target_folder', 'knowledge_base')
            }
            
            # 调用GitHub上传工作流
            result = await self._call_webhook(webhook_url, workflow_data)
            
            if result.get('success', False):
                logger.info(f"GitHub上传工作流触发成功: {len(upload_data.get('files', []))} 个文件")
            else:
                logger.warning(f"GitHub上传工作流触发失败: {result.get('error', 'unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"触发GitHub上传工作流失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def trigger_knowledge_update(self, knowledge_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        触发知识库更新工作流
        
        Args:
            knowledge_data: 知识库数据
            
        Returns:
            更新结果
        """
        try:
            webhook_url = f"{self.base_url}{self.workflows['knowledge_update']}"
            
            # 构建知识库更新数据
            workflow_data = {
                'event': 'knowledge_update',
                'timestamp': datetime.now().isoformat(),
                'knowledge_index': knowledge_data.get('index', {}),
                'new_documents': knowledge_data.get('new_documents', []),
                'updated_relationships': knowledge_data.get('relationships', []),
                'topic_clusters': knowledge_data.get('clusters', {}),
                'github_config': self.github_config
            }
            
            # 调用知识库更新工作流
            result = await self._call_webhook(webhook_url, workflow_data)
            
            if result.get('success', False):
                logger.info("知识库更新工作流触发成功")
            else:
                logger.warning(f"知识库更新工作流触发失败: {result.get('error', 'unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"触发知识库更新工作流失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _call_webhook(self, webhook_url: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用n8n webhook
        
        Args:
            webhook_url: webhook URL
            data: 发送数据
            
        Returns:
            响应结果
        """
        try:
            headers = {
                'Content-Type': 'application/json'
            }
            
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        return {
                            'success': True,
                            'data': result,
                            'status_code': response.status
                        }
                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {error_text}",
                            'status_code': response.status
                        }
                        
        except asyncio.TimeoutError:
            return {
                'success': False,
                'error': 'Webhook调用超时',
                'status_code': 0
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'status_code': 0
            }
    
    async def create_github_upload_workflow(self) -> str:
        """
        创建GitHub上传工作流配置
        
        Returns:
            工作流配置JSON
        """
        workflow_config = {
            "name": "Ewandata GitHub Auto Upload",
            "nodes": [
                {
                    "parameters": {
                        "httpMethod": "POST",
                        "path": "github-upload",
                        "responseMode": "responseNode",
                        "options": {}
                    },
                    "id": "webhook-trigger",
                    "name": "Webhook Trigger",
                    "type": "n8n-nodes-base.webhook",
                    "typeVersion": 1,
                    "position": [240, 300]
                },
                {
                    "parameters": {
                        "values": {
                            "string": [
                                {
                                    "name": "repository",
                                    "value": "={{ $json.repository }}"
                                },
                                {
                                    "name": "branch", 
                                    "value": "={{ $json.branch }}"
                                },
                                {
                                    "name": "commit_message",
                                    "value": "={{ $json.commit_message }}"
                                }
                            ]
                        },
                        "options": {}
                    },
                    "id": "extract-data",
                    "name": "Extract Data",
                    "type": "n8n-nodes-base.set",
                    "typeVersion": 1,
                    "position": [460, 300]
                },
                {
                    "parameters": {
                        "authentication": "headerAuth",
                        "requestMethod": "PUT",
                        "url": "=https://api.github.com/repos/{{ $json.repository }}/contents/{{ $json.target_folder }}/{{ $json.filename }}",
                        "jsonParameters": True,
                        "bodyParametersJson": "={\n  \"message\": \"{{ $json.commit_message }}\",\n  \"content\": \"{{ $json.file_content_base64 }}\",\n  \"branch\": \"{{ $json.branch }}\"\n}",
                        "options": {}
                    },
                    "id": "github-upload",
                    "name": "GitHub Upload",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 3,
                    "position": [680, 300],
                    "credentials": {
                        "httpHeaderAuth": {
                            "id": "github-token",
                            "name": "GitHub Token"
                        }
                    }
                },
                {
                    "parameters": {
                        "respondWith": "json",
                        "responseBody": "={\n  \"success\": true,\n  \"uploaded_files\": {{ $json.files.length }},\n  \"repository\": \"{{ $json.repository }}\",\n  \"commit_url\": \"{{ $json.html_url }}\"\n}"
                    },
                    "id": "response",
                    "name": "Response",
                    "type": "n8n-nodes-base.respondToWebhook",
                    "typeVersion": 1,
                    "position": [900, 300]
                }
            ],
            "connections": {
                "Webhook Trigger": {
                    "main": [
                        [
                            {
                                "node": "Extract Data",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Extract Data": {
                    "main": [
                        [
                            {
                                "node": "GitHub Upload",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "GitHub Upload": {
                    "main": [
                        [
                            {
                                "node": "Response",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            },
            "active": True,
            "settings": {},
            "id": "github-upload-workflow"
        }
        
        return json.dumps(workflow_config, indent=2)
    
    async def create_file_processing_workflow(self) -> str:
        """
        创建文件处理工作流配置
        
        Returns:
            工作流配置JSON
        """
        workflow_config = {
            "name": "Ewandata File Processing",
            "nodes": [
                {
                    "parameters": {
                        "httpMethod": "POST",
                        "path": "file-processed",
                        "responseMode": "responseNode"
                    },
                    "id": "webhook-trigger",
                    "name": "File Processed Trigger",
                    "type": "n8n-nodes-base.webhook",
                    "typeVersion": 1,
                    "position": [240, 300]
                },
                {
                    "parameters": {
                        "conditions": {
                            "string": [
                                {
                                    "value1": "={{ $json.ai_analysis.result.classification.importance }}",
                                    "operation": "larger",
                                    "value2": "7"
                                }
                            ]
                        }
                    },
                    "id": "check-importance",
                    "name": "Check Importance",
                    "type": "n8n-nodes-base.if",
                    "typeVersion": 1,
                    "position": [460, 300]
                },
                {
                    "parameters": {
                        "functionCode": "// 生成Markdown文件内容\nconst fileInfo = $input.first().json.file_info;\nconst aiAnalysis = $input.first().json.ai_analysis.result;\n\nconst markdownContent = `# ${fileInfo.file_info.name} - AI分析报告\n\n## 文件信息\n- **文件名**: ${fileInfo.file_info.name}\n- **处理时间**: ${new Date().toISOString()}\n- **重要性**: ${aiAnalysis.classification.importance}/10\n\n## AI分析结果\n\n### 关键词\n${aiAnalysis.keywords.join(', ')}\n\n### 摘要\n${aiAnalysis.summary}\n\n### 分类\n- **类别**: ${aiAnalysis.classification.category}\n- **主题**: ${aiAnalysis.classification.topics.join(', ')}\n\n---\n*由Ewandata混合AI系统自动生成*\n`;\n\nreturn {\n  filename: `${fileInfo.file_info.stem}_analysis.md`,\n  content: markdownContent,\n  file_info: fileInfo,\n  ai_analysis: aiAnalysis\n};"
                    },
                    "id": "generate-markdown",
                    "name": "Generate Markdown",
                    "type": "n8n-nodes-base.function",
                    "typeVersion": 1,
                    "position": [680, 200]
                },
                {
                    "parameters": {
                        "authentication": "headerAuth",
                        "requestMethod": "PUT",
                        "url": "=https://api.github.com/repos/{{ $json.github_config.repository }}/contents/knowledge_base/{{ $json.filename }}",
                        "jsonParameters": True,
                        "bodyParametersJson": "={\n  \"message\": \"Add AI analysis: {{ $json.filename }}\",\n  \"content\": \"{{ Buffer.from($json.content).toString('base64') }}\",\n  \"branch\": \"{{ $json.github_config.branch || 'main' }}\"\n}"
                    },
                    "id": "upload-to-github",
                    "name": "Upload to GitHub",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 3,
                    "position": [900, 200]
                },
                {
                    "parameters": {
                        "respondWith": "json",
                        "responseBody": "={\n  \"success\": true,\n  \"message\": \"File processed and uploaded\",\n  \"github_url\": \"{{ $json.html_url }}\"\n}"
                    },
                    "id": "success-response",
                    "name": "Success Response",
                    "type": "n8n-nodes-base.respondToWebhook",
                    "typeVersion": 1,
                    "position": [1120, 200]
                },
                {
                    "parameters": {
                        "respondWith": "json",
                        "responseBody": "={\n  \"success\": true,\n  \"message\": \"File processed but not uploaded (low importance)\"\n}"
                    },
                    "id": "skip-response",
                    "name": "Skip Response",
                    "type": "n8n-nodes-base.respondToWebhook",
                    "typeVersion": 1,
                    "position": [680, 400]
                }
            ],
            "connections": {
                "File Processed Trigger": {
                    "main": [
                        [
                            {
                                "node": "Check Importance",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Check Importance": {
                    "main": [
                        [
                            {
                                "node": "Generate Markdown",
                                "type": "main",
                                "index": 0
                            }
                        ],
                        [
                            {
                                "node": "Skip Response",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Generate Markdown": {
                    "main": [
                        [
                            {
                                "node": "Upload to GitHub",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Upload to GitHub": {
                    "main": [
                        [
                            {
                                "node": "Success Response",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            },
            "active": True,
            "settings": {},
            "id": "file-processing-workflow"
        }
        
        return json.dumps(workflow_config, indent=2)
    
    async def test_connection(self) -> bool:
        """测试n8n连接"""
        try:
            test_url = f"{self.base_url}/healthz"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(test_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        logger.info("n8n连接测试成功")
                        return True
                    else:
                        logger.warning(f"n8n连接测试失败: HTTP {response.status}")
                        return False
                        
        except Exception as e:
            logger.warning(f"n8n连接测试失败: {e}")
            return False


async def main():
    """测试函数"""
    config = {
        'base_url': 'http://localhost:5678',
        'api_key': '',
        'github': {
            'repository': 'EwanCosmos/Ewandata',
            'branch': 'main',
            'token': 'your_github_token'
        },
        'webhooks': {
            'file_processed': '/webhook/file-processed',
            'github_upload': '/webhook/github-upload',
            'knowledge_update': '/webhook/knowledge-update'
        }
    }
    
    manager = N8nWorkflowManager(config)
    
    # 测试连接
    connected = await manager.test_connection()
    print(f"n8n连接状态: {connected}")
    
    # 生成工作流配置
    file_workflow = await manager.create_file_processing_workflow()
    print("文件处理工作流配置已生成")
    
    github_workflow = await manager.create_github_upload_workflow()
    print("GitHub上传工作流配置已生成")


if __name__ == "__main__":
    asyncio.run(main())
