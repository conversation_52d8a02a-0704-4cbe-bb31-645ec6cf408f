# Ewandata混合AI系统知识库目录

> 生成时间: 2025-07-05 00:30:44
> 文档总数: 23
> 分类数量: 4
> 主题数量: 4

## 📊 知识库统计概览

### 文档分布
- **总文档数**: 23
- **分类数量**: 4
- **主题数量**: 4
- **高重要性文档**: 11

### 重要性分布
- **低重要性 (1-4分)**: 11 个文档
- **极高重要性 (9-10分)**: 9 个文档
- **高重要性 (7-8分)**: 2 个文档
- **中等重要性 (5-6分)**: 1 个文档

## 📂 按分类组织

### 其他 (8 个文档)

#### 📄 以太坊信息.docx
- **重要性**: 10/10
- **主题**: 人工智能
- **关键词**: 100, eth, gas, base, limit...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\以太坊信息.docx`
- **处理结果**: `data/processed/以太坊信息_improved_processed.json`

#### 📄 29条工程提示词简介及案例.docx
- **重要性**: 1/10
- **主题**: 无
- **关键词**: file, word处理错误, 29条工程提示词简介及案例, docx, not...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\桌面文件包\29条工程提示词简介及案例.docx`
- **处理结果**: `data/processed/29条工程提示词简介及案例_processed.json`

#### 📄 ai自动化记忆.docx
- **重要性**: 1/10
- **主题**: 人工智能
- **关键词**: file, word处理错误, ai自动化记忆, docx, not...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\ai自动化记忆.docx`
- **处理结果**: `data/processed/ai自动化记忆_processed.json`

#### 📄 ima网址信息.txt
- **重要性**: 1/10
- **主题**: 无
- **关键词**: https, ima, com
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\ima网址信息.txt`
- **处理结果**: `data/processed/ima网址信息_processed.json`

#### 📄 logo设计.txt
- **重要性**: 1/10
- **主题**: 人工智能
- **关键词**: logo自动设计ai, 写字做logo, 写需求描述做logo
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\logo设计.txt`
- **处理结果**: `data/processed/logo设计_processed.json`

#### 📄 区块链大模型构想.docx
- **重要性**: 1/10
- **主题**: 无
- **关键词**: file, word处理错误, 区块链大模型构想, docx, not...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\区块链大模型构想.docx`
- **处理结果**: `data/processed/区块链大模型构想_processed.json`

#### 📄 博客.txt
- **重要性**: 1/10
- **主题**: 无
- **关键词**: 豆包可以生成博客了
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\博客.txt`
- **处理结果**: `data/processed/博客_processed.json`

#### 📄 进销存记忆.docx
- **重要性**: 1/10
- **主题**: 无
- **关键词**: file, word处理错误, 进销存记忆, docx, not...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\进销存记忆.docx`
- **处理结果**: `data/processed/进销存记忆_processed.json`

### 技术文档 (8 个文档)

#### 📄 blogger.txt
- **重要性**: 10/10
- **主题**: 编程, 人工智能
- **关键词**: whisper, import, model, transcript, video...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\blogger.txt`
- **处理结果**: `data/processed/blogger_processed.json`

#### 📄 brain记忆文档.docx
- **重要性**: 10/10
- **主题**: 编程, 人工智能
- **关键词**: three, react, drei, fiber, node_modules...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\brain记忆文档.docx`
- **处理结果**: `data/processed/brain记忆文档_improved_processed.json`

#### 📄 Ewandata项目需求英文描述.txt
- **重要性**: 10/10
- **主题**: 编程, 人工智能
- **关键词**: ewandata, 4070, phi, 主题分类, http...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\Ewandata项目需求英文描述.txt`
- **处理结果**: `data/processed/Ewandata项目需求英文描述_processed.json`

#### 📄 qwen cli.txt
- **重要性**: 10/10
- **主题**: 编程, 人工智能
- **关键词**: llama3, def, file, print, python...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\qwen cli.txt`
- **处理结果**: `data/processed/qwen cli_processed.json`

#### 📄 个人简介.docx
- **重要性**: 10/10
- **主题**: 编程, 人工智能
- **关键词**: 2021, 个人简介, 2023, ewan, cosmos...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\个人简介.docx`
- **处理结果**: `data/processed/个人简介_improved_processed.json`

#### 📄 代码示例.py
- **重要性**: 10/10
- **主题**: 编程, 人工智能
- **关键词**: self, request, task_type, await, return...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\代码示例.py`
- **处理结果**: `data/processed/代码示例_processed.json`

#### 📄 项目笔记.md
- **重要性**: 9/10
- **主题**: 编程, 人工智能
- **关键词**: bitsandbytesconfig, true, 30s, 项目开发笔记, 今日工作总结...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\项目笔记.md`
- **处理结果**: `data/processed/项目笔记_processed.json`

#### 📄 data流程cursor生成.txt
- **重要性**: 4/10
- **主题**: 编程
- **关键词**: 系统架构设计, 部署方式, python, 轻量级容器化, 我建议使用python作为主要开发语言...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\data流程cursor生成.txt`
- **处理结果**: `data/processed/data流程cursor生成_processed.json`

### 项目管理 (6 个文档)

#### 📄 短视频ai新闻prompt完美版.docx
- **重要性**: 10/10
- **主题**: 管理, 人工智能
- **关键词**: 星际之门, 心理学应用, 人性的弱点, business, insider...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\短视频ai新闻prompt完美版.docx`
- **处理结果**: `data/processed/短视频ai新闻prompt完美版_improved_processed.json`

#### 📄 厨房agent.txt
- **重要性**: 7/10
- **主题**: 管理
- **关键词**: 我想到一些问题需要补充提醒一下你, 既然是智能体, 就一定要区别于传统的视频教学app, 一定要和用户之间有客户舒服的较为密切的交互, 比如先问他今天想吃啥...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\厨房agent.txt`
- **处理结果**: `data/processed/厨房agent_processed.json`

#### 📄 厨房agent有啥吃啥计划.docx
- **重要性**: 7/10
- **主题**: 管理
- **关键词**: word文档, 厨房agent有啥吃啥计划, docx, 我想到一些问题需要补充提醒一下你, 既然是智能体...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\厨房agent有啥吃啥计划.docx`
- **处理结果**: `data/processed/厨房agent有啥吃啥计划_improved_processed.json`

#### 📄 测试文档1.txt
- **重要性**: 6/10
- **主题**: 管理, 人工智能
- **关键词**: ewandata混合ai系统测试文档, 这是一个用于测试文件监控和ai处理能力的示例文档, 系统概述, ewandata是一个创新的混合ai架构智能知识管理系统, 专为rtx...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\测试文档1.txt`
- **处理结果**: `data/processed/测试文档1_processed.json`

#### 📄 你猜我做.txt
- **重要性**: 4/10
- **主题**: 管理
- **关键词**: 我现在正在阿里百炼创建一个自己的应用智能体, 这次的创建目标如下, 主题有啥吃啥, 不知道今天吃点啥, 你就拿手机输入文字或拍图片把家里有的食材通通上传...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\你猜我做.txt`
- **处理结果**: `data/processed/你猜我做_processed.json`

#### 📄 项目结构树.docx
- **重要性**: 1/10
- **主题**: 管理
- **关键词**: file, word处理错误, 项目结构树, docx, not...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\提示词\项目结构树.docx`
- **处理结果**: `data/processed/项目结构树_processed.json`

### 学习笔记 (1 个文档)

#### 📄 type Engligh改成人工智能学习工具.txt
- **重要性**: 2/10
- **主题**: 学习, 人工智能
- **关键词**: 将英语学习工具esay, word变化成人工智能知识的学习工具, 学习模式不变, 只是更好内容, 当然还能拿来学习编程基础...
- **文件路径**: `C:\Users\<USER>\Desktop\临时记\type Engligh改成人工智能学习工具.txt`
- **处理结果**: `data/processed/type Engligh改成人工智能学习工具_processed.json`


## 🏷️ 按主题组织

### 人工智能 (13 个文档)

- **ai自动化记忆.docx** (重要性: 1/10, 分类: 其他)
- **blogger.txt** (重要性: 10/10, 分类: 技术文档)
- **brain记忆文档.docx** (重要性: 10/10, 分类: 技术文档)
- **Ewandata项目需求英文描述.txt** (重要性: 10/10, 分类: 技术文档)
- **logo设计.txt** (重要性: 1/10, 分类: 其他)
- **qwen cli.txt** (重要性: 10/10, 分类: 技术文档)
- **type Engligh改成人工智能学习工具.txt** (重要性: 2/10, 分类: 学习笔记)
- **个人简介.docx** (重要性: 10/10, 分类: 技术文档)
- **代码示例.py** (重要性: 10/10, 分类: 技术文档)
- **以太坊信息.docx** (重要性: 10/10, 分类: 其他)
- **测试文档1.txt** (重要性: 6/10, 分类: 项目管理)
- **短视频ai新闻prompt完美版.docx** (重要性: 10/10, 分类: 项目管理)
- **项目笔记.md** (重要性: 9/10, 分类: 技术文档)
### 编程 (8 个文档)

- **blogger.txt** (重要性: 10/10, 分类: 技术文档)
- **brain记忆文档.docx** (重要性: 10/10, 分类: 技术文档)
- **data流程cursor生成.txt** (重要性: 4/10, 分类: 技术文档)
- **Ewandata项目需求英文描述.txt** (重要性: 10/10, 分类: 技术文档)
- **qwen cli.txt** (重要性: 10/10, 分类: 技术文档)
- **个人简介.docx** (重要性: 10/10, 分类: 技术文档)
- **代码示例.py** (重要性: 10/10, 分类: 技术文档)
- **项目笔记.md** (重要性: 9/10, 分类: 技术文档)
### 管理 (6 个文档)

- **你猜我做.txt** (重要性: 4/10, 分类: 项目管理)
- **厨房agent.txt** (重要性: 7/10, 分类: 项目管理)
- **厨房agent有啥吃啥计划.docx** (重要性: 7/10, 分类: 项目管理)
- **测试文档1.txt** (重要性: 6/10, 分类: 项目管理)
- **短视频ai新闻prompt完美版.docx** (重要性: 10/10, 分类: 项目管理)
- **项目结构树.docx** (重要性: 1/10, 分类: 项目管理)
### 学习 (1 个文档)

- **type Engligh改成人工智能学习工具.txt** (重要性: 2/10, 分类: 学习笔记)

## ⭐ 高重要性文档详情

### 📄 blogger.txt
- **重要性**: 10/10
- **分类**: 技术文档
- **主题**: 编程, 人工智能
- **摘要**: 有几个类似Monica的AI工具可以将YouTube视频转换为博客内容：
Blogify. ai - 这是一个专门将YouTube视频转换成博客文章的AI工具
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\blogger.txt`

### 📄 brain记忆文档.docx
- **重要性**: 10/10
- **分类**: 技术文档
- **主题**: 编程, 人工智能
- **摘要**: [Word文档] brain记忆文档. docx

以下是您当前项目的关键信息梳理及执行流程概述，供新AI对话框快速了解已完成的服务进度、用户的使用习惯，以及继续完善项目的任务进度：
项目进展
项目名称: BrainLight
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\brain记忆文档.docx`

### 📄 Ewandata项目需求英文描述.txt
- **重要性**: 10/10
- **分类**: 技术文档
- **主题**: 编程, 人工智能
- **摘要**: 帮我创建一个本地知识库项目，名字叫Ewandata路径在E：Ewandata. 它的主要作用就是成为一个知道我所有数字信息的AI数字系统，也是我最好的私人助理和工作搭档
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\Ewandata项目需求英文描述.txt`

### 📄 qwen cli.txt
- **重要性**: 10/10
- **分类**: 技术文档
- **主题**: 编程, 人工智能
- **摘要**: ### 🌟 项目目标
我们希望构建一个类似 Gemini CLI 的工具，但使用你本地的 LLaMA3-8B 模型. 这个工具将能够：
1
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\qwen cli.txt`

### 📄 个人简介.docx
- **重要性**: 10/10
- **分类**: 技术文档
- **主题**: 编程, 人工智能
- **摘要**: [Word文档] 个人简介. docx

个人简介
姓名：王宇（Ewan Cosmos）
理念：AI时代已经来临，碳基生命无法与硅基生命竞争，关键在于如何学好、用好AI
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\个人简介.docx`

### 📄 代码示例.py
- **重要性**: 10/10
- **分类**: 技术文档
- **主题**: 编程, 人工智能
- **摘要**: """
混合AI系统核心代码示例
演示智能任务路由和模型管理
"""

import asyncio
import torch
from typing import Dict, Any

class HybridAIManager:
    """混合AI管理器"""
    
    def __init__(self):
        self. local_models = {}
    ...
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\代码示例.py`

### 📄 以太坊信息.docx
- **重要性**: 10/10
- **分类**: 其他
- **主题**: 人工智能
- **摘要**: [Word文档] 以太坊信息. docx

Available Accounts
==================
(0) ****************************************** (100 ETH)
(1) ****************************************** (100 ETH)
(2) 0x0d08Ec4F806c58F28bD0...
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\以太坊信息.docx`

### 📄 短视频ai新闻prompt完美版.docx
- **重要性**: 10/10
- **分类**: 项目管理
- **主题**: 管理, 人工智能
- **摘要**: [Word文档] 短视频ai新闻prompt完美版. docx

结构化Prompt：增强版
角色：
“你是一位精通人工智能动态的科技媒体创作者，熟悉全球AI竞争与技术趋势，同时深入研究卡耐基的《人性的弱点》、勒庞的《乌合之众》等心理学经典，擅长通过人性洞察提升内容的感染力和逻辑深度
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\1_1_桌面文件包\桌面文件包\短视频ai新闻prompt完美版.docx`

### 📄 项目笔记.md
- **重要性**: 9/10
- **分类**: 技术文档
- **主题**: 编程, 人工智能
- **摘要**: # 项目开发笔记

## 今日工作总结 (2025-01-03)

### 完成的任务
- [x] 混合AI架构设计和实现
- [x] RTX 4070 GPU优化配置
- [x] 智能任务路由机制开发
- [x] 成本优化器实现
- [x] 文件监控服务测试

### 技术要点

#### 模型量化优化
使用4-bit量化技术将7B参数模型压缩到4GB显存：
```python
from tra...
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\项目笔记.md`

### 📄 厨房agent.txt
- **重要性**: 7/10
- **分类**: 项目管理
- **主题**: 管理
- **摘要**: 我想到一些问题需要补充提醒一下你：1. 既然是智能体，就一定要区别于传统的视频教学app，一定要和用户之间有客户舒服的较为密切的交互，比如先问他今天想吃啥，冰箱里有啥，更想吃什么口味的食物，愿意去采购啥
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\厨房agent.txt`

### 📄 厨房agent有啥吃啥计划.docx
- **重要性**: 7/10
- **分类**: 项目管理
- **主题**: 管理
- **摘要**: [Word文档] 厨房agent有啥吃啥计划. docx

我想到一些问题需要补充提醒一下你：1
- **原始文件**: `C:\Users\<USER>\Desktop\临时记\厨房agent有啥吃啥计划.docx`


## 🔗 文档关联关系

### 关键词关联

- **docx**: 8 个文档
  - 29条工程提示词简介及案例.docx
  - ai自动化记忆.docx
  - brain记忆文档.docx
  - ... 还有 5 个文档
- **file**: 6 个文档
  - 29条工程提示词简介及案例.docx
  - ai自动化记忆.docx
  - qwen cli.txt
  - ... 还有 3 个文档
- **word处理错误**: 5 个文档
  - 29条工程提示词简介及案例.docx
  - ai自动化记忆.docx
  - 区块链大模型构想.docx
  - ... 还有 2 个文档
- **not**: 5 个文档
  - 29条工程提示词简介及案例.docx
  - ai自动化记忆.docx
  - 区块链大模型构想.docx
  - ... 还有 2 个文档
- **zip**: 5 个文档
  - 29条工程提示词简介及案例.docx
  - ai自动化记忆.docx
  - 区块链大模型构想.docx
  - ... 还有 2 个文档
- **import**: 3 个文档
  - blogger.txt
  - qwen cli.txt
  - 代码示例.py
- **word文档**: 3 个文档
  - brain记忆文档.docx
  - 以太坊信息.docx
  - 厨房agent有啥吃啥计划.docx
- **4070**: 3 个文档
  - Ewandata项目需求英文描述.txt
  - 测试文档1.txt
  - 项目笔记.md
- **python**: 2 个文档
  - data流程cursor生成.txt
  - qwen cli.txt
- **def**: 2 个文档
  - qwen cli.txt
  - 代码示例.py

### 主题关联

- **人工智能**: 13 个文档
  - ai自动化记忆.docx
  - blogger.txt
  - brain记忆文档.docx
  - ... 还有 10 个文档
- **编程**: 8 个文档
  - blogger.txt
  - brain记忆文档.docx
  - data流程cursor生成.txt
  - ... 还有 5 个文档
- **管理**: 6 个文档
  - 你猜我做.txt
  - 厨房agent.txt
  - 厨房agent有啥吃啥计划.docx
  - ... 还有 3 个文档

---
*由Ewandata混合AI系统自动生成于 2025-07-05 00:30:44*
