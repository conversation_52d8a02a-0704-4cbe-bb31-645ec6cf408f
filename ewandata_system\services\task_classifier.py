"""
任务分类器和复杂度评估器
智能分析任务类型和复杂度，为路由决策提供依据
"""

import re
import logging
from typing import Dict, List, Any, Tuple
import asyncio
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class TaskClassifier:
    """任务分类器"""
    
    def __init__(self):
        """初始化任务分类器"""
        self.task_patterns = {
            "document_analysis": [
                r"分析.*文档", r"总结.*内容", r"提取.*信息", r"关键词", r"摘要",
                r"analyze.*document", r"summarize", r"extract.*information"
            ],
            "code_analysis": [
                r"代码.*分析", r"程序.*解释", r"函数.*说明", r"bug.*修复", r"代码.*优化",
                r"code.*analysis", r"program.*explanation", r"function.*description",
                r"def\s+\w+", r"class\s+\w+", r"import\s+\w+", r"#include", r"function\s*\("
            ],
            "creative_generation": [
                r"创作", r"写.*故事", r"编写.*文章", r"设计.*方案", r"创意",
                r"creative", r"write.*story", r"compose", r"design.*plan", r"brainstorm"
            ],
            "data_extraction": [
                r"提取.*数据", r"解析.*表格", r"整理.*信息", r"数据.*清洗",
                r"extract.*data", r"parse.*table", r"organize.*information", r"data.*cleaning"
            ],
            "complex_reasoning": [
                r"推理", r"逻辑.*分析", r"因果.*关系", r"深度.*思考", r"哲学.*问题",
                r"reasoning", r"logical.*analysis", r"causal.*relationship", r"deep.*thinking"
            ],
            "quick_response": [
                r"简单.*问题", r"快速.*回答", r"是否", r"什么是", r"如何",
                r"simple.*question", r"quick.*answer", r"yes.*no", r"what.*is", r"how.*to"
            ],
            "translation": [
                r"翻译", r"translate", r"中文.*英文", r"英文.*中文", r"语言.*转换"
            ]
        }
        
        self.complexity_indicators = {
            "high": [
                r"深入.*分析", r"全面.*评估", r"复杂.*推理", r"多角度.*思考",
                r"comprehensive.*analysis", r"complex.*reasoning", r"multi.*perspective"
            ],
            "medium": [
                r"分析", r"解释", r"比较", r"评价",
                r"analysis", r"explanation", r"comparison", r"evaluation"
            ],
            "low": [
                r"简单", r"快速", r"直接", r"基本",
                r"simple", r"quick", r"direct", r"basic"
            ]
        }
        
        logger.info("任务分类器初始化完成")
    
    async def classify(self, request: Dict[str, Any]) -> str:
        """
        分类任务类型
        
        Args:
            request: 包含任务内容的请求
            
        Returns:
            任务类型
        """
        content = request.get('content', '')
        task_type = request.get('task_type', '')
        
        # 如果已指定任务类型，直接返回
        if task_type and task_type in self.task_patterns:
            return task_type
        
        # 基于内容分析任务类型
        content_lower = content.lower()
        
        # 计算每种任务类型的匹配分数
        scores = {}
        for task_type, patterns in self.task_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, content_lower))
                score += matches
            scores[task_type] = score
        
        # 返回得分最高的任务类型
        if scores:
            best_task_type = max(scores, key=scores.get)
            if scores[best_task_type] > 0:
                logger.info(f"任务分类: {best_task_type} (得分: {scores[best_task_type]})")
                return best_task_type
        
        # 默认返回文档分析
        logger.info("任务分类: document_analysis (默认)")
        return "document_analysis"


class ComplexityEvaluator:
    """复杂度评估器"""
    
    def __init__(self):
        """初始化复杂度评估器"""
        self.complexity_factors = {
            "content_length": {
                "weight": 0.2,
                "thresholds": [500, 2000, 5000]  # 字符数阈值
            },
            "technical_terms": {
                "weight": 0.3,
                "patterns": [
                    r"算法", r"数据结构", r"机器学习", r"深度学习", r"神经网络",
                    r"algorithm", r"data.*structure", r"machine.*learning", r"deep.*learning"
                ]
            },
            "reasoning_complexity": {
                "weight": 0.3,
                "patterns": [
                    r"因为.*所以", r"如果.*那么", r"推理", r"逻辑", r"因果",
                    r"because.*therefore", r"if.*then", r"reasoning", r"logic", r"causal"
                ]
            },
            "creative_requirements": {
                "weight": 0.2,
                "patterns": [
                    r"创新", r"创意", r"设计", r"想象", r"原创",
                    r"innovative", r"creative", r"design", r"imagine", r"original"
                ]
            }
        }
        
        logger.info("复杂度评估器初始化完成")
    
    async def evaluate(self, request: Dict[str, Any]) -> float:
        """
        评估任务复杂度
        
        Args:
            request: 包含任务内容的请求
            
        Returns:
            复杂度分数 (0.0-1.0)
        """
        content = request.get('content', '')
        content_lower = content.lower()
        
        total_score = 0.0
        
        # 1. 内容长度评估
        length_score = self._evaluate_content_length(content)
        total_score += length_score * self.complexity_factors["content_length"]["weight"]
        
        # 2. 技术术语密度评估
        tech_score = self._evaluate_technical_terms(content_lower)
        total_score += tech_score * self.complexity_factors["technical_terms"]["weight"]
        
        # 3. 推理复杂度评估
        reasoning_score = self._evaluate_reasoning_complexity(content_lower)
        total_score += reasoning_score * self.complexity_factors["reasoning_complexity"]["weight"]
        
        # 4. 创意要求评估
        creative_score = self._evaluate_creative_requirements(content_lower)
        total_score += creative_score * self.complexity_factors["creative_requirements"]["weight"]
        
        # 额外因素
        # 多语言混合增加复杂度
        if self._has_mixed_languages(content):
            total_score += 0.1
        
        # 代码块增加复杂度
        if self._has_code_blocks(content):
            total_score += 0.15
        
        # 数学公式增加复杂度
        if self._has_math_formulas(content):
            total_score += 0.1
        
        # 确保分数在0-1范围内
        complexity_score = min(1.0, max(0.0, total_score))
        
        logger.info(f"复杂度评估: {complexity_score:.3f}")
        return complexity_score
    
    def _evaluate_content_length(self, content: str) -> float:
        """评估内容长度复杂度"""
        length = len(content)
        thresholds = self.complexity_factors["content_length"]["thresholds"]
        
        if length < thresholds[0]:
            return 0.2  # 短文本
        elif length < thresholds[1]:
            return 0.5  # 中等长度
        elif length < thresholds[2]:
            return 0.8  # 长文本
        else:
            return 1.0  # 超长文本
    
    def _evaluate_technical_terms(self, content_lower: str) -> float:
        """评估技术术语密度"""
        patterns = self.complexity_factors["technical_terms"]["patterns"]
        
        total_matches = 0
        for pattern in patterns:
            matches = len(re.findall(pattern, content_lower))
            total_matches += matches
        
        # 基于匹配数量计算分数
        if total_matches == 0:
            return 0.1
        elif total_matches <= 2:
            return 0.4
        elif total_matches <= 5:
            return 0.7
        else:
            return 1.0
    
    def _evaluate_reasoning_complexity(self, content_lower: str) -> float:
        """评估推理复杂度"""
        patterns = self.complexity_factors["reasoning_complexity"]["patterns"]
        
        total_matches = 0
        for pattern in patterns:
            matches = len(re.findall(pattern, content_lower))
            total_matches += matches
        
        # 基于推理指标计算分数
        if total_matches == 0:
            return 0.2
        elif total_matches <= 2:
            return 0.5
        elif total_matches <= 4:
            return 0.8
        else:
            return 1.0
    
    def _evaluate_creative_requirements(self, content_lower: str) -> float:
        """评估创意要求"""
        patterns = self.complexity_factors["creative_requirements"]["patterns"]
        
        total_matches = 0
        for pattern in patterns:
            matches = len(re.findall(pattern, content_lower))
            total_matches += matches
        
        # 创意任务通常需要更高的复杂度
        if total_matches == 0:
            return 0.1
        elif total_matches <= 1:
            return 0.6
        elif total_matches <= 3:
            return 0.8
        else:
            return 1.0
    
    def _has_mixed_languages(self, content: str) -> bool:
        """检查是否包含多种语言"""
        # 简单检查中英文混合
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', content))
        has_english = bool(re.search(r'[a-zA-Z]', content))
        return has_chinese and has_english
    
    def _has_code_blocks(self, content: str) -> bool:
        """检查是否包含代码块"""
        code_indicators = [
            r'```', r'def\s+\w+', r'class\s+\w+', r'import\s+\w+',
            r'function\s*\(', r'#include', r'<script', r'SELECT\s+.*FROM'
        ]
        
        for pattern in code_indicators:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False
    
    def _has_math_formulas(self, content: str) -> bool:
        """检查是否包含数学公式"""
        math_indicators = [
            r'\$.*\$', r'\\frac', r'\\sum', r'\\int', r'\\alpha', r'\\beta',
            r'∑', r'∫', r'α', r'β', r'γ', r'δ', r'π', r'∞'
        ]
        
        for pattern in math_indicators:
            if re.search(pattern, content):
                return True
        return False


class RouteDecider:
    """路由决策器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化路由决策器
        
        Args:
            config: 路由配置
        """
        self.config = config
        self.task_type_mapping = config.get('task_types', {})
        self.fallback_strategy = config.get('fallback_strategy', 'external')
        
        logger.info("路由决策器初始化完成")
    
    async def decide(self, task_type: str, complexity_score: float, 
                    request: Dict[str, Any]) -> Dict[str, Any]:
        """
        做出路由决策
        
        Args:
            task_type: 任务类型
            complexity_score: 复杂度分数
            request: 原始请求
            
        Returns:
            路由决策结果
        """
        # 基于任务类型的初始路由
        initial_route = self.task_type_mapping.get(task_type, self.fallback_strategy)
        
        # 基于复杂度调整路由
        if complexity_score > 0.8:
            # 高复杂度任务优先使用外部AI
            if initial_route in ['primary', 'fast', 'code']:
                route_decision = {
                    'target': 'external',
                    'provider': 'browser_automation',
                    'reason': f'高复杂度任务 (score: {complexity_score:.3f})'
                }
            else:
                route_decision = {
                    'target': 'external',
                    'provider': initial_route,
                    'reason': f'高复杂度任务 (score: {complexity_score:.3f})'
                }
        elif complexity_score < 0.3 and task_type == 'quick_response':
            # 低复杂度快速响应任务使用最快模型
            route_decision = {
                'target': 'local',
                'model_type': 'fast',
                'reason': f'低复杂度快速任务 (score: {complexity_score:.3f})'
            }
        else:
            # 中等复杂度任务使用本地模型
            if initial_route in ['primary', 'fast', 'code']:
                route_decision = {
                    'target': 'local',
                    'model_type': initial_route,
                    'reason': f'中等复杂度本地处理 (score: {complexity_score:.3f})'
                }
            else:
                route_decision = {
                    'target': 'external',
                    'provider': initial_route,
                    'reason': f'外部AI处理 (score: {complexity_score:.3f})'
                }
        
        # 添加额外的决策信息
        route_decision.update({
            'task_type': task_type,
            'complexity_score': complexity_score,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"路由决策: {route_decision}")
        return route_decision


async def main():
    """测试函数"""
    # 测试任务分类器
    classifier = TaskClassifier()
    
    test_requests = [
        {"content": "请分析这个Python代码的功能：def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)"},
        {"content": "请帮我写一个关于人工智能的创意故事"},
        {"content": "什么是机器学习？"},
        {"content": "请深入分析量子计算的工作原理和未来发展趋势"}
    ]
    
    # 测试复杂度评估器
    evaluator = ComplexityEvaluator()
    
    # 测试路由决策器
    route_config = {
        "task_types": {
            "document_analysis": "primary",
            "code_analysis": "code",
            "quick_response": "fast",
            "creative_generation": "external"
        },
        "fallback_strategy": "external"
    }
    decider = RouteDecider(route_config)
    
    for i, request in enumerate(test_requests):
        print(f"\n测试 {i+1}: {request['content'][:50]}...")
        
        task_type = await classifier.classify(request)
        complexity = await evaluator.evaluate(request)
        decision = await decider.decide(task_type, complexity, request)
        
        print(f"  任务类型: {task_type}")
        print(f"  复杂度: {complexity:.3f}")
        print(f"  路由决策: {decision['target']} - {decision.get('model_type', decision.get('provider'))}")
        print(f"  原因: {decision['reason']}")


if __name__ == "__main__":
    asyncio.run(main())
