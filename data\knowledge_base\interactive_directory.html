<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ewandata知识库交互式目录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .stat-item {
            background: #3498db;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            margin: 5px;
            text-align: center;
            min-width: 120px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        
        .search-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .filters {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background: #3498db;
            color: white;
        }
        
        .directory {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .category-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s;
        }
        
        .category-header:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .category-title {
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .category-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        
        .expand-icon {
            transition: transform 0.3s;
            font-size: 1.2em;
        }
        
        .category-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background: #f8f9fa;
        }
        
        .category-content.expanded {
            max-height: 2000px;
        }
        
        .document-item {
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            transition: background-color 0.3s;
        }
        
        .document-item:hover {
            background-color: #e3f2fd;
        }
        
        .document-item:last-child {
            border-bottom: none;
        }
        
        .document-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .document-icon {
            font-size: 1.2em;
        }
        
        .document-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
            color: #666;
        }
        
        .importance-badge {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .importance-high {
            background: #e74c3c;
        }
        
        .importance-medium {
            background: #f39c12;
        }
        
        .importance-low {
            background: #95a5a6;
        }
        
        .topics {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }
        
        .topic-tag {
            background: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        
        .document-summary {
            color: #555;
            font-size: 0.95em;
            line-height: 1.4;
            margin-bottom: 10px;
        }
        
        .document-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .doc-link {
            color: #3498db;
            text-decoration: none;
            padding: 5px 10px;
            border: 1px solid #3498db;
            border-radius: 5px;
            font-size: 0.9em;
            transition: all 0.3s;
        }
        
        .doc-link:hover {
            background: #3498db;
            color: white;
        }
        
        .hidden {
            display: none !important;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats {
                flex-direction: column;
                align-items: center;
            }
            
            .filters {
                justify-content: center;
            }
            
            .document-meta {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Ewandata知识库</h1>
            <p class="subtitle">混合AI智能知识管理系统 - 交互式目录</p>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">23</span>
                    <span>总文档</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4</span>
                    <span>分类</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4</span>
                    <span>主题</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">11</span>
                    <span>高重要性</span>
                </div>
            </div>
            <p style="color: #7f8c8d; margin-top: 15px;">
                📅 生成时间: 2025-07-05 00:59:05
            </p>
        </div>
        
        <div class="search-container">
            <input type="text" class="search-box" id="searchBox" placeholder="🔍 搜索文档、关键词、主题...">
            <div class="filters">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="high-importance">高重要性</button>
                <button class="filter-btn" data-filter="category-其他">其他</button>
                <button class="filter-btn" data-filter="category-学习笔记">学习笔记</button>
                <button class="filter-btn" data-filter="category-技术文档">技术文档</button>
                <button class="filter-btn" data-filter="category-项目管理">项目管理</button>
                <button class="filter-btn" data-filter="topic-人工智能">人工智能</button>
                <button class="filter-btn" data-filter="topic-学习">学习</button>
                <button class="filter-btn" data-filter="topic-管理">管理</button>
                <button class="filter-btn" data-filter="topic-编程">编程</button>
            </div>
        </div>
        
        <div class="directory" id="directory">
            <div class="category-section" data-category="其他">
                <div class="category-header" onclick="toggleCategory('其他')">
                    <div class="category-title">📁 其他</div>
                    <div class="category-count">8 个文档</div>
                    <div class="expand-icon" id="icon-其他">▼</div>
                </div>
                <div class="category-content" id="content-其他">
                    <div class="document-item" 
                         data-category="其他" 
                         data-importance="10"
                         data-topics="人工智能"
                         data-keywords="以太坊信息.docx 人工智能 [word文档] 以太坊信息. docx

available accounts
==================
(0) ****************************************** (100 eth)
(1) ****************************************** (100 eth)
(2) 0x0d08ec4f806c58f28bd0">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            以太坊信息.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">10/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">[Word文档] 以太坊信息. docx

Available Accounts
==================
(0) ****************************************** (100 ETH)
(1) ****************************************** (100 ETH)
(2) 0x0d08Ec4F806c58F28bD0...</div>
                        <div class="document-links">
                            <a href="processed_documents/以太坊信息_improved_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/以太坊信息_improved_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="其他" 
                         data-importance="1"
                         data-topics=""
                         data-keywords="29条工程提示词简介及案例.docx  [word处理错误] 29条工程提示词简介及案例. docx: file is not a zip file">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            29条工程提示词简介及案例.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">1/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <div class="document-summary">[Word处理错误] 29条工程提示词简介及案例. docx: File is not a zip file...</div>
                        <div class="document-links">
                            <a href="processed_documents/29条工程提示词简介及案例_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/29条工程提示词简介及案例_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="其他" 
                         data-importance="1"
                         data-topics="人工智能"
                         data-keywords="ai自动化记忆.docx 人工智能 [word处理错误] ai自动化记忆. docx: file is not a zip file">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            ai自动化记忆.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">1/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">[Word处理错误] ai自动化记忆. docx: File is not a zip file...</div>
                        <div class="document-links">
                            <a href="processed_documents/ai自动化记忆_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/ai自动化记忆_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="其他" 
                         data-importance="1"
                         data-topics=""
                         data-keywords="ima网址信息.txt  https://ima. qq">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            ima网址信息.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">1/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <div class="document-summary">https://ima. qq...</div>
                        <div class="document-links">
                            <a href="processed_documents/ima网址信息_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/ima网址信息_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="其他" 
                         data-importance="1"
                         data-topics="人工智能"
                         data-keywords="logo设计.txt 人工智能 logo自动设计ai，写字做logo，写需求描述做logo">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            logo设计.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">1/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">logo自动设计ai，写字做logo，写需求描述做logo...</div>
                        <div class="document-links">
                            <a href="processed_documents/logo设计_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/logo设计_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="其他" 
                         data-importance="1"
                         data-topics=""
                         data-keywords="区块链大模型构想.docx  [word处理错误] 区块链大模型构想. docx: file is not a zip file">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            区块链大模型构想.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">1/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <div class="document-summary">[Word处理错误] 区块链大模型构想. docx: File is not a zip file...</div>
                        <div class="document-links">
                            <a href="processed_documents/区块链大模型构想_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/区块链大模型构想_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="其他" 
                         data-importance="1"
                         data-topics=""
                         data-keywords="博客.txt  豆包可以生成博客了">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            博客.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">1/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <div class="document-summary">豆包可以生成博客了...</div>
                        <div class="document-links">
                            <a href="processed_documents/博客_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/博客_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="其他" 
                         data-importance="1"
                         data-topics=""
                         data-keywords="进销存记忆.docx  [word处理错误] 进销存记忆. docx: file is not a zip file">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            进销存记忆.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">1/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <div class="document-summary">[Word处理错误] 进销存记忆. docx: File is not a zip file...</div>
                        <div class="document-links">
                            <a href="processed_documents/进销存记忆_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/进销存记忆_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="category-section" data-category="技术文档">
                <div class="category-header" onclick="toggleCategory('技术文档')">
                    <div class="category-title">📁 技术文档</div>
                    <div class="category-count">8 个文档</div>
                    <div class="expand-icon" id="icon-技术文档">▼</div>
                </div>
                <div class="category-content" id="content-技术文档">
                    <div class="document-item" 
                         data-category="技术文档" 
                         data-importance="10"
                         data-topics="编程,人工智能"
                         data-keywords="blogger.txt 编程 人工智能 有几个类似monica的ai工具可以将youtube视频转换为博客内容：
blogify. ai - 这是一个专门将youtube视频转换成博客文章的ai工具">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            blogger.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">10/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>技术文档</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">编程</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">有几个类似Monica的AI工具可以将YouTube视频转换为博客内容：
Blogify. ai - 这是一个专门将YouTube视频转换成博客文章的AI工具...</div>
                        <div class="document-links">
                            <a href="processed_documents/blogger_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/blogger_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="技术文档" 
                         data-importance="10"
                         data-topics="编程,人工智能"
                         data-keywords="brain记忆文档.docx 编程 人工智能 [word文档] brain记忆文档. docx

以下是您当前项目的关键信息梳理及执行流程概述，供新ai对话框快速了解已完成的服务进度、用户的使用习惯，以及继续完善项目的任务进度：
项目进展
项目名称: brainlight">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            brain记忆文档.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">10/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>技术文档</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">编程</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">[Word文档] brain记忆文档. docx

以下是您当前项目的关键信息梳理及执行流程概述，供新AI对话框快速了解已完成的服务进度、用户的使用习惯，以及继续完善项目的任务进度：
项目进展
项目名称: BrainLight...</div>
                        <div class="document-links">
                            <a href="processed_documents/brain记忆文档_improved_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/brain记忆文档_improved_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="技术文档" 
                         data-importance="10"
                         data-topics="编程,人工智能"
                         data-keywords="ewandata项目需求英文描述.txt 编程 人工智能 帮我创建一个本地知识库项目，名字叫ewandata路径在e：ewandata. 它的主要作用就是成为一个知道我所有数字信息的ai数字系统，也是我最好的私人助理和工作搭档">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            Ewandata项目需求英文描述.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">10/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>技术文档</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">编程</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">帮我创建一个本地知识库项目，名字叫Ewandata路径在E：Ewandata. 它的主要作用就是成为一个知道我所有数字信息的AI数字系统，也是我最好的私人助理和工作搭档...</div>
                        <div class="document-links">
                            <a href="processed_documents/Ewandata项目需求英文描述_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/Ewandata项目需求英文描述_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="技术文档" 
                         data-importance="10"
                         data-topics="编程,人工智能"
                         data-keywords="qwen cli.txt 编程 人工智能 ### 🌟 项目目标
我们希望构建一个类似 gemini cli 的工具，但使用你本地的 llama3-8b 模型. 这个工具将能够：
1">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            qwen cli.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">10/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>技术文档</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">编程</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">### 🌟 项目目标
我们希望构建一个类似 Gemini CLI 的工具，但使用你本地的 LLaMA3-8B 模型. 这个工具将能够：
1...</div>
                        <div class="document-links">
                            <a href="processed_documents/qwen cli_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/qwen cli_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="技术文档" 
                         data-importance="10"
                         data-topics="编程,人工智能"
                         data-keywords="个人简介.docx 编程 人工智能 [word文档] 个人简介. docx

个人简介
姓名：王宇（ewan cosmos）
理念：ai时代已经来临，碳基生命无法与硅基生命竞争，关键在于如何学好、用好ai">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            个人简介.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">10/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>技术文档</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">编程</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">[Word文档] 个人简介. docx

个人简介
姓名：王宇（Ewan Cosmos）
理念：AI时代已经来临，碳基生命无法与硅基生命竞争，关键在于如何学好、用好AI...</div>
                        <div class="document-links">
                            <a href="processed_documents/个人简介_improved_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/个人简介_improved_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="技术文档" 
                         data-importance="10"
                         data-topics="编程,人工智能"
                         data-keywords="代码示例.py 编程 人工智能 """
混合ai系统核心代码示例
演示智能任务路由和模型管理
"""

import asyncio
import torch
from typing import dict, any

class hybridaimanager:
    """混合ai管理器"""
    
    def __init__(self):
        self. local_models = {}
    ">
                        <div class="document-title">
                            <span class="document-icon">💻</span>
                            代码示例.py
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">10/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>技术文档</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">编程</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">"""
混合AI系统核心代码示例
演示智能任务路由和模型管理
"""

import asyncio
import torch
from typing import Dict, Any

class HybridAIManager:
    """混合AI管理器"""
    
    def __init__(self):
        self. local_models = {}
    ...</div>
                        <div class="document-links">
                            <a href="processed_documents/代码示例_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/代码示例_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="技术文档" 
                         data-importance="9"
                         data-topics="编程,人工智能"
                         data-keywords="项目笔记.md 编程 人工智能 # 项目开发笔记

## 今日工作总结 (2025-01-03)

### 完成的任务
- [x] 混合ai架构设计和实现
- [x] rtx 4070 gpu优化配置
- [x] 智能任务路由机制开发
- [x] 成本优化器实现
- [x] 文件监控服务测试

### 技术要点

#### 模型量化优化
使用4-bit量化技术将7b参数模型压缩到4gb显存：
```python
from tra">
                        <div class="document-title">
                            <span class="document-icon">📝</span>
                            项目笔记.md
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">9/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>技术文档</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">编程</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary"># 项目开发笔记

## 今日工作总结 (2025-01-03)

### 完成的任务
- [x] 混合AI架构设计和实现
- [x] RTX 4070 GPU优化配置
- [x] 智能任务路由机制开发
- [x] 成本优化器实现
- [x] 文件监控服务测试

### 技术要点

#### 模型量化优化
使用4-bit量化技术将7B参数模型压缩到4GB显存：
```python
from tra...</div>
                        <div class="document-links">
                            <a href="processed_documents/项目笔记_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/项目笔记_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="技术文档" 
                         data-importance="4"
                         data-topics="编程"
                         data-keywords="data流程cursor生成.txt 编程 系统架构设计
1. 部署方式：python + 轻量级容器化
我建议使用python作为主要开发语言，结合docker进行部分服务的容器化：
主要应用使用python开发，便于快速迭代和调试
将llama模型服务和数据库等稳定组件容器化，确保环境一致性
2">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            data流程cursor生成.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">4/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>技术文档</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">编程</span>
                        </div>
                        <div class="document-summary">系统架构设计
1. 部署方式：Python + 轻量级容器化
我建议使用Python作为主要开发语言，结合Docker进行部分服务的容器化：
主要应用使用Python开发，便于快速迭代和调试
将Llama模型服务和数据库等稳定组件容器化，确保环境一致性
2...</div>
                        <div class="document-links">
                            <a href="processed_documents/data流程cursor生成_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/data流程cursor生成_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="category-section" data-category="项目管理">
                <div class="category-header" onclick="toggleCategory('项目管理')">
                    <div class="category-title">📁 项目管理</div>
                    <div class="category-count">6 个文档</div>
                    <div class="expand-icon" id="icon-项目管理">▼</div>
                </div>
                <div class="category-content" id="content-项目管理">
                    <div class="document-item" 
                         data-category="项目管理" 
                         data-importance="10"
                         data-topics="管理,人工智能"
                         data-keywords="短视频ai新闻prompt完美版.docx 管理 人工智能 [word文档] 短视频ai新闻prompt完美版. docx

结构化prompt：增强版
角色：
“你是一位精通人工智能动态的科技媒体创作者，熟悉全球ai竞争与技术趋势，同时深入研究卡耐基的《人性的弱点》、勒庞的《乌合之众》等心理学经典，擅长通过人性洞察提升内容的感染力和逻辑深度">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            短视频ai新闻prompt完美版.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-high">10/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>项目管理</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">管理</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">[Word文档] 短视频ai新闻prompt完美版. docx

结构化Prompt：增强版
角色：
“你是一位精通人工智能动态的科技媒体创作者，熟悉全球AI竞争与技术趋势，同时深入研究卡耐基的《人性的弱点》、勒庞的《乌合之众》等心理学经典，擅长通过人性洞察提升内容的感染力和逻辑深度...</div>
                        <div class="document-links">
                            <a href="processed_documents/短视频ai新闻prompt完美版_improved_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/短视频ai新闻prompt完美版_improved_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="项目管理" 
                         data-importance="7"
                         data-topics="管理"
                         data-keywords="厨房agent.txt 管理 我想到一些问题需要补充提醒一下你：1. 既然是智能体，就一定要区别于传统的视频教学app，一定要和用户之间有客户舒服的较为密切的交互，比如先问他今天想吃啥，冰箱里有啥，更想吃什么口味的食物，愿意去采购啥">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            厨房agent.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-medium">7/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>项目管理</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">管理</span>
                        </div>
                        <div class="document-summary">我想到一些问题需要补充提醒一下你：1. 既然是智能体，就一定要区别于传统的视频教学app，一定要和用户之间有客户舒服的较为密切的交互，比如先问他今天想吃啥，冰箱里有啥，更想吃什么口味的食物，愿意去采购啥...</div>
                        <div class="document-links">
                            <a href="processed_documents/厨房agent_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/厨房agent_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="项目管理" 
                         data-importance="7"
                         data-topics="管理"
                         data-keywords="厨房agent有啥吃啥计划.docx 管理 [word文档] 厨房agent有啥吃啥计划. docx

我想到一些问题需要补充提醒一下你：1">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            厨房agent有啥吃啥计划.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-medium">7/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>项目管理</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">管理</span>
                        </div>
                        <div class="document-summary">[Word文档] 厨房agent有啥吃啥计划. docx

我想到一些问题需要补充提醒一下你：1...</div>
                        <div class="document-links">
                            <a href="processed_documents/厨房agent有啥吃啥计划_improved_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/厨房agent有啥吃啥计划_improved_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="项目管理" 
                         data-importance="6"
                         data-topics="管理,人工智能"
                         data-keywords="测试文档1.txt 管理 人工智能 ewandata混合ai系统测试文档

这是一个用于测试文件监控和ai处理能力的示例文档. 系统概述：
ewandata是一个创新的混合ai架构智能知识管理系统，专为rtx 4070 gpu优化设计">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            测试文档1.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-medium">6/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>项目管理</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">管理</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">Ewandata混合AI系统测试文档

这是一个用于测试文件监控和AI处理能力的示例文档. 系统概述：
Ewandata是一个创新的混合AI架构智能知识管理系统，专为RTX 4070 GPU优化设计...</div>
                        <div class="document-links">
                            <a href="processed_documents/测试文档1_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/测试文档1_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="项目管理" 
                         data-importance="4"
                         data-topics="管理"
                         data-keywords="你猜我做.txt 管理 我现在正在阿里百炼创建一个自己的应用智能体，这次的创建目标如下：1. 主题有啥吃啥，不知道今天吃点啥，你就拿手机输入文字或拍图片把家里有的食材通通上传，我来把他们变成你今天美味的食物搭配，分为低质和高热量2种选择">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            你猜我做.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">4/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>项目管理</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">管理</span>
                        </div>
                        <div class="document-summary">我现在正在阿里百炼创建一个自己的应用智能体，这次的创建目标如下：1. 主题有啥吃啥，不知道今天吃点啥，你就拿手机输入文字或拍图片把家里有的食材通通上传，我来把他们变成你今天美味的食物搭配，分为低质和高热量2种选择...</div>
                        <div class="document-links">
                            <a href="processed_documents/你猜我做_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/你猜我做_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                    <div class="document-item" 
                         data-category="项目管理" 
                         data-importance="1"
                         data-topics="管理"
                         data-keywords="项目结构树.docx 管理 [word处理错误] 项目结构树. docx: file is not a zip file">
                        <div class="document-title">
                            <span class="document-icon">📄</span>
                            项目结构树.docx
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">1/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>项目管理</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">管理</span>
                        </div>
                        <div class="document-summary">[Word处理错误] 项目结构树. docx: File is not a zip file...</div>
                        <div class="document-links">
                            <a href="processed_documents/项目结构树_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/项目结构树_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="category-section" data-category="学习笔记">
                <div class="category-header" onclick="toggleCategory('学习笔记')">
                    <div class="category-title">📁 学习笔记</div>
                    <div class="category-count">1 个文档</div>
                    <div class="expand-icon" id="icon-学习笔记">▼</div>
                </div>
                <div class="category-content" id="content-学习笔记">
                    <div class="document-item" 
                         data-category="学习笔记" 
                         data-importance="2"
                         data-topics="学习,人工智能"
                         data-keywords="type engligh改成人工智能学习工具.txt 学习 人工智能 将英语学习工具esay word变化成人工智能知识的学习工具，学习模式不变，只是更好内容. 当然还能拿来学习编程基础，以及配合ewanknowleage的元认知学习法一起来使用">
                        <div class="document-title">
                            <span class="document-icon">📃</span>
                            type Engligh改成人工智能学习工具.txt
                        </div>
                        <div class="document-meta">
                            <div class="meta-item">
                                <span>⭐</span>
                                <span class="importance-badge importance-low">2/10</span>
                            </div>
                            <div class="meta-item">
                                <span>📂</span>
                                <span>学习笔记</span>
                            </div>
                        </div>
                        <div class="topics">
                            <span class="topic-tag">学习</span>
                            <span class="topic-tag">人工智能</span>
                        </div>
                        <div class="document-summary">将英语学习工具Esay word变化成人工智能知识的学习工具，学习模式不变，只是更好内容. 当然还能拿来学习编程基础，以及配合Ewanknowleage的元认知学习法一起来使用...</div>
                        <div class="document-links">
                            <a href="processed_documents/type Engligh改成人工智能学习工具_processed.json" class="doc-link" target="_blank">📊 JSON数据</a>
                            <a href="processed_documents/type Engligh改成人工智能学习工具_processed.md" class="doc-link" target="_blank">📝 报告</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 切换分类展开/折叠
        function toggleCategory(category) {
            const content = document.getElementById(`content-${category}`);
            const icon = document.getElementById(`icon-${category}`);
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.textContent = '▼';
            } else {
                content.classList.add('expanded');
                icon.textContent = '▲';
            }
        }
        
        // 搜索功能
        const searchBox = document.getElementById('searchBox');
        const documentItems = document.querySelectorAll('.document-item');
        
        searchBox.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            documentItems.forEach(item => {
                const keywords = item.getAttribute('data-keywords');
                if (keywords.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
            
            // 自动展开有匹配结果的分类
            document.querySelectorAll('.category-section').forEach(section => {
                const visibleItems = section.querySelectorAll('.document-item[style="display: block"], .document-item:not([style*="display: none"])');
                const content = section.querySelector('.category-content');
                const icon = section.querySelector('.expand-icon');
                
                if (visibleItems.length > 0 && searchTerm) {
                    content.classList.add('expanded');
                    icon.textContent = '▲';
                }
            });
        });
        
        // 过滤功能
        const filterBtns = document.querySelectorAll('.filter-btn');
        
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // 更新按钮状态
                filterBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                
                documentItems.forEach(item => {
                    let show = false;
                    
                    if (filter === 'all') {
                        show = true;
                    } else if (filter === 'high-importance') {
                        const importance = parseInt(item.getAttribute('data-importance'));
                        show = importance >= 7;
                    } else if (filter.startsWith('category-')) {
                        const category = filter.replace('category-', '');
                        show = item.getAttribute('data-category') === category;
                    } else if (filter.startsWith('topic-')) {
                        const topic = filter.replace('topic-', '');
                        const topics = item.getAttribute('data-topics');
                        show = topics.includes(topic);
                    }
                    
                    item.style.display = show ? 'block' : 'none';
                });
                
                // 自动展开有匹配结果的分类
                document.querySelectorAll('.category-section').forEach(section => {
                    const visibleItems = section.querySelectorAll('.document-item[style="display: block"], .document-item:not([style*="display: none"])');
                    const content = section.querySelector('.category-content');
                    const icon = section.querySelector('.expand-icon');
                    
                    if (visibleItems.length > 0) {
                        content.classList.add('expanded');
                        icon.textContent = '▲';
                    } else {
                        content.classList.remove('expanded');
                        icon.textContent = '▼';
                    }
                });
            });
        });
        
        // 默认展开第一个分类
        document.addEventListener('DOMContentLoaded', function() {
            const firstCategory = document.querySelector('.category-section');
            if (firstCategory) {
                const categoryName = firstCategory.getAttribute('data-category');
                toggleCategory(categoryName);
            }
        });
    </script>
</body>
</html>