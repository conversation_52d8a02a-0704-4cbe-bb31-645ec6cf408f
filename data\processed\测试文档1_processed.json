{"original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\测试文档1.txt", "processed_time": "2025-07-04T23:31:02.466001", "processing_time_seconds": 0.0011532306671142578, "file_info": {"name": "测试文档1.txt", "type": ".txt", "size_chars": 416, "size_bytes": 864}, "ai_analysis": {"keywords": [], "summary": "Ewandata混合AI系统测试文档\n\n这是一个用于测试文件监控和AI处理能力的示例文档. 系统概述：\nEwandata是一个创新的混合AI架构智能知识管理系统，专为RTX 4070 GPU优化设计", "classification": {"category": "其他", "category_confidence": 0.0, "topics": [], "importance": 4, "tags": [], "category_scores": {"技术文档": 0.0, "项目管理": 0.0, "学习笔记": 0.0, "商业文档": 0.0, "个人资料": 0.0, "其他": 0.0}}, "enhanced_data": {"keywords": ["ewandata混合ai系统测试文档", "这是一个用于测试文件监控和ai处理能力的示例文档", "系统概述", "ewandata是一个创新的混合ai架构智能知识管理系统", "专为rtx", "4070", "gpu优化设计", "核心特性", "本地多模型协同", "qwen2"], "summary": "Ewandata混合AI系统测试文档\n\n这是一个用于测试文件监控和AI处理能力的示例文档. 系统概述：\nEwandata是一个创新的混合AI架构智能知识管理系统，专为RTX 4070 GPU优化设计", "classification": {"category": "项目管理", "topics": ["管理", "人工智能"], "importance": 6, "tags": ["项目", "AI"]}, "ai_enhanced": true, "ai_type": "simple_rule_based"}, "reclassified": true, "reclassified_time": "2025-07-05T00:57:12.713091"}, "content_preview": "Ewandata混合AI系统测试文档\n\n这是一个用于测试文件监控和AI处理能力的示例文档。\n\n系统概述：\nEwandata是一个创新的混合AI架构智能知识管理系统，专为RTX 4070 GPU优化设计。\n\n核心特性：\n1. 本地多模型协同：Qwen2-7B + CodeLlama-7B + Phi-3-mini\n2. 智能任务路由：基于任务类型和复杂度的自动路由决策\n3. 成本优化：90%本地处理 + 智能外部AI调用，日成本 < $5\n4. 无缝集成：桌面文件监控 + 多渠道信息采集\n\n技术栈：\n- PyTorch 2.5.1+cu118：GPU加速深度学习框架\n- Transformers 4.36+：Hugging Face模型库\n- BitsAndBytes：模型量化和优化\n- Playwright：浏览器自动化框架\n\n测试目标：\n验证系统能够自动检测此文件，进行AI分析，提取关键词，生成摘要，并输出结构化数据。"}