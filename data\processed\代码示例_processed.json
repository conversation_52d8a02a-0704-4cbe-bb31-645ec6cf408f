{"original_file": "C:\\Users\\<USER>\\Desktop\\临时记\\代码示例.py", "processed_time": "2025-07-04T23:31:02.978442", "processing_time_seconds": 0.0014655590057373047, "file_info": {"name": "代码示例.py", "type": ".py", "size_chars": 1589, "size_bytes": 1876}, "ai_analysis": {"keywords": [], "summary": "\"\"\"\n混合AI系统核心代码示例\n演示智能任务路由和模型管理\n\"\"\"\n\nimport asyncio\nimport torch\nfrom typing import Dict, Any\n\nclass HybridAIManager:\n    \"\"\"混合AI管理器\"\"\"\n    \n    def __init__(self):\n        self. local_models = {}\n    ...", "classification": {"category": "技术文档", "category_confidence": 0.65, "topics": [], "importance": 5, "tags": ["文档", "技术"], "category_scores": {"技术文档": 6.5, "项目管理": 0.0, "学习笔记": 0.0, "商业文档": 0.0, "个人资料": 0.0, "其他": 0.0}}, "enhanced_data": {"keywords": ["self", "request", "task_type", "await", "return", "def", "local_models", "import", "dict", "any"], "summary": "\"\"\"\n混合AI系统核心代码示例\n演示智能任务路由和模型管理\n\"\"\"\n\nimport asyncio\nimport torch\nfrom typing import Dict, Any\n\nclass HybridAIManager:\n    \"\"\"混合AI管理器\"\"\"\n    \n    def __init__(self):\n        self. local_models = {}\n    ...", "classification": {"category": "技术文档", "topics": ["编程", "人工智能"], "importance": 10, "tags": ["技术", "AI"]}, "ai_enhanced": true, "ai_type": "simple_rule_based"}, "reclassified": true, "reclassified_time": "2025-07-05T00:57:12.626593"}, "content_preview": "\"\"\"\n混合AI系统核心代码示例\n演示智能任务路由和模型管理\n\"\"\"\n\nimport asyncio\nimport torch\nfrom typing import Dict, Any\n\nclass HybridAIManager:\n    \"\"\"混合AI管理器\"\"\"\n    \n    def __init__(self):\n        self.local_models = {}\n        self.external_ai = None\n        self.task_classifier = None\n        self.cost_optimizer = None\n        \n    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"\n        处理用户请求\n        \n        Args:\n            request: 用户请求内容\n            \n        Returns:\n     ..."}