"""
混合AI系统部署脚本
一键部署本地多模型 + 外部AI集成的完整解决方案
"""

import os
import sys
import subprocess
import json
import torch
from pathlib import Path
import asyncio

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

def print_step(step, description):
    print(f"\n[步骤 {step}] {description}")
    print("-" * 60)

def check_system_requirements():
    """检查系统要求"""
    print_step(1, "检查系统要求")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    # 检查GPU
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"✅ GPU: {gpu_name} ({gpu_memory:.1f} GB)")
        
        if gpu_memory < 10:
            print("⚠️ 建议使用12GB以上显存的GPU")
            print("   当前配置可能需要使用更多的模型量化")
        
        return True
    else:
        print("⚠️ 未检测到CUDA GPU，将使用CPU模式")
        print("   性能将显著降低，建议使用GPU")
        return True

def install_dependencies():
    """安装依赖库"""
    print_step(2, "安装混合AI系统依赖")
    
    # 核心AI依赖
    ai_dependencies = [
        "transformers>=4.36.0",
        "torch>=2.0.0", 
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",  # 量化支持
        "sentencepiece>=0.1.99",
        "protobuf>=3.20.0"
    ]
    
    # 浏览器自动化依赖
    automation_dependencies = [
        "playwright>=1.40.0",
        "selenium>=4.15.0",
        "beautifulsoup4>=4.12.0",
        "aiohttp>=3.9.0"
    ]
    
    # 工作流和API依赖
    workflow_dependencies = [
        "requests>=2.31.0",
        "schedule>=1.2.0",
        "psutil>=5.9.0",
        "numpy>=1.24.0"
    ]
    
    all_dependencies = ai_dependencies + automation_dependencies + workflow_dependencies
    
    print("安装依赖库...")
    for dep in all_dependencies:
        try:
            print(f"安装 {dep}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True, check=True)
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {dep} 安装失败: {e}")
            return False
    
    # 安装Playwright浏览器
    try:
        print("安装Playwright浏览器...")
        subprocess.run([
            sys.executable, "-m", "playwright", "install", "chromium"
        ], check=True)
        print("✅ Playwright浏览器安装成功")
    except subprocess.CalledProcessError as e:
        print(f"⚠️ Playwright浏览器安装失败: {e}")
    
    return True

def create_hybrid_ai_config():
    """创建混合AI配置"""
    print_step(3, "创建混合AI配置文件")
    
    # 检测GPU内存并调整配置
    gpu_memory = 12.0  # 默认RTX 4070
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    # 根据GPU内存调整模型配置
    if gpu_memory >= 12:
        quantization_primary = "4bit"
        quantization_secondary = "4bit"
    elif gpu_memory >= 8:
        quantization_primary = "4bit"
        quantization_secondary = "8bit"
    else:
        quantization_primary = "8bit"
        quantization_secondary = "8bit"
    
    hybrid_config = {
        "local_models": {
            "primary_model": {
                "name": "Qwen/Qwen2-7B-Instruct",
                "type": "general",
                "memory_resident": True,
                "quantization": quantization_primary
            },
            "secondary_models": [
                {
                    "name": "codellama/CodeLlama-7b-Instruct-hf",
                    "type": "code",
                    "memory_resident": False,
                    "quantization": quantization_secondary
                },
                {
                    "name": "microsoft/Phi-3-mini-4k-instruct",
                    "type": "fast",
                    "memory_resident": False,
                    "quantization": "8bit"
                }
            ]
        },
        "external_ai": {
            "enabled": True,
            "complexity_threshold": 0.8,
            "cost_threshold": 5.0,
            "providers": [
                {
                    "name": "browser_automation",
                    "type": "free",
                    "priority": 1,
                    "daily_limit": 50
                },
                {
                    "name": "free_api_pool",
                    "type": "api",
                    "priority": 2,
                    "daily_limit": 100,
                    "deepseek_key": "",
                    "groq_key": ""
                },
                {
                    "name": "n8n_workflow",
                    "type": "workflow",
                    "priority": 3,
                    "daily_limit": 200,
                    "base_url": "http://localhost:5678",
                    "api_key": ""
                }
            ]
        },
        "routing": {
            "task_types": {
                "document_analysis": "primary",
                "code_analysis": "code", 
                "quick_response": "fast",
                "creative_generation": "external",
                "complex_reasoning": "external",
                "translation": "primary"
            },
            "fallback_strategy": "external"
        },
        "optimization": {
            "memory_optimization": True,
            "model_switching_delay": 2.0,
            "cache_responses": True,
            "batch_processing": True
        }
    }
    
    # 保存配置
    config_dir = Path("ewandata_system/config")
    config_dir.mkdir(parents=True, exist_ok=True)
    
    config_file = config_dir / "hybrid_ai_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(hybrid_config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 混合AI配置已创建: {config_file}")
    print(f"   GPU内存: {gpu_memory:.1f}GB")
    print(f"   主模型量化: {quantization_primary}")
    print(f"   次要模型量化: {quantization_secondary}")
    
    return True

def download_models():
    """下载AI模型"""
    print_step(4, "下载AI模型")
    
    models_to_download = [
        "Qwen/Qwen2-7B-Instruct",
        "microsoft/Phi-3-mini-4k-instruct"
    ]
    
    # CodeLlama较大，可选下载
    download_codelama = input("是否下载CodeLlama-7B用于代码分析？(y/N): ").lower().strip() == 'y'
    if download_codelama:
        models_to_download.append("codellama/CodeLlama-7b-Instruct-hf")
    
    for model_name in models_to_download:
        try:
            print(f"下载模型: {model_name}")
            print("⚠️ 首次下载可能需要20-40分钟，请耐心等待...")
            
            from transformers import AutoTokenizer, AutoModelForCausalLM
            
            # 下载tokenizer
            tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True
            )
            print(f"✅ {model_name} Tokenizer下载完成")
            
            # 下载模型（不加载到内存）
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                trust_remote_code=True,
                device_map=None
            )
            print(f"✅ {model_name} 模型下载完成")
            
            # 清理内存
            del model, tokenizer
            
        except Exception as e:
            print(f"❌ {model_name} 下载失败: {e}")
            return False
    
    return True

def test_hybrid_ai_system():
    """测试混合AI系统"""
    print_step(5, "测试混合AI系统")
    
    try:
        # 添加系统路径
        sys.path.append('ewandata_system')
        
        # 测试导入
        print("测试组件导入...")
        from services.hybrid_ai_manager import get_hybrid_ai_manager
        from services.multi_model_manager import MultiModelManager
        from services.task_classifier import TaskClassifier, ComplexityEvaluator
        from services.external_ai_integrator import ExternalAIIntegrator
        from services.cost_optimizer import CostOptimizer
        
        print("✅ 所有组件导入成功")
        
        # 创建测试函数
        async def run_test():
            print("初始化混合AI管理器...")
            manager = get_hybrid_ai_manager()
            
            # 测试请求
            test_requests = [
                {
                    "content": "请解释什么是人工智能",
                    "task_type": "document_analysis",
                    "max_tokens": 200
                },
                {
                    "content": "def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
                    "task_type": "code_analysis",
                    "max_tokens": 300
                },
                {
                    "content": "1+1等于几？",
                    "task_type": "quick_response",
                    "max_tokens": 50
                }
            ]
            
            print("执行测试请求...")
            for i, request in enumerate(test_requests, 1):
                print(f"\n测试 {i}: {request['content'][:30]}...")
                
                try:
                    result = await manager.process_request(request)
                    
                    if result['success']:
                        print(f"✅ 测试 {i} 成功")
                        print(f"   处理方式: {result['metadata']['processed_by']}")
                        print(f"   响应时间: {result['metadata']['response_time']:.2f}秒")
                        print(f"   内容长度: {len(result['result']['content'])}字符")
                    else:
                        print(f"❌ 测试 {i} 失败: {result['error']}")
                        
                except Exception as e:
                    print(f"❌ 测试 {i} 异常: {e}")
            
            # 获取系统状态
            print("\n获取系统状态...")
            status = await manager.get_system_status()
            
            print("✅ 系统状态:")
            print(f"   GPU内存使用: {status['gpu_status'].get('memory_allocated', 0):.1f}GB")
            print(f"   已加载模型: {status['model_status'].get('loaded_models', 0)}")
            print(f"   总请求数: {manager.performance_stats['total_requests']}")
            print(f"   本地处理: {manager.performance_stats['local_processed']}")
            print(f"   外部处理: {manager.performance_stats['external_processed']}")
            
            await manager.shutdown()
        
        # 运行异步测试
        asyncio.run(run_test())
        
        return True
        
    except Exception as e:
        print(f"❌ 系统测试失败: {e}")
        return False

def create_startup_scripts():
    """创建启动脚本"""
    print_step(6, "创建启动脚本")
    
    # 混合AI系统启动脚本
    startup_script = """@echo off
echo 启动Ewandata混合AI系统...
cd /d "%~dp0"

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo Python未安装或不在PATH中
    pause
    exit /b 1
)

echo 启动混合AI系统...
python -c "
import asyncio
import sys
sys.path.append('ewandata_system')

async def main():
    from services.hybrid_ai_manager import get_hybrid_ai_manager
    
    print('🚀 Ewandata混合AI系统启动')
    print('支持本地多模型 + 外部AI智能路由')
    print('按 Ctrl+C 停止系统')
    
    manager = get_hybrid_ai_manager()
    
    try:
        # 显示系统状态
        status = await manager.get_system_status()
        print(f'GPU: {status[\"gpu_status\"].get(\"device_name\", \"CPU模式\")}')
        print(f'可用模型: {len(status[\"model_status\"].get(\"models\", {}))}')
        
        # 启动交互式会话
        print('\\n输入问题开始对话 (输入 \"quit\" 退出):')
        
        while True:
            user_input = input('\\n用户: ').strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                break
            
            if not user_input:
                continue
            
            request = {
                'content': user_input,
                'max_tokens': 500
            }
            
            print('AI: 正在思考...')
            result = await manager.process_request(request)
            
            if result['success']:
                print(f'AI: {result[\"result\"][\"content\"]}')
                print(f'(处理方式: {result[\"metadata\"][\"processed_by\"]}, '
                      f'耗时: {result[\"metadata\"][\"response_time\"]:.1f}秒)')
            else:
                print(f'AI: 抱歉，处理失败: {result[\"error\"]}')
    
    except KeyboardInterrupt:
        print('\\n系统停止')
    finally:
        await manager.shutdown()

if __name__ == '__main__':
    asyncio.run(main())
"

pause
"""
    
    # 批量测试脚本
    batch_test_script = """@echo off
echo 混合AI系统批量测试...
cd /d "%~dp0"

python -c "
import asyncio
import sys
sys.path.append('ewandata_system')

async def main():
    from services.hybrid_ai_manager import get_hybrid_ai_manager
    
    manager = get_hybrid_ai_manager()
    
    test_cases = [
        {'content': '请分析人工智能的发展趋势', 'type': 'analysis'},
        {'content': 'def quicksort(arr): pass  # 请完成这个快速排序函数', 'type': 'code'},
        {'content': '今天天气怎么样？', 'type': 'quick'},
        {'content': '请写一个关于未来科技的创意故事', 'type': 'creative'},
        {'content': '解释量子计算的工作原理和应用前景', 'type': 'complex'}
    ]
    
    print(f'开始批量测试 {len(test_cases)} 个案例...')
    
    results = []
    for i, test in enumerate(test_cases, 1):
        print(f'\\n[{i}/{len(test_cases)}] 测试: {test[\"content\"][:30]}...')
        
        request = {'content': test['content'], 'max_tokens': 300}
        result = await manager.process_request(request)
        
        if result['success']:
            print(f'✅ 成功 - {result[\"metadata\"][\"processed_by\"]} ({result[\"metadata\"][\"response_time\"]:.1f}s)')
            results.append(True)
        else:
            print(f'❌ 失败 - {result[\"error\"]}')
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f'\\n📊 测试完成: {sum(results)}/{len(results)} 成功 ({success_rate:.1f}%)')
    
    # 显示系统状态
    status = await manager.get_system_status()
    print(f'性能统计: {manager.performance_stats}')
    
    await manager.shutdown()

asyncio.run(main())
"

pause
"""
    
    # 保存脚本
    with open("start_hybrid_ai.bat", 'w', encoding='utf-8') as f:
        f.write(startup_script)
    print("✅ 启动脚本已创建: start_hybrid_ai.bat")
    
    with open("test_hybrid_ai.bat", 'w', encoding='utf-8') as f:
        f.write(batch_test_script)
    print("✅ 测试脚本已创建: test_hybrid_ai.bat")
    
    return True

def generate_deployment_report():
    """生成部署报告"""
    print_step(7, "生成部署报告")
    
    # 检测系统配置
    gpu_info = "CPU模式"
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_info = f"{gpu_name} ({gpu_memory:.1f}GB)"
    
    deployment_report = {
        "deployment_info": {
            "timestamp": "2025-01-03T10:00:00",
            "system_type": "混合AI架构",
            "gpu_config": gpu_info,
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        },
        "architecture_overview": {
            "local_models": [
                "Qwen2-7B-Instruct (主模型，中文优化)",
                "CodeLlama-7B (代码分析)",
                "Phi-3-mini (快速响应)"
            ],
            "external_ai": [
                "浏览器自动化 (ChatGPT, Claude, Grok)",
                "免费API池 (DeepSeek, Groq)",
                "n8n工作流集成"
            ],
            "intelligent_routing": [
                "任务类型自动识别",
                "复杂度智能评估",
                "成本优化路由决策"
            ]
        },
        "performance_expectations": {
            "local_processing": "90%+ 任务本地处理",
            "response_time": "本地: 2-10秒, 外部: 5-30秒",
            "daily_capacity": "1000+ 请求/天",
            "cost_efficiency": "< $5/天 (包含所有外部AI)"
        },
        "usage_instructions": {
            "startup": "运行 start_hybrid_ai.bat",
            "testing": "运行 test_hybrid_ai.bat",
            "configuration": "编辑 ewandata_system/config/hybrid_ai_config.json",
            "monitoring": "查看系统状态和成本报告"
        },
        "optimization_tips": [
            "优先使用免费资源 (浏览器自动化)",
            "复杂任务自动路由到高级AI",
            "本地模型量化减少显存占用",
            "智能缓存避免重复计算",
            "成本监控防止超支"
        ]
    }
    
    # 保存报告
    with open("deployment_report.json", 'w', encoding='utf-8') as f:
        json.dump(deployment_report, f, ensure_ascii=False, indent=2)
    
    print("✅ 部署报告已生成: deployment_report.json")
    
    # 显示关键信息
    print("\n🎯 部署摘要:")
    print(f"   系统类型: 混合AI架构 (本地+外部)")
    print(f"   GPU配置: {gpu_info}")
    print(f"   本地模型: 3个专用模型")
    print(f"   外部AI: 多种免费和付费选项")
    print(f"   预期成本: < $5/天")
    print(f"   处理能力: 1000+ 请求/天")
    
    return True

def main():
    """主部署函数"""
    print_header("🚀 Ewandata混合AI系统部署")
    
    print("本脚本将部署以下功能:")
    print("✅ 本地多模型协同 (Qwen2-7B + CodeLlama + Phi-3)")
    print("✅ 智能任务路由和复杂度评估")
    print("✅ 外部AI集成 (浏览器自动化 + API)")
    print("✅ 成本优化和免费资源利用")
    print("✅ 完整的监控和管理系统")
    
    # 确认部署
    confirm = input("\n是否继续部署？(y/N): ").lower().strip()
    if confirm != 'y':
        print("部署已取消")
        return
    
    success_steps = 0
    total_steps = 7
    
    # 执行部署步骤
    steps = [
        ("检查系统要求", check_system_requirements),
        ("安装依赖库", install_dependencies),
        ("创建混合AI配置", create_hybrid_ai_config),
        ("下载AI模型", download_models),
        ("测试混合AI系统", test_hybrid_ai_system),
        ("创建启动脚本", create_startup_scripts),
        ("生成部署报告", generate_deployment_report)
    ]
    
    for step_name, step_func in steps:
        try:
            if step_func():
                success_steps += 1
            else:
                print(f"❌ {step_name} 失败")
                break
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
            break
    
    # 显示结果
    print_header("📋 部署结果")
    print(f"完成步骤: {success_steps}/{total_steps}")
    
    if success_steps == total_steps:
        print("🎉 混合AI系统部署完成！")
        print("\n✅ 系统特性:")
        print("   🧠 本地多模型智能协同")
        print("   🌐 外部AI无缝集成")
        print("   🎯 智能任务路由")
        print("   💰 成本优化管理")
        print("   ⚡ 高性能GPU加速")
        
        print("\n🚀 下一步:")
        print("   1. 运行 start_hybrid_ai.bat 启动系统")
        print("   2. 运行 test_hybrid_ai.bat 批量测试")
        print("   3. 查看 deployment_report.json 了解详情")
        print("   4. 根据需要配置API密钥")
        
        print("\n💡 使用提示:")
        print("   - 系统会自动选择最优的AI处理方式")
        print("   - 简单任务使用本地模型 (免费)")
        print("   - 复杂任务智能路由到外部AI")
        print("   - 成本控制确保每日支出 < $5")
        
    else:
        print("⚠️ 部署未完全成功")
        print("请检查错误信息并重新运行部署脚本")

if __name__ == "__main__":
    main()
