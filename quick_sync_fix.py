"""
快速修复GitHub同步问题
"""

import os
import subprocess
from pathlib import Path

def main():
    print("🚀 快速修复GitHub同步...")
    
    # 切换到项目目录
    os.chdir("E:/Ewandata")
    
    # 1. 检查数据库状态
    print("\n📊 检查数据库状态:")
    db_files = [
        "data/metadata.db",
        "data/projects.db"
    ]
    
    for db_file in db_files:
        if Path(db_file).exists():
            size = Path(db_file).stat().st_size
            print(f"✅ {db_file}: {size} bytes")
        else:
            print(f"❌ {db_file}: 不存在")
    
    # 2. 检查知识库文件
    print("\n📁 检查知识库文件:")
    kb_dir = Path("data/knowledge_base")
    if kb_dir.exists():
        md_files = list(kb_dir.glob("*.md"))
        print(f"✅ Markdown文件: {len(md_files)} 个")
    
    proc_dir = Path("data/processed")
    if proc_dir.exists():
        json_files = list(proc_dir.glob("*.json"))
        print(f"✅ JSON文件: {len(json_files)} 个")
    
    # 3. 添加重要文件到Git
    print("\n📤 准备同步到GitHub:")
    important_files = [
        "data/",
        "ewandata_system/",
        "README.md",
        ".gitignore"
    ]
    
    for file_path in important_files:
        if Path(file_path).exists():
            try:
                subprocess.run(['git', 'add', file_path], check=True)
                print(f"✅ 已添加: {file_path}")
            except:
                print(f"⚠️ 添加失败: {file_path}")
    
    # 4. 创建提交
    print("\n💾 创建提交:")
    try:
        commit_msg = "更新Ewandata知识库数据和系统文件"
        result = subprocess.run(['git', 'commit', '-m', commit_msg], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 提交成功: {commit_msg}")
        else:
            print(f"ℹ️ 提交状态: {result.stdout.strip()}")
    except Exception as e:
        print(f"❌ 提交失败: {e}")
    
    # 5. 检查推送状态
    print("\n🔍 检查推送状态:")
    try:
        status_result = subprocess.run(['git', 'status', '-b'], 
                                     capture_output=True, text=True, check=True)
        
        status_lines = status_result.stdout.strip().split('\n')
        branch_line = status_lines[0] if status_lines else ""
        
        print(f"分支状态: {branch_line}")
        
        if "ahead" in branch_line:
            print("📤 有提交需要推送到GitHub")
            print("运行以下命令推送: git push origin main")
        else:
            print("✅ 本地与远程同步")
            
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
    
    # 6. 显示下一步操作
    print("\n🎯 下一步操作建议:")
    print("1. 运行: git push origin main")
    print("2. 检查GitHub仓库确认文件已上传")
    print("3. 验证知识库数据完整性")
    
    print("\n✅ 快速修复完成！")

if __name__ == "__main__":
    main()
