"""
Ewandata 知识库存储系统
支持向量数据库、关系数据库和文件系统的统一管理
"""

import sqlite3
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import uuid
import hashlib

# 向量数据库
try:
    import chromadb
    from chromadb.config import Settings
    CHROMA_AVAILABLE = True
except ImportError:
    CHROMA_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class KnowledgeBase:
    """知识库管理系统"""

    def __init__(self, base_path: str, config: Optional[Dict] = None):
        self.base_path = Path(base_path)
        self.config = config or {}

        # 创建必要目录
        self.data_dir = self.base_path / "data"
        self.vectors_dir = self.data_dir / "vectors"
        self.knowledge_dir = self.data_dir / "knowledge_base"
        self.processed_dir = self.data_dir / "processed"

        for dir_path in [self.data_dir, self.vectors_dir, self.knowledge_dir, self.processed_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # 初始化数据库
        self.sqlite_db = self.data_dir / "metadata.db"
        self.init_sqlite()

        # 初始化向量数据库
        if CHROMA_AVAILABLE:
            self.init_chroma()
        else:
            logger.warning("ChromaDB未安装，向量搜索功能不可用")
            self.chroma_client = None
            self.collection = None

        logger.info(f"知识库初始化完成: {self.base_path}")

    def init_sqlite(self):
        """初始化SQLite数据库"""
        self.conn = sqlite3.connect(str(self.sqlite_db), check_same_thread=False)
        self.conn.row_factory = sqlite3.Row  # 使结果可以按列名访问

        # 创建文档表
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                title TEXT,
                content TEXT,
                file_path TEXT,
                file_hash TEXT,
                file_type TEXT,
                file_size INTEGER,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                tags TEXT,
                keywords TEXT,
                summary TEXT,
                importance_score REAL,
                processing_status TEXT,
                metadata TEXT
            )
        ''')

        # 创建标签表
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS tags (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE,
                description TEXT,
                color TEXT,
                created_at TIMESTAMP
            )
        ''')

        # 创建文档标签关联表
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS document_tags (
                document_id TEXT,
                tag_id INTEGER,
                PRIMARY KEY (document_id, tag_id),
                FOREIGN KEY (document_id) REFERENCES documents (id),
                FOREIGN KEY (tag_id) REFERENCES tags (id)
            )
        ''')

        # 创建搜索历史表
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS search_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query TEXT,
                results_count INTEGER,
                search_time TIMESTAMP,
                search_type TEXT
            )
        ''')

        # 创建索引
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_documents_file_hash ON documents(file_hash)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_documents_file_type ON documents(file_type)')

        self.conn.commit()
        logger.info("SQLite数据库初始化完成")

    def init_chroma(self):
        """初始化ChromaDB向量数据库"""
        try:
            # 配置ChromaDB
            settings = Settings(
                chroma_db_impl="duckdb+parquet",
                persist_directory=str(self.vectors_dir)
            )

            self.chroma_client = chromadb.Client(settings)

            # 创建或获取集合
            collection_name = self.config.get('collection_name', 'ewandata_documents')
            try:
                self.collection = self.chroma_client.get_collection(collection_name)
                logger.info(f"使用现有向量集合: {collection_name}")
            except:
                self.collection = self.chroma_client.create_collection(
                    name=collection_name,
                    metadata={"description": "Ewandata文档向量存储"}
                )
                logger.info(f"创建新向量集合: {collection_name}")

        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {str(e)}")
            self.chroma_client = None
            self.collection = None

    def store_document(self, doc_data: Dict[str, Any], embeddings: Optional[List[float]] = None) -> str:
        """存储文档到知识库"""
        try:
            # 生成文档ID
            doc_id = self._generate_doc_id(doc_data)

            # 检查是否已存在
            if self._document_exists(doc_data.get('file_hash', '')):
                logger.info(f"文档已存在，跳过存储: {doc_data.get('file_name', 'unknown')}")
                return self._get_existing_doc_id(doc_data.get('file_hash', ''))

            # 准备数据
            now = datetime.now().isoformat()

            # 存储到SQLite
            self.conn.execute('''
                INSERT INTO documents
                (id, title, content, file_path, file_hash, file_type, file_size,
                 created_at, updated_at, tags, keywords, summary, importance_score,
                 processing_status, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                doc_id,
                doc_data.get('title', doc_data.get('file_name', '')),
                doc_data.get('content', ''),
                doc_data.get('file_path', ''),
                doc_data.get('file_hash', ''),
                doc_data.get('type', doc_data.get('file_extension', '')),
                doc_data.get('size', 0),
                now,
                now,
                ','.join(doc_data.get('tags', [])),
                ','.join(doc_data.get('keywords', [])),
                doc_data.get('summary', ''),
                doc_data.get('importance_score', 0.5),
                'completed',
                json.dumps({k: v for k, v in doc_data.items() if k not in [
                    'content', 'title', 'file_path', 'file_hash', 'type', 'size'
                ]})
            ))

            # 存储到向量数据库
            if self.collection and embeddings:
                self.collection.add(
                    documents=[doc_data.get('content', '')[:1000]],  # 限制长度
                    metadatas=[{
                        "title": doc_data.get('title', ''),
                        "file_path": doc_data.get('file_path', ''),
                        "file_type": doc_data.get('type', ''),
                        "tags": ','.join(doc_data.get('tags', [])),
                        "created_at": now
                    }],
                    embeddings=[embeddings],
                    ids=[doc_id]
                )

            # 保存为Markdown文件
            self._save_as_markdown(doc_id, doc_data)

            # 保存为JSON文件
            self._save_as_json(doc_id, doc_data)

            self.conn.commit()
            logger.info(f"文档存储完成: {doc_id}")
            return doc_id

        except Exception as e:
            logger.error(f"文档存储失败: {str(e)}")
            self.conn.rollback()
            raise

    def _generate_doc_id(self, doc_data: Dict[str, Any]) -> str:
        """生成文档ID"""
        # 使用文件哈希和时间戳生成唯一ID
        file_hash = doc_data.get('file_hash', '')
        timestamp = datetime.now().isoformat()

        if file_hash:
            return f"doc_{file_hash[:8]}_{int(datetime.now().timestamp())}"
        else:
            return f"doc_{uuid.uuid4().hex[:8]}_{int(datetime.now().timestamp())}"

    def _document_exists(self, file_hash: str) -> bool:
        """检查文档是否已存在"""
        if not file_hash:
            return False

        cursor = self.conn.execute(
            'SELECT COUNT(*) FROM documents WHERE file_hash = ?',
            (file_hash,)
        )
        return cursor.fetchone()[0] > 0

    def _get_existing_doc_id(self, file_hash: str) -> str:
        """获取已存在文档的ID"""
        cursor = self.conn.execute(
            'SELECT id FROM documents WHERE file_hash = ? LIMIT 1',
            (file_hash,)
        )
        result = cursor.fetchone()
        return result[0] if result else ""

    def _save_as_markdown(self, doc_id: str, doc_data: Dict[str, Any]):
        """保存为Markdown格式"""
        try:
            md_file = self.knowledge_dir / f"{doc_id}.md"

            # 构建Markdown内容
            content = f"""# {doc_data.get('title', doc_data.get('file_name', 'Untitled'))}

## 文档信息
- **文件路径**: {doc_data.get('file_path', '')}
- **文件类型**: {doc_data.get('type', '')}
- **文件大小**: {doc_data.get('size_mb', 0):.2f} MB
- **创建时间**: {doc_data.get('created_time', '')}
- **修改时间**: {doc_data.get('modified_time', '')}

## 标签
{', '.join(doc_data.get('tags', []))}

## 关键词
{', '.join(doc_data.get('keywords', []))}

## 摘要
{doc_data.get('summary', '')}

## 内容
{doc_data.get('content', '')}

---
*文档ID: {doc_id}*
*重要性评分: {doc_data.get('importance_score', 0.5):.2f}*
"""

            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(content)

        except Exception as e:
            logger.error(f"保存Markdown文件失败: {str(e)}")

    def _save_as_json(self, doc_id: str, doc_data: Dict[str, Any]):
        """保存为JSON格式"""
        try:
            json_file = self.processed_dir / f"{doc_id}.json"

            # 准备JSON数据
            json_data = {
                "id": doc_id,
                "timestamp": datetime.now().isoformat(),
                "document": doc_data
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")

    def search_similar(self, query: str, n_results: int = 5, query_embeddings: Optional[List[float]] = None) -> List[Dict]:
        """语义搜索相似内容"""
        if not self.collection:
            logger.warning("向量数据库不可用，无法进行语义搜索")
            return []

        try:
            # 记录搜索历史
            self._record_search(query, 'semantic')

            if query_embeddings:
                # 使用提供的嵌入向量
                results = self.collection.query(
                    query_embeddings=[query_embeddings],
                    n_results=n_results
                )
            else:
                # 使用文本查询
                results = self.collection.query(
                    query_texts=[query],
                    n_results=n_results
                )

            # 格式化结果
            formatted_results = []
            if results['documents']:
                for i in range(len(results['documents'][0])):
                    formatted_results.append({
                        'id': results['ids'][0][i],
                        'content': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'distance': results['distances'][0][i] if 'distances' in results else None
                    })

            return formatted_results

        except Exception as e:
            logger.error(f"语义搜索失败: {str(e)}")
            return []

    def search_text(self, query: str, limit: int = 10) -> List[Dict]:
        """全文搜索"""
        try:
            # 记录搜索历史
            self._record_search(query, 'text')

            # SQL全文搜索
            cursor = self.conn.execute('''
                SELECT id, title, content, file_path, file_type, tags, keywords,
                       summary, importance_score, created_at
                FROM documents
                WHERE content LIKE ? OR title LIKE ? OR tags LIKE ? OR keywords LIKE ?
                ORDER BY importance_score DESC, created_at DESC
                LIMIT ?
            ''', (f'%{query}%', f'%{query}%', f'%{query}%', f'%{query}%', limit))

            results = []
            for row in cursor.fetchall():
                results.append({
                    'id': row['id'],
                    'title': row['title'],
                    'content': row['content'][:500] + '...' if len(row['content']) > 500 else row['content'],
                    'file_path': row['file_path'],
                    'file_type': row['file_type'],
                    'tags': row['tags'].split(',') if row['tags'] else [],
                    'keywords': row['keywords'].split(',') if row['keywords'] else [],
                    'summary': row['summary'],
                    'importance_score': row['importance_score'],
                    'created_at': row['created_at']
                })

            return results

        except Exception as e:
            logger.error(f"全文搜索失败: {str(e)}")
            return []

    def get_document(self, doc_id: str) -> Optional[Dict]:
        """获取单个文档"""
        try:
            cursor = self.conn.execute(
                'SELECT * FROM documents WHERE id = ?',
                (doc_id,)
            )
            row = cursor.fetchone()

            if row:
                return {
                    'id': row['id'],
                    'title': row['title'],
                    'content': row['content'],
                    'file_path': row['file_path'],
                    'file_hash': row['file_hash'],
                    'file_type': row['file_type'],
                    'file_size': row['file_size'],
                    'created_at': row['created_at'],
                    'updated_at': row['updated_at'],
                    'tags': row['tags'].split(',') if row['tags'] else [],
                    'keywords': row['keywords'].split(',') if row['keywords'] else [],
                    'summary': row['summary'],
                    'importance_score': row['importance_score'],
                    'processing_status': row['processing_status'],
                    'metadata': json.loads(row['metadata']) if row['metadata'] else {}
                }

            return None

        except Exception as e:
            logger.error(f"获取文档失败: {str(e)}")
            return None

    def list_documents(self, limit: int = 50, offset: int = 0, file_type: Optional[str] = None) -> List[Dict]:
        """列出文档"""
        try:
            if file_type:
                cursor = self.conn.execute('''
                    SELECT id, title, file_path, file_type, file_size, created_at,
                           tags, summary, importance_score
                    FROM documents
                    WHERE file_type = ?
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                ''', (file_type, limit, offset))
            else:
                cursor = self.conn.execute('''
                    SELECT id, title, file_path, file_type, file_size, created_at,
                           tags, summary, importance_score
                    FROM documents
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                ''', (limit, offset))

            results = []
            for row in cursor.fetchall():
                results.append({
                    'id': row['id'],
                    'title': row['title'],
                    'file_path': row['file_path'],
                    'file_type': row['file_type'],
                    'file_size': row['file_size'],
                    'created_at': row['created_at'],
                    'tags': row['tags'].split(',') if row['tags'] else [],
                    'summary': row['summary'],
                    'importance_score': row['importance_score']
                })

            return results

        except Exception as e:
            logger.error(f"列出文档失败: {str(e)}")
            return []

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            stats = {}

            # 文档总数
            cursor = self.conn.execute('SELECT COUNT(*) FROM documents')
            stats['total_documents'] = cursor.fetchone()[0]

            # 按类型统计
            cursor = self.conn.execute('''
                SELECT file_type, COUNT(*) as count
                FROM documents
                GROUP BY file_type
                ORDER BY count DESC
            ''')
            stats['by_type'] = {row[0]: row[1] for row in cursor.fetchall()}

            # 总大小
            cursor = self.conn.execute('SELECT SUM(file_size) FROM documents')
            total_size = cursor.fetchone()[0] or 0
            stats['total_size_mb'] = total_size / (1024 * 1024)

            # 最近添加
            cursor = self.conn.execute('''
                SELECT COUNT(*) FROM documents
                WHERE created_at > datetime('now', '-7 days')
            ''')
            stats['recent_documents'] = cursor.fetchone()[0]

            # 标签统计
            cursor = self.conn.execute('''
                SELECT tags FROM documents WHERE tags IS NOT NULL AND tags != ''
            ''')
            all_tags = []
            for row in cursor.fetchall():
                all_tags.extend(row[0].split(','))

            from collections import Counter
            tag_counts = Counter(tag.strip() for tag in all_tags if tag.strip())
            stats['top_tags'] = dict(tag_counts.most_common(10))

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}

    def _record_search(self, query: str, search_type: str):
        """记录搜索历史"""
        try:
            self.conn.execute('''
                INSERT INTO search_history (query, search_time, search_type)
                VALUES (?, ?, ?)
            ''', (query, datetime.now().isoformat(), search_type))
            self.conn.commit()
        except Exception as e:
            logger.error(f"记录搜索历史失败: {str(e)}")

    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
        if self.chroma_client:
            # ChromaDB会自动持久化
            pass


# 全局知识库实例
knowledge_base = None

def get_knowledge_base(base_path: str = "E:/Ewandata", config: Optional[Dict] = None) -> KnowledgeBase:
    """获取全局知识库实例"""
    global knowledge_base
    if knowledge_base is None:
        knowledge_base = KnowledgeBase(base_path, config)
    return knowledge_base


if __name__ == "__main__":
    # 测试代码
    kb = KnowledgeBase("E:/Ewandata")

    print("知识库统计信息:")
    stats = kb.get_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")

    # 测试搜索
    results = kb.search_text("测试")
    print(f"\n搜索结果数量: {len(results)}")

    kb.close()