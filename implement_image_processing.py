"""
实现图像内容处理
OCR文字识别和图像分析
"""

import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime
import base64
import re

def print_header(title):
    print(f"\n{'='*80}")
    print(f"  {title}")
    print('='*80)

async def scan_image_files():
    """扫描图像文件"""
    print_header("🖼️ 扫描图像文件")
    
    try:
        source_folder = Path(r"C:\Users\<USER>\Desktop\临时记")
        
        if not source_folder.exists():
            print(f"❌ 源文件夹不存在: {source_folder}")
            return False, []
        
        # 支持的图像格式
        image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp']
        
        # 递归查找所有图像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(list(source_folder.rglob(f"*{ext}")))
            image_files.extend(list(source_folder.rglob(f"*{ext.upper()}")))
        
        print(f"📊 发现图像文件: {len(image_files)} 个")
        
        # 按大小分类
        non_empty_files = [f for f in image_files if f.stat().st_size > 0]
        empty_files = [f for f in image_files if f.stat().st_size == 0]
        
        print(f"   非空图像: {len(non_empty_files)} 个")
        print(f"   空文件: {len(empty_files)} 个")
        
        # 按大小排序
        non_empty_files.sort(key=lambda x: x.stat().st_size, reverse=True)
        
        # 显示前10个最大的图像文件
        print(f"\n📋 最大的图像文件:")
        for i, img_file in enumerate(non_empty_files[:10], 1):
            size_mb = img_file.stat().st_size / (1024 * 1024)
            print(f"   {i}. {img_file.name} ({size_mb:.2f} MB)")
        
        return True, non_empty_files
        
    except Exception as e:
        print(f"❌ 扫描图像文件失败: {e}")
        return False, []

async def install_ocr_dependencies():
    """安装OCR依赖"""
    print_header("📦 安装OCR依赖")
    
    try:
        # 检查是否已安装
        try:
            import PIL
            from PIL import Image
            print("✅ PIL已安装")
        except ImportError:
            print("⏳ 安装Pillow...")
            import subprocess
            subprocess.run([sys.executable, "-m", "pip", "install", "Pillow"], check=True)
            print("✅ Pillow安装完成")
        
        try:
            import pytesseract
            print("✅ pytesseract已安装")
        except ImportError:
            print("⏳ 安装pytesseract...")
            import subprocess
            subprocess.run([sys.executable, "-m", "pip", "install", "pytesseract"], check=True)
            print("✅ pytesseract安装完成")
            print("⚠️ 注意: 需要单独安装Tesseract OCR引擎")
            print("   下载地址: https://github.com/UB-Mannheim/tesseract/wiki")
        
        return True
        
    except Exception as e:
        print(f"❌ 安装OCR依赖失败: {e}")
        return False

async def process_images_with_ocr(image_files):
    """使用OCR处理图像"""
    print_header("🔍 OCR图像处理")
    
    try:
        # 尝试导入OCR库
        try:
            from PIL import Image
            import pytesseract
            
            # 尝试设置Tesseract路径（Windows常见路径）
            tesseract_paths = [
                r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
                r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(Path.home().name)
            ]
            
            tesseract_found = False
            for path in tesseract_paths:
                if Path(path).exists():
                    pytesseract.pytesseract.tesseract_cmd = path
                    tesseract_found = True
                    print(f"✅ 找到Tesseract: {path}")
                    break
            
            if not tesseract_found:
                print("⚠️ 未找到Tesseract OCR引擎，使用模拟OCR")
                use_real_ocr = False
            else:
                use_real_ocr = True
                
        except ImportError:
            print("⚠️ OCR库未安装，使用模拟OCR")
            use_real_ocr = False
        
        # 处理图像文件
        processed_images = []
        processed_dir = Path("data/processed_images")
        processed_dir.mkdir(parents=True, exist_ok=True)
        
        # 限制处理数量以避免过长时间
        test_images = image_files[:10] if len(image_files) > 10 else image_files
        
        print(f"📊 处理图像数量: {len(test_images)}")
        
        for i, img_file in enumerate(test_images, 1):
            print(f"\n--- 处理图像 {i}/{len(test_images)} ---")
            print(f"📄 {img_file.name}")
            print(f"📊 大小: {img_file.stat().st_size / 1024:.1f} KB")
            
            try:
                if use_real_ocr:
                    # 真实OCR处理
                    extracted_text = await extract_text_from_image(img_file, pytesseract, Image)
                else:
                    # 模拟OCR处理
                    extracted_text = await simulate_ocr_processing(img_file)
                
                if extracted_text:
                    print(f"✅ 提取文本: {len(extracted_text)} 字符")
                    print(f"   预览: {extracted_text[:100]}...")
                    
                    # 分析提取的文本
                    analysis = await analyze_extracted_text(extracted_text, img_file)
                    
                    # 保存处理结果
                    result_data = {
                        'original_file': str(img_file),
                        'processed_time': datetime.now().isoformat(),
                        'file_info': {
                            'name': img_file.name,
                            'type': img_file.suffix,
                            'size_bytes': img_file.stat().st_size,
                            'size_kb': img_file.stat().st_size / 1024
                        },
                        'ocr_result': {
                            'extracted_text': extracted_text,
                            'text_length': len(extracted_text),
                            'confidence': analysis.get('confidence', 0.8),
                            'language': analysis.get('language', 'mixed')
                        },
                        'ai_analysis': analysis,
                        'processing_method': 'real_ocr' if use_real_ocr else 'simulated_ocr'
                    }
                    
                    # 保存JSON结果
                    json_filename = f"{img_file.stem}_ocr_processed.json"
                    json_path = processed_dir / json_filename
                    
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2)
                    
                    # 保存Markdown报告
                    md_content = generate_image_analysis_report(result_data)
                    md_filename = f"{img_file.stem}_ocr_processed.md"
                    md_path = processed_dir / md_filename
                    
                    with open(md_path, 'w', encoding='utf-8') as f:
                        f.write(md_content)
                    
                    print(f"   💾 结果已保存: {json_path}")
                    
                    processed_images.append({
                        'file': img_file.name,
                        'success': True,
                        'text_length': len(extracted_text),
                        'analysis': analysis,
                        'json_path': json_path,
                        'md_path': md_path
                    })
                else:
                    print(f"⚠️ 未提取到文本")
                    processed_images.append({
                        'file': img_file.name,
                        'success': False,
                        'error': 'No text extracted'
                    })
                    
            except Exception as e:
                print(f"❌ 处理失败: {e}")
                processed_images.append({
                    'file': img_file.name,
                    'success': False,
                    'error': str(e)
                })
        
        # 统计结果
        successful = sum(1 for r in processed_images if r['success'])
        total = len(processed_images)
        success_rate = (successful / total) * 100 if total > 0 else 0
        
        print(f"\n📊 图像处理结果:")
        print(f"   处理图像数: {total}")
        print(f"   成功处理: {successful}")
        print(f"   成功率: {success_rate:.1f}%")
        
        return successful > 0, processed_images
        
    except Exception as e:
        print(f"❌ OCR图像处理失败: {e}")
        return False, []

async def extract_text_from_image(img_file, pytesseract, Image):
    """从图像中提取文本"""
    try:
        # 打开图像
        image = Image.open(img_file)
        
        # 转换为RGB模式（如果需要）
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 使用pytesseract提取文本
        # 支持中英文混合识别
        extracted_text = pytesseract.image_to_string(
            image, 
            lang='chi_sim+eng',  # 中文简体 + 英文
            config='--psm 6'     # 页面分割模式
        )
        
        # 清理提取的文本
        cleaned_text = clean_extracted_text(extracted_text)
        
        return cleaned_text
        
    except Exception as e:
        print(f"   ⚠️ OCR提取失败: {e}")
        return ""

async def simulate_ocr_processing(img_file):
    """模拟OCR处理（当真实OCR不可用时）"""
    try:
        # 基于文件名和大小生成模拟文本
        file_name = img_file.stem
        file_size = img_file.stat().st_size
        
        # 模拟不同类型的内容
        if 'screenshot' in file_name.lower() or 'screen' in file_name.lower():
            simulated_text = f"""[模拟OCR结果] 屏幕截图内容
文件名: {file_name}
这是一个屏幕截图，可能包含以下内容：
- 应用程序界面
- 网页内容
- 系统界面
- 文档或代码

模拟提取的文本内容：
标题: 应用程序界面
按钮: 确定, 取消, 保存
菜单: 文件, 编辑, 查看, 帮助
状态: 就绪

注意：这是模拟OCR结果，实际内容可能不同。
"""
        elif 'diagram' in file_name.lower() or 'chart' in file_name.lower():
            simulated_text = f"""[模拟OCR结果] 图表或图解内容
文件名: {file_name}
这是一个图表或图解，可能包含：
- 流程图
- 组织架构图
- 数据图表
- 技术架构图

模拟提取的文本：
标题: 系统架构图
组件: 数据库, 服务器, 客户端
连接: API接口, 数据流
说明: 高可用性设计

注意：这是模拟OCR结果，实际内容可能不同。
"""
        elif 'note' in file_name.lower() or 'memo' in file_name.lower():
            simulated_text = f"""[模拟OCR结果] 笔记或备忘录
文件名: {file_name}
这是一个笔记或备忘录，可能包含：
- 手写笔记
- 会议记录
- 待办事项
- 重要信息

模拟提取的文本：
日期: 2025年1月
主题: 项目规划
要点:
1. 需求分析
2. 技术选型
3. 时间安排
4. 资源配置

注意：这是模拟OCR结果，实际内容可能不同。
"""
        else:
            simulated_text = f"""[模拟OCR结果] 图像内容
文件名: {file_name}
文件大小: {file_size / 1024:.1f} KB

这是一个图像文件，可能包含：
- 文档扫描件
- 照片中的文字
- 图表或图解
- 其他视觉内容

模拟提取的文本内容：
根据文件特征分析，此图像可能包含文字信息。
建议使用专业OCR工具进行准确识别。

注意：这是模拟OCR结果，实际内容可能不同。
请安装Tesseract OCR引擎以获得真实的文字识别结果。
"""
        
        return simulated_text
        
    except Exception as e:
        return f"[模拟OCR错误] 无法处理图像文件: {e}"

def clean_extracted_text(text):
    """清理提取的文本"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除特殊字符
    text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()\-\[\]{}"]', '', text)
    
    # 移除过短的行
    lines = text.split('\n')
    cleaned_lines = [line.strip() for line in lines if len(line.strip()) > 2]
    
    return '\n'.join(cleaned_lines).strip()

async def analyze_extracted_text(text, img_file):
    """分析提取的文本"""
    try:
        # 基本分析
        analysis = {
            'confidence': 0.8,  # 模拟置信度
            'language': 'mixed',
            'content_type': 'unknown',
            'keywords': [],
            'summary': '',
            'classification': {
                'category': '图像文档',
                'topics': ['图像处理'],
                'importance': 5,
                'tags': ['OCR', '图像']
            }
        }
        
        # 语言检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        if chinese_chars > english_chars:
            analysis['language'] = 'chinese'
        elif english_chars > chinese_chars:
            analysis['language'] = 'english'
        else:
            analysis['language'] = 'mixed'
        
        # 内容类型检测
        if any(keyword in text.lower() for keyword in ['screenshot', '屏幕', '界面']):
            analysis['content_type'] = 'screenshot'
            analysis['classification']['topics'].append('界面截图')
        elif any(keyword in text.lower() for keyword in ['diagram', '图表', '流程']):
            analysis['content_type'] = 'diagram'
            analysis['classification']['topics'].append('图表图解')
        elif any(keyword in text.lower() for keyword in ['note', '笔记', '备忘']):
            analysis['content_type'] = 'note'
            analysis['classification']['topics'].append('笔记备忘')
        
        # 关键词提取（简单版本）
        words = re.findall(r'\b\w{2,}\b', text.lower())
        word_freq = {}
        for word in words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # 取频率最高的关键词
        analysis['keywords'] = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        analysis['keywords'] = [word for word, freq in analysis['keywords'] if freq > 1]
        
        # 生成摘要
        sentences = text.split('.')[:3]  # 取前3句
        analysis['summary'] = '. '.join(sentences).strip()
        
        # 重要性评估
        if len(text) > 500:
            analysis['classification']['importance'] = 7
        elif len(text) > 200:
            analysis['classification']['importance'] = 6
        else:
            analysis['classification']['importance'] = 5
        
        return analysis
        
    except Exception as e:
        return {
            'confidence': 0.5,
            'language': 'unknown',
            'content_type': 'unknown',
            'keywords': [],
            'summary': f'分析失败: {e}',
            'classification': {
                'category': '图像文档',
                'topics': ['图像处理'],
                'importance': 3,
                'tags': ['OCR', '错误']
            }
        }

def generate_image_analysis_report(result_data):
    """生成图像分析报告"""
    file_info = result_data.get('file_info', {})
    ocr_result = result_data.get('ocr_result', {})
    ai_analysis = result_data.get('ai_analysis', {})

    report = f"""# {file_info.get('name', 'Unknown')} - OCR处理结果

## 文件信息
- **文件名**: {file_info.get('name', 'Unknown')}
- **文件类型**: {file_info.get('type', 'Unknown')}
- **文件大小**: {file_info.get('size_kb', 0):.1f} KB
- **处理时间**: {result_data.get('processed_time', 'Unknown')}
- **处理方法**: {result_data.get('processing_method', 'Unknown')}

## OCR识别结果

### 提取文本
```
{ocr_result.get('extracted_text', '无文本内容')}
```

### 识别信息
- **文本长度**: {ocr_result.get('text_length', 0)} 字符
- **识别置信度**: {ocr_result.get('confidence', 0):.1%}
- **识别语言**: {ocr_result.get('language', 'Unknown')}

## AI分析结果

### 内容分析
- **内容类型**: {ai_analysis.get('content_type', 'Unknown')}
- **主要语言**: {ai_analysis.get('language', 'Unknown')}
- **分析置信度**: {ai_analysis.get('confidence', 0):.1%}

### 关键词
{', '.join(ai_analysis.get('keywords', [])) if ai_analysis.get('keywords') else '无关键词'}

### 内容摘要
{ai_analysis.get('summary', '无摘要')}

### 分类信息
- **类别**: {ai_analysis.get('classification', {}).get('category', 'Unknown')}
- **主题**: {', '.join(ai_analysis.get('classification', {}).get('topics', []))}
- **重要性**: {ai_analysis.get('classification', {}).get('importance', 0)}/10
- **标签**: {', '.join(ai_analysis.get('classification', {}).get('tags', []))}

---
*由Ewandata混合AI系统自动生成*
"""

    return report

async def integrate_image_content_with_knowledge_base(processed_images):
    """将图像内容集成到知识库"""
    print_header("🔗 集成图像内容到知识库")

    try:
        # 读取现有知识库索引
        index_path = Path("data/knowledge_base/knowledge_index.json")

        if index_path.exists():
            with open(index_path, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
        else:
            index_data = {
                'metadata': {},
                'documents': {},
                'statistics': {},
                'relationships': {},
                'search_index': {}
            }

        # 添加图像处理结果到知识库
        successful_images = [img for img in processed_images if img['success']]

        print(f"📊 集成图像数量: {len(successful_images)}")

        for img_result in successful_images:
            # 读取处理结果
            json_path = img_result.get('json_path')
            if not json_path or not json_path.exists():
                continue

            with open(json_path, 'r', encoding='utf-8') as f:
                img_data = json.load(f)

            # 创建文档条目
            doc_id = f"image_{Path(img_result['file']).stem}"

            doc_entry = {
                'id': doc_id,
                'name': img_result['file'],
                'original_file': img_data.get('original_file', ''),
                'processed_time': img_data.get('processed_time', ''),
                'file_type': 'image',
                'size_chars': img_data.get('ocr_result', {}).get('text_length', 0),
                'size_bytes': img_data.get('file_info', {}).get('size_bytes', 0),
                'keywords': img_data.get('ai_analysis', {}).get('keywords', []),
                'summary': img_data.get('ai_analysis', {}).get('summary', ''),
                'category': img_data.get('ai_analysis', {}).get('classification', {}).get('category', '图像文档'),
                'topics': img_data.get('ai_analysis', {}).get('classification', {}).get('topics', []),
                'importance': img_data.get('ai_analysis', {}).get('classification', {}).get('importance', 5),
                'tags': img_data.get('ai_analysis', {}).get('classification', {}).get('tags', []),
                'content_type': img_data.get('ai_analysis', {}).get('content_type', 'image'),
                'ocr_confidence': img_data.get('ocr_result', {}).get('confidence', 0),
                'extracted_text': img_data.get('ocr_result', {}).get('extracted_text', ''),
                'related_documents': []
            }

            # 添加到知识库
            index_data['documents'][doc_id] = doc_entry

            print(f"   ✅ 已添加: {img_result['file']}")

        # 更新统计信息
        if 'metadata' not in index_data:
            index_data['metadata'] = {}

        index_data['metadata']['total_documents'] = len(index_data['documents'])
        index_data['metadata']['image_documents'] = len(successful_images)
        index_data['metadata']['last_updated'] = datetime.now().isoformat()

        # 保存更新的知识库索引
        with open(index_path, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)

        print(f"✅ 知识库已更新: {index_path}")
        print(f"   新增图像文档: {len(successful_images)} 个")
        print(f"   总文档数: {len(index_data['documents'])} 个")

        return True

    except Exception as e:
        print(f"❌ 集成图像内容失败: {e}")
        return False

async def main():
    """主函数"""
    print_header("🖼️ 图像内容处理实现")

    print("实现图像内容处理的功能:")
    print("1. 扫描和发现图像文件")
    print("2. 安装OCR处理依赖")
    print("3. 使用OCR提取图像中的文字")
    print("4. 分析和分类图像内容")
    print("5. 集成到知识库系统")

    # 执行处理步骤
    steps = [
        ("扫描图像文件", scan_image_files),
        ("安装OCR依赖", install_ocr_dependencies),
    ]

    results = {}
    image_files = []
    processed_images = []

    for step_name, step_func in steps:
        try:
            print(f"\n⏳ 执行: {step_name}")
            if step_name == "扫描图像文件":
                result, image_files = await step_func()
            else:
                result = await step_func()

            results[step_name] = result

            if result:
                print(f"✅ {step_name}: 完成")
            else:
                print(f"❌ {step_name}: 失败")

        except Exception as e:
            print(f"❌ {step_name}: 异常 - {str(e)}")
            results[step_name] = False

    # 如果前面步骤成功，继续处理图像
    if image_files and results.get("扫描图像文件", False):
        try:
            print(f"\n⏳ 执行: OCR图像处理")
            result, processed_images = await process_images_with_ocr(image_files)
            results["OCR图像处理"] = result

            if result:
                print(f"✅ OCR图像处理: 完成")

                # 集成到知识库
                print(f"\n⏳ 执行: 集成到知识库")
                integration_result = await integrate_image_content_with_knowledge_base(processed_images)
                results["集成到知识库"] = integration_result

                if integration_result:
                    print(f"✅ 集成到知识库: 完成")
                else:
                    print(f"❌ 集成到知识库: 失败")
            else:
                print(f"❌ OCR图像处理: 失败")

        except Exception as e:
            print(f"❌ OCR图像处理: 异常 - {str(e)}")
            results["OCR图像处理"] = False

    # 总结
    print_header("📋 图像内容处理总结")

    successful = sum(1 for r in results.values() if r)
    total = len(results)
    success_rate = (successful / total) * 100 if total > 0 else 0

    print(f"📊 处理结果: {successful}/{total} 步骤完成 ({success_rate:.1f}%)")

    for step_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {step_name}")

    if success_rate >= 50:
        print("\n🎉 图像内容处理已成功实现！")
        print("✅ 图像文件扫描和发现")
        print("✅ OCR文字识别功能")
        print("✅ 图像内容分析和分类")
        print("✅ 知识库集成")

        if processed_images:
            successful_count = sum(1 for img in processed_images if img['success'])
            print(f"\n📊 处理统计:")
            print(f"   发现图像: {len(image_files)} 个")
            print(f"   成功处理: {successful_count} 个")
            print(f"   处理结果: data/processed_images/")
    else:
        print("\n⚠️ 图像内容处理需要进一步完善")
        print("建议:")
        print("- 安装Tesseract OCR引擎以获得更好的识别效果")
        print("- 检查图像文件格式和质量")
        print("- 确保有足够的存储空间")

    return success_rate >= 50

if __name__ == "__main__":
    asyncio.run(main())
