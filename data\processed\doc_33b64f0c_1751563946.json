{"id": "doc_33b64f0c_1751563946", "timestamp": "2025-07-04T01:32:26.943151", "document": {"file_name": "test_config_1751563945.json", "file_path": "C:\\Users\\<USER>\\Desktop\\临时记\\test_config_1751563945.json", "file_extension": ".json", "size": 739, "size_mb": 0.0007047653198242188, "created_time": "2025-07-04T01:32:25.990189", "modified_time": "2025-07-04T01:32:25.991186", "file_hash": "33b64f0cb03ad5e15934c6e00367a7fc", "mime_type": "application/json", "type": "json", "content": "{\n  \"project_name\": \"Ewandata_Test\",\n  \"timestamp\": \"2025-07-04T01:32:25.990188\",\n  \"test_config\": {\n    \"auto_processing\": true,\n    \"github_sync\": true,\n    \"cleanup_original\": true\n  },\n  \"expected_outputs\": [\n    \"knowledge_base_entry.md\",\n    \"structured_data.json\",\n    \"github_commit\"\n  ],\n  \"test_data\": {\n    \"keywords\": [\n      \"知识管理\",\n      \"自动化\",\n      \"AI\",\n      \"协同创新\"\n    ],\n    \"categories\": [\n      \"系统测试\",\n      \"功能验证\",\n      \"端到端测试\"\n    ],\n    \"priority\": \"high\",\n    \"processing_requirements\": {\n      \"extract_keywords\": true,\n      \"generate_summary\": true,\n      \"create_associations\": true,\n      \"upload_to_github\": true\n    }\n  }\n}", "data_structure": {"root.project_name": "str", "root.timestamp": "str", "root.test_config": {"root.test_config.auto_processing": "bool", "root.test_config.github_sync": "bool", "root.test_config.cleanup_original": "bool"}, "root.expected_outputs": "array[3]", "root.test_data": {"root.test_data.keywords": "array[4]", "root.test_data.categories": "array[3]", "root.test_data.priority": "str", "root.test_data.processing_requirements": {"root.test_data.processing_requirements.extract_keywords": "bool", "root.test_data.processing_requirements.generate_summary": "bool", "root.test_data.processing_requirements.create_associations": "bool", "root.test_data.processing_requirements.upload_to_github": "bool"}}}, "char_count": 658, "is_array": false, "is_object": true, "processing_time": "2025-07-04T01:32:26.888184", "processor_version": "1.0.0"}}