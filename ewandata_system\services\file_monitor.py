"""
文件监控服务
监控桌面临时记文件夹，自动处理新文件
"""

import os
import time
import threading
from pathlib import Path
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class FileMonitor:
    """文件监控器"""
    
    def __init__(self, watch_folder):
        """
        初始化文件监控器
        
        Args:
            watch_folder: 要监控的文件夹路径
        """
        self.watch_folder = Path(watch_folder)
        self.observer = None
        self.is_running = False
        self.processed_files = set()
        
        # 确保监控文件夹存在
        self.watch_folder.mkdir(parents=True, exist_ok=True)
        
        print(f"文件监控器初始化: {self.watch_folder}")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            print("文件监控已在运行")
            return
        
        try:
            event_handler = FileEventHandler(self)
            self.observer = Observer()
            self.observer.schedule(event_handler, str(self.watch_folder), recursive=False)
            self.observer.start()
            self.is_running = True
            print(f"✅ 开始监控文件夹: {self.watch_folder}")
            
        except Exception as e:
            print(f"❌ 启动文件监控失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.observer and self.is_running:
            self.observer.stop()
            self.observer.join()
            self.is_running = False
            print("✅ 文件监控已停止")
    
    def scan_existing_files(self):
        """扫描现有文件"""
        existing_files = []
        
        if not self.watch_folder.exists():
            return existing_files
        
        for file_path in self.watch_folder.iterdir():
            if file_path.is_file() and not file_path.name.startswith('.'):
                existing_files.append(file_path)
        
        print(f"发现 {len(existing_files)} 个现有文件")
        return existing_files
    
    def process_file(self, file_path):
        """处理单个文件"""
        file_path = Path(file_path)
        
        if str(file_path) in self.processed_files:
            return
        
        if not file_path.exists() or file_path.is_dir():
            return
        
        # 跳过临时文件和隐藏文件
        if file_path.name.startswith('.') or file_path.name.startswith('~'):
            return
        
        print(f"🔄 处理文件: {file_path.name}")
        
        try:
            # 导入处理模块
            from processors.document_processor import DocumentProcessor
            from storage.knowledge_base import KnowledgeBase
            
            # 处理文档
            processor = DocumentProcessor()
            doc_data = processor.process_file(str(file_path))
            
            if "error" in doc_data:
                print(f"❌ 文档处理失败: {doc_data['error']}")
                return
            
            # 存储到知识库
            kb = KnowledgeBase("E:/Ewandata")
            doc_id = kb.store_document(doc_data)
            kb.close()
            
            print(f"✅ 文档处理完成，ID: {doc_id}")
            
            # 标记为已处理
            self.processed_files.add(str(file_path))
            
            # 删除原文件
            try:
                file_path.unlink()
                print(f"🗑️ 原文件已删除: {file_path.name}")
            except Exception as e:
                print(f"⚠️ 删除原文件失败: {e}")
            
        except ImportError as e:
            print(f"❌ 导入模块失败: {e}")
        except Exception as e:
            print(f"❌ 处理文件失败: {e}")


class FileEventHandler(FileSystemEventHandler):
    """文件事件处理器"""
    
    def __init__(self, monitor):
        self.monitor = monitor
        super().__init__()
    
    def on_created(self, event):
        """文件创建事件"""
        if not event.is_directory:
            # 等待文件写入完成
            time.sleep(2)
            self.monitor.process_file(event.src_path)
    
    def on_modified(self, event):
        """文件修改事件"""
        if not event.is_directory:
            # 等待文件写入完成
            time.sleep(2)
            self.monitor.process_file(event.src_path)


def main():
    """主函数 - 用于测试"""
    monitor = FileMonitor("C:/Users/<USER>/Desktop/临时记")
    
    try:
        # 处理现有文件
        existing_files = monitor.scan_existing_files()
        for file_path in existing_files:
            monitor.process_file(file_path)
        
        # 开始监控
        monitor.start_monitoring()
        
        print("文件监控服务已启动，按 Ctrl+C 停止...")
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        monitor.stop_monitoring()
        print("文件监控服务已停止")


if __name__ == "__main__":
    main()
