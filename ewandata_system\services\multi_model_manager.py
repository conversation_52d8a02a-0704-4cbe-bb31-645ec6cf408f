"""
多模型管理器
在RTX 4070上智能管理多个AI模型的加载、切换和优化
"""

import asyncio
import logging
import torch
from typing import Dict, List, Any, Optional
from datetime import datetime
import gc
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import psutil

logger = logging.getLogger(__name__)

class ModelInstance:
    """模型实例封装"""
    
    def __init__(self, name: str, model_type: str, config: Dict[str, Any]):
        self.name = name
        self.model_type = model_type
        self.config = config
        self.model = None
        self.tokenizer = None
        self.is_loaded = False
        self.last_used = None
        self.usage_count = 0
        self.memory_usage = 0
    
    async def load(self):
        """加载模型"""
        if self.is_loaded:
            return True
        
        try:
            logger.info(f"加载模型: {self.name}")
            
            # 配置量化
            quantization_config = None
            if self.config.get('quantization') == '4bit':
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            elif self.config.get('quantization') == '8bit':
                quantization_config = BitsAndBytesConfig(
                    load_in_8bit=True
                )
            
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.name,
                trust_remote_code=True
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.name,
                quantization_config=quantization_config,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
            
            self.is_loaded = True
            self.memory_usage = self._get_memory_usage()
            
            logger.info(f"✅ 模型加载成功: {self.name} (显存: {self.memory_usage:.1f}GB)")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败 {self.name}: {e}")
            return False
    
    async def unload(self):
        """卸载模型"""
        if not self.is_loaded:
            return
        
        try:
            logger.info(f"卸载模型: {self.name}")
            
            # 清理模型
            if self.model:
                del self.model
                self.model = None
            
            if self.tokenizer:
                del self.tokenizer
                self.tokenizer = None
            
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # 清理Python内存
            gc.collect()
            
            self.is_loaded = False
            self.memory_usage = 0
            
            logger.info(f"✅ 模型卸载完成: {self.name}")
            
        except Exception as e:
            logger.error(f"❌ 模型卸载失败 {self.name}: {e}")
    
    async def generate_response(self, prompt: str, max_new_tokens: int = 512, 
                              temperature: float = 0.7) -> str:
        """生成响应"""
        if not self.is_loaded:
            raise Exception(f"模型未加载: {self.name}")
        
        try:
            self.last_used = datetime.now()
            self.usage_count += 1
            
            # 构建对话格式（针对不同模型类型）
            if self.model_type == 'code':
                # CodeLlama格式
                formatted_prompt = f"<s>[INST] {prompt} [/INST]"
            elif 'qwen' in self.name.lower():
                # Qwen格式
                messages = [
                    {"role": "system", "content": "你是一个专业的AI助手。"},
                    {"role": "user", "content": prompt}
                ]
                formatted_prompt = self.tokenizer.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )
            else:
                # 通用格式
                formatted_prompt = prompt
            
            # 编码输入
            inputs = self.tokenizer.encode(formatted_prompt, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = inputs.to("cuda")
            
            # 生成响应
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码响应
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 移除原始prompt
            if formatted_prompt in response:
                response = response.replace(formatted_prompt, "").strip()
            
            return response
            
        except Exception as e:
            logger.error(f"生成响应失败 {self.name}: {e}")
            return f"生成失败: {str(e)}"
    
    def _get_memory_usage(self) -> float:
        """获取模型显存使用量"""
        if torch.cuda.is_available() and self.is_loaded:
            return torch.cuda.memory_allocated() / 1024**3
        return 0.0


class MultiModelManager:
    """多模型管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化多模型管理器
        
        Args:
            config: 模型配置
        """
        self.config = config
        self.models: Dict[str, ModelInstance] = {}
        self.primary_model_type = None
        self.max_memory_gb = 11.0  # RTX 4070可用显存限制
        
        self._initialize_models()
        
        logger.info("多模型管理器初始化完成")
    
    def _initialize_models(self):
        """初始化模型实例"""
        # 主模型
        primary_config = self.config['primary_model']
        primary_instance = ModelInstance(
            primary_config['name'],
            primary_config['type'],
            primary_config
        )
        self.models['primary'] = primary_instance
        self.primary_model_type = 'primary'
        
        # 次要模型
        for model_config in self.config['secondary_models']:
            model_instance = ModelInstance(
                model_config['name'],
                model_config['type'],
                model_config
            )
            self.models[model_config['type']] = model_instance
        
        logger.info(f"初始化了 {len(self.models)} 个模型实例")
    
    async def ensure_model_loaded(self, model_type: str) -> bool:
        """确保指定模型已加载"""
        if model_type not in self.models:
            logger.error(f"未知模型类型: {model_type}")
            return False
        
        model_instance = self.models[model_type]
        
        if model_instance.is_loaded:
            return True
        
        # 检查内存是否足够
        current_memory = self._get_total_memory_usage()
        estimated_memory = self._estimate_model_memory(model_instance)
        
        if current_memory + estimated_memory > self.max_memory_gb:
            # 需要释放一些模型
            await self._free_memory_for_model(estimated_memory)
        
        # 加载模型
        return await model_instance.load()
    
    async def _free_memory_for_model(self, required_memory: float):
        """为新模型释放内存"""
        logger.info(f"需要释放 {required_memory:.1f}GB 内存")
        
        # 获取可卸载的模型（非常驻模型，按使用时间排序）
        unloadable_models = []
        for model_type, model_instance in self.models.items():
            if (model_instance.is_loaded and 
                not model_instance.config.get('memory_resident', False)):
                unloadable_models.append((model_type, model_instance))
        
        # 按最后使用时间排序（最久未使用的优先卸载）
        unloadable_models.sort(
            key=lambda x: x[1].last_used or datetime.min
        )
        
        freed_memory = 0
        for model_type, model_instance in unloadable_models:
            if freed_memory >= required_memory:
                break
            
            logger.info(f"卸载模型以释放内存: {model_type}")
            freed_memory += model_instance.memory_usage
            await model_instance.unload()
        
        logger.info(f"释放了 {freed_memory:.1f}GB 内存")
    
    def _estimate_model_memory(self, model_instance: ModelInstance) -> float:
        """估算模型内存需求"""
        # 基于模型名称和量化配置估算
        if '7b' in model_instance.name.lower():
            if model_instance.config.get('quantization') == '4bit':
                return 4.0  # 7B模型4bit量化约4GB
            elif model_instance.config.get('quantization') == '8bit':
                return 7.0  # 7B模型8bit量化约7GB
            else:
                return 14.0  # 7B模型FP16约14GB
        elif '3b' in model_instance.name.lower() or 'mini' in model_instance.name.lower():
            if model_instance.config.get('quantization') == '4bit':
                return 2.0
            elif model_instance.config.get('quantization') == '8bit':
                return 4.0
            else:
                return 8.0
        else:
            return 6.0  # 默认估算
    
    def _get_total_memory_usage(self) -> float:
        """获取当前总内存使用量"""
        total = 0
        for model_instance in self.models.values():
            if model_instance.is_loaded:
                total += model_instance.memory_usage
        return total
    
    def get_model(self, model_type: str) -> Optional[ModelInstance]:
        """获取模型实例"""
        return self.models.get(model_type)
    
    async def optimize_memory(self):
        """内存优化"""
        logger.info("开始内存优化...")
        
        # 卸载长时间未使用的模型
        current_time = datetime.now()
        for model_type, model_instance in self.models.items():
            if (model_instance.is_loaded and 
                not model_instance.config.get('memory_resident', False) and
                model_instance.last_used):
                
                # 如果超过30分钟未使用，卸载模型
                time_since_use = (current_time - model_instance.last_used).total_seconds()
                if time_since_use > 1800:  # 30分钟
                    logger.info(f"卸载长时间未使用的模型: {model_type}")
                    await model_instance.unload()
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        gc.collect()
        
        logger.info("内存优化完成")
    
    async def get_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        status = {
            "total_models": len(self.models),
            "loaded_models": 0,
            "total_memory_usage": 0,
            "models": {}
        }
        
        for model_type, model_instance in self.models.items():
            model_status = {
                "name": model_instance.name,
                "type": model_instance.model_type,
                "is_loaded": model_instance.is_loaded,
                "memory_usage": model_instance.memory_usage,
                "usage_count": model_instance.usage_count,
                "last_used": model_instance.last_used.isoformat() if model_instance.last_used else None
            }
            
            status["models"][model_type] = model_status
            
            if model_instance.is_loaded:
                status["loaded_models"] += 1
                status["total_memory_usage"] += model_instance.memory_usage
        
        return status
    
    async def shutdown(self):
        """关闭所有模型"""
        logger.info("关闭所有模型...")
        
        for model_type, model_instance in self.models.items():
            if model_instance.is_loaded:
                await model_instance.unload()
        
        logger.info("所有模型已关闭")


async def main():
    """测试函数"""
    config = {
        "primary_model": {
            "name": "Qwen/Qwen2-7B-Instruct",
            "type": "general",
            "memory_resident": True,
            "quantization": "4bit"
        },
        "secondary_models": [
            {
                "name": "microsoft/Phi-3-mini-4k-instruct",
                "type": "fast",
                "memory_resident": False,
                "quantization": "8bit"
            }
        ]
    }
    
    manager = MultiModelManager(config)
    
    try:
        # 测试加载主模型
        await manager.ensure_model_loaded('primary')
        
        # 获取状态
        status = await manager.get_status()
        print("模型状态:", status)
        
        # 测试生成
        primary_model = manager.get_model('primary')
        if primary_model:
            response = await primary_model.generate_response("你好，请介绍一下人工智能")
            print("生成响应:", response[:100])
        
    finally:
        await manager.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
