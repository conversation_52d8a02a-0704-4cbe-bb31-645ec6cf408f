"""
成本优化器
智能管理API调用成本，最大化利用免费资源
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class CostOptimizer:
    """成本优化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化成本优化器
        
        Args:
            config: 成本配置
        """
        self.config = config
        self.daily_cost_limit = config.get('cost_threshold', 10.0)  # 每日成本限制（美元）
        
        # 成本跟踪
        self.cost_tracking = {
            "daily_cost": 0.0,
            "monthly_cost": 0.0,
            "last_reset": datetime.now().date(),
            "provider_costs": {},
            "free_quota_usage": {},
            "cost_history": []
        }
        
        # 免费资源配置
        self.free_resources = {
            "chatgpt_web": {
                "daily_limit": 50,
                "cost_per_request": 0.0,
                "priority": 1
            },
            "claude_web": {
                "daily_limit": 30,
                "cost_per_request": 0.0,
                "priority": 2
            },
            "grok_web": {
                "daily_limit": 20,
                "cost_per_request": 0.0,
                "priority": 3
            },
            "deepseek_api": {
                "daily_limit": 1000,
                "cost_per_request": 0.0001,
                "priority": 4
            },
            "groq_api": {
                "daily_limit": 100,
                "cost_per_request": 0.0,
                "priority": 5
            }
        }
        
        # 付费资源配置
        self.paid_resources = {
            "openai_api": {
                "cost_per_1k_tokens": 0.002,
                "priority": 10
            },
            "claude_api": {
                "cost_per_1k_tokens": 0.003,
                "priority": 11
            },
            "gpt4_api": {
                "cost_per_1k_tokens": 0.03,
                "priority": 15
            }
        }
        
        self._load_cost_data()
        
        logger.info("成本优化器初始化完成")
    
    def _load_cost_data(self):
        """加载成本数据"""
        cost_file = Path("data/cost_tracking.json")
        
        if cost_file.exists():
            try:
                with open(cost_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)
                    
                # 检查是否需要重置每日成本
                last_reset = datetime.fromisoformat(saved_data.get('last_reset', datetime.now().isoformat())).date()
                today = datetime.now().date()
                
                if last_reset != today:
                    # 重置每日成本
                    saved_data['daily_cost'] = 0.0
                    saved_data['last_reset'] = today.isoformat()
                    
                    # 重置免费配额使用
                    for resource in saved_data.get('free_quota_usage', {}):
                        saved_data['free_quota_usage'][resource]['daily_usage'] = 0
                
                self.cost_tracking.update(saved_data)
                
            except Exception as e:
                logger.warning(f"成本数据加载失败: {e}")
    
    def _save_cost_data(self):
        """保存成本数据"""
        cost_file = Path("data/cost_tracking.json")
        cost_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # 转换日期为字符串
            save_data = self.cost_tracking.copy()
            save_data['last_reset'] = save_data['last_reset'].isoformat()
            
            with open(cost_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"成本数据保存失败: {e}")
    
    async def can_process(self, provider: str, request: Dict[str, Any]) -> bool:
        """
        检查是否可以处理请求
        
        Args:
            provider: 提供商名称
            request: 请求内容
            
        Returns:
            是否可以处理
        """
        # 检查每日成本限制
        if self.cost_tracking['daily_cost'] >= self.daily_cost_limit:
            logger.warning(f"已达到每日成本限制: ${self.cost_tracking['daily_cost']:.2f}")
            return False
        
        # 检查免费资源配额
        if provider in self.free_resources:
            resource_config = self.free_resources[provider]
            usage_key = provider
            
            if usage_key not in self.cost_tracking['free_quota_usage']:
                self.cost_tracking['free_quota_usage'][usage_key] = {
                    'daily_usage': 0,
                    'total_usage': 0
                }
            
            usage = self.cost_tracking['free_quota_usage'][usage_key]
            
            if usage['daily_usage'] >= resource_config['daily_limit']:
                logger.warning(f"免费资源 {provider} 已达到每日限制")
                return False
        
        # 估算请求成本
        estimated_cost = self._estimate_request_cost(provider, request)
        
        if self.cost_tracking['daily_cost'] + estimated_cost > self.daily_cost_limit:
            logger.warning(f"请求成本过高，将超出每日限制: ${estimated_cost:.4f}")
            return False
        
        return True
    
    def _estimate_request_cost(self, provider: str, request: Dict[str, Any]) -> float:
        """
        估算请求成本
        
        Args:
            provider: 提供商名称
            request: 请求内容
            
        Returns:
            估算成本
        """
        # 免费资源成本为0
        if provider in self.free_resources:
            return self.free_resources[provider]['cost_per_request']
        
        # 付费资源根据token数量计算
        if provider in self.paid_resources:
            content = request.get('content', '')
            estimated_tokens = len(content) // 4 + request.get('max_tokens', 512)  # 粗略估算
            
            cost_per_1k = self.paid_resources[provider]['cost_per_1k_tokens']
            return (estimated_tokens / 1000) * cost_per_1k
        
        # 默认估算
        return 0.01
    
    async def record_usage(self, provider: str, actual_cost: float):
        """
        记录实际使用情况
        
        Args:
            provider: 提供商名称
            actual_cost: 实际成本
        """
        # 更新成本跟踪
        self.cost_tracking['daily_cost'] += actual_cost
        self.cost_tracking['monthly_cost'] += actual_cost
        
        # 更新提供商成本
        if provider not in self.cost_tracking['provider_costs']:
            self.cost_tracking['provider_costs'][provider] = 0.0
        self.cost_tracking['provider_costs'][provider] += actual_cost
        
        # 更新免费资源使用
        if provider in self.free_resources:
            usage_key = provider
            if usage_key not in self.cost_tracking['free_quota_usage']:
                self.cost_tracking['free_quota_usage'][usage_key] = {
                    'daily_usage': 0,
                    'total_usage': 0
                }
            
            usage = self.cost_tracking['free_quota_usage'][usage_key]
            usage['daily_usage'] += 1
            usage['total_usage'] += 1
        
        # 记录成本历史
        self.cost_tracking['cost_history'].append({
            'timestamp': datetime.now().isoformat(),
            'provider': provider,
            'cost': actual_cost
        })
        
        # 保持历史记录在合理范围内
        if len(self.cost_tracking['cost_history']) > 1000:
            self.cost_tracking['cost_history'] = self.cost_tracking['cost_history'][-500:]
        
        # 保存数据
        self._save_cost_data()
        
        logger.info(f"记录使用: {provider} - ${actual_cost:.4f}")
    
    async def can_retry(self) -> bool:
        """
        检查是否可以重试（使用付费资源）
        
        Returns:
            是否可以重试
        """
        # 如果每日成本还有余量，允许重试
        remaining_budget = self.daily_cost_limit - self.cost_tracking['daily_cost']
        return remaining_budget > 0.05  # 至少保留5分钱的余量
    
    def get_optimal_provider(self, task_complexity: float) -> str:
        """
        获取最优提供商
        
        Args:
            task_complexity: 任务复杂度
            
        Returns:
            最优提供商名称
        """
        available_providers = []
        
        # 检查免费资源
        for provider, config in self.free_resources.items():
            usage_key = provider
            usage = self.cost_tracking['free_quota_usage'].get(usage_key, {'daily_usage': 0})
            
            if usage['daily_usage'] < config['daily_limit']:
                available_providers.append({
                    'name': provider,
                    'cost': config['cost_per_request'],
                    'priority': config['priority'],
                    'type': 'free'
                })
        
        # 如果有免费资源可用，优先使用
        if available_providers:
            # 按优先级排序
            available_providers.sort(key=lambda x: x['priority'])
            
            # 根据任务复杂度选择
            if task_complexity > 0.8:
                # 高复杂度任务选择最好的免费资源
                return available_providers[0]['name']
            else:
                # 低复杂度任务可以使用任何免费资源
                return available_providers[0]['name']
        
        # 如果没有免费资源，检查付费资源
        remaining_budget = self.daily_cost_limit - self.cost_tracking['daily_cost']
        
        if remaining_budget > 0.1:  # 至少有10分钱预算
            # 选择性价比最高的付费资源
            if task_complexity > 0.9:
                return "gpt4_api"  # 最复杂任务使用最好的模型
            elif task_complexity > 0.7:
                return "claude_api"  # 中高复杂度使用Claude
            else:
                return "openai_api"  # 一般复杂度使用GPT-3.5
        
        # 如果预算不足，返回最便宜的选项
        return "deepseek_api"
    
    async def get_status(self) -> Dict[str, Any]:
        """获取成本状态"""
        # 计算免费资源使用情况
        free_usage = {}
        for provider, config in self.free_resources.items():
            usage = self.cost_tracking['free_quota_usage'].get(provider, {'daily_usage': 0})
            free_usage[provider] = {
                'daily_usage': usage['daily_usage'],
                'daily_limit': config['daily_limit'],
                'usage_percentage': (usage['daily_usage'] / config['daily_limit']) * 100,
                'remaining': config['daily_limit'] - usage['daily_usage']
            }
        
        # 计算成本效率
        total_requests = sum(usage.get('daily_usage', 0) for usage in self.cost_tracking['free_quota_usage'].values())
        cost_per_request = self.cost_tracking['daily_cost'] / max(total_requests, 1)
        
        return {
            "daily_cost": round(self.cost_tracking['daily_cost'], 4),
            "daily_limit": self.daily_cost_limit,
            "remaining_budget": round(self.daily_cost_limit - self.cost_tracking['daily_cost'], 4),
            "monthly_cost": round(self.cost_tracking['monthly_cost'], 2),
            "cost_per_request": round(cost_per_request, 4),
            "free_resource_usage": free_usage,
            "provider_costs": self.cost_tracking['provider_costs'],
            "total_requests_today": total_requests,
            "cost_efficiency": round((total_requests / max(self.cost_tracking['daily_cost'], 0.01)) * 100, 2)
        }
    
    def generate_cost_report(self) -> Dict[str, Any]:
        """生成成本报告"""
        status = self.get_status()
        
        # 分析成本趋势
        recent_history = self.cost_tracking['cost_history'][-100:]  # 最近100条记录
        
        provider_usage = {}
        for record in recent_history:
            provider = record['provider']
            if provider not in provider_usage:
                provider_usage[provider] = {'count': 0, 'cost': 0.0}
            provider_usage[provider]['count'] += 1
            provider_usage[provider]['cost'] += record['cost']
        
        # 生成建议
        recommendations = []
        
        # 检查免费资源利用率
        for provider, usage in status['free_resource_usage'].items():
            if usage['usage_percentage'] < 50:
                recommendations.append(f"可以更多使用免费资源: {provider} (当前使用率: {usage['usage_percentage']:.1f}%)")
        
        # 检查成本效率
        if status['cost_per_request'] > 0.01:
            recommendations.append("建议优先使用免费资源以降低平均成本")
        
        if status['remaining_budget'] < 2.0:
            recommendations.append("每日预算即将用完，建议优先使用免费资源")
        
        return {
            "status": status,
            "provider_usage": provider_usage,
            "recommendations": recommendations,
            "report_time": datetime.now().isoformat()
        }


async def main():
    """测试函数"""
    config = {
        "cost_threshold": 5.0
    }
    
    optimizer = CostOptimizer(config)
    
    # 测试请求
    test_request = {
        "content": "这是一个测试请求",
        "max_tokens": 100
    }
    
    # 测试不同提供商
    providers = ["chatgpt_web", "deepseek_api", "openai_api"]
    
    for provider in providers:
        can_process = await optimizer.can_process(provider, test_request)
        print(f"{provider}: 可以处理 = {can_process}")
        
        if can_process:
            # 模拟使用
            await optimizer.record_usage(provider, 0.001)
    
    # 获取状态
    status = await optimizer.get_status()
    print("成本状态:", json.dumps(status, ensure_ascii=False, indent=2))
    
    # 生成报告
    report = optimizer.generate_cost_report()
    print("成本报告:", json.dumps(report, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
