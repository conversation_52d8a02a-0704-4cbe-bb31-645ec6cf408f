"""
Qwen2-7B AI服务
专门针对中文文档处理和智能分析优化
"""

import os
import torch
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import time
import re
from datetime import datetime

logger = logging.getLogger(__name__)

class QwenAIService:
    """Qwen2-7B AI服务类"""
    
    def __init__(self, model_name: str = "Qwen/Qwen2-7B-Instruct"):
        """
        初始化Qwen AI服务
        
        Args:
            model_name: Qwen模型名称
        """
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.max_length = 4096  # Qwen2支持更长的上下文
        self.is_initialized = False
        
        logger.info(f"Qwen AI服务初始化，设备: {self.device}")
    
    def initialize_model(self):
        """初始化Qwen模型"""
        if self.is_initialized:
            return True
        
        try:
            logger.info(f"正在加载Qwen模型: {self.model_name}")
            
            from transformers import AutoTokenizer, AutoModelForCausalLM
            
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            self.is_initialized = True
            logger.info("✅ Qwen模型加载成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ Qwen模型加载失败: {e}")
            return False
    
    def generate_response(self, prompt: str, max_new_tokens: int = 512, temperature: float = 0.7) -> str:
        """生成AI响应"""
        if not self.is_initialized:
            if not self.initialize_model():
                return "AI模型未初始化"
        
        try:
            # 构建对话格式
            messages = [
                {"role": "system", "content": "你是一个专业的文档分析助手，擅长中文文档理解、信息提取和智能分析。"},
                {"role": "user", "content": prompt}
            ]
            
            # 应用聊天模板
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            # 编码输入
            model_inputs = self.tokenizer([text], return_tensors="pt").to(self.device)
            
            # 生成响应
            with torch.no_grad():
                generated_ids = self.model.generate(
                    model_inputs.input_ids,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码响应
            generated_ids = [
                output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            
            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            return response.strip()
            
        except Exception as e:
            logger.error(f"Qwen生成失败: {e}")
            return f"生成失败: {str(e)}"
    
    def extract_keywords(self, content: str, max_keywords: int = 10) -> List[str]:
        """智能关键词提取"""
        prompt = f"""请从以下中文文档中提取{max_keywords}个最重要的关键词。
要求：
1. 关键词应该是名词或名词短语
2. 优先选择专业术语和核心概念
3. 避免常见的停用词
4. 用逗号分隔，不要编号

文档内容：
{content[:2000]}

关键词："""
        
        response = self.generate_response(prompt, max_new_tokens=100)
        
        # 解析关键词
        try:
            # 清理响应，移除可能的前缀
            clean_response = re.sub(r'^[关键词：\s]*', '', response)
            keywords = [kw.strip() for kw in clean_response.split(',')]
            keywords = [kw for kw in keywords if kw and len(kw) > 1 and not kw.isdigit()]
            return keywords[:max_keywords]
        except:
            return []
    
    def generate_summary(self, content: str, max_length: int = 200) -> str:
        """智能摘要生成"""
        prompt = f"""请为以下中文文档生成一个简洁准确的摘要。
要求：
1. 摘要长度不超过{max_length}字
2. 突出文档的核心内容和关键信息
3. 使用简洁明了的语言
4. 保持客观中性的语调

文档内容：
{content[:3000]}

摘要："""
        
        response = self.generate_response(prompt, max_new_tokens=256)
        
        # 清理和截断摘要
        summary = response.strip()
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
    
    def classify_content(self, content: str) -> Dict[str, Any]:
        """智能内容分类"""
        prompt = f"""请分析以下中文文档的类型和主题，并以JSON格式返回结果。

文档内容：
{content[:2000]}

请返回包含以下字段的JSON：
{{
    "category": "主要类别（如：技术文档、新闻资讯、学术论文、项目计划、学习笔记等）",
    "topics": ["主要话题1", "主要话题2", "主要话题3"],
    "importance": "重要性评分（1-10）",
    "tags": ["相关标签1", "相关标签2", "相关标签3"],
    "domain": "所属领域（如：人工智能、软件开发、商业管理等）"
}}

JSON："""
        
        response = self.generate_response(prompt, max_new_tokens=300)
        
        try:
            # 提取JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                
                # 验证和清理结果
                if not isinstance(result.get('topics'), list):
                    result['topics'] = []
                if not isinstance(result.get('tags'), list):
                    result['tags'] = []
                if not isinstance(result.get('importance'), (int, float)):
                    result['importance'] = 5
                
                return result
        except:
            pass
        
        # 如果JSON解析失败，返回默认结果
        return {
            "category": "未分类",
            "topics": [],
            "importance": 5,
            "tags": [],
            "domain": "通用"
        }
    
    def predict_information_value(self, content: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """预测信息的未来价值"""
        prompt = f"""请分析以下信息的未来价值和重要性。

信息内容：
{content[:2000]}

请从以下维度进行评估：
1. 时效性：信息的时间敏感度（1-10分）
2. 影响力：可能产生的影响范围（1-10分）
3. 实用性：对用户的实际价值（1-10分）
4. 稀缺性：信息的独特性和稀缺程度（1-10分）
5. 趋势性：是否代表未来发展趋势（1-10分）

请以JSON格式返回评估结果：
{{
    "overall_score": "综合评分（1-10）",
    "timeliness": "时效性评分",
    "influence": "影响力评分", 
    "practicality": "实用性评分",
    "scarcity": "稀缺性评分",
    "trend": "趋势性评分",
    "recommendation": "价值评估说明"
}}

JSON："""
        
        response = self.generate_response(prompt, max_new_tokens=400)
        
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                
                # 确保所有评分都是数字
                for key in ['overall_score', 'timeliness', 'influence', 'practicality', 'scarcity', 'trend']:
                    if key in result:
                        try:
                            result[key] = float(result[key])
                        except:
                            result[key] = 5.0
                
                return result
        except:
            pass
        
        return {
            "overall_score": 5.0,
            "timeliness": 5.0,
            "influence": 5.0,
            "practicality": 5.0,
            "scarcity": 5.0,
            "trend": 5.0,
            "recommendation": "需要进一步分析"
        }
    
    def analyze_information_correlation(self, content_list: List[str]) -> Dict[str, Any]:
        """分析多条信息之间的关联性"""
        if len(content_list) < 2:
            return {"error": "需要至少2条信息进行关联分析"}
        
        # 构建分析内容
        content_summary = ""
        for i, content in enumerate(content_list[:5], 1):  # 最多分析5条
            content_summary += f"信息{i}：{content[:300]}...\n\n"
        
        prompt = f"""请分析以下多条信息之间的关联性和模式。

{content_summary}

请分析：
1. 共同主题和关键词
2. 信息之间的逻辑关系
3. 潜在的发展趋势
4. 综合价值评估

请以JSON格式返回分析结果：
{{
    "common_themes": ["共同主题1", "共同主题2"],
    "relationships": "信息间关系描述",
    "trends": "发现的趋势",
    "synthesis": "综合分析结论",
    "correlation_score": "关联度评分（1-10）"
}}

JSON："""
        
        response = self.generate_response(prompt, max_new_tokens=500)
        
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass
        
        return {
            "common_themes": [],
            "relationships": "分析失败",
            "trends": "无法识别",
            "synthesis": "需要重新分析",
            "correlation_score": 1.0
        }


# 全局Qwen AI服务实例
_qwen_ai_service = None

def get_qwen_ai_service() -> QwenAIService:
    """获取Qwen AI服务实例（单例模式）"""
    global _qwen_ai_service
    if _qwen_ai_service is None:
        _qwen_ai_service = QwenAIService()
    return _qwen_ai_service
