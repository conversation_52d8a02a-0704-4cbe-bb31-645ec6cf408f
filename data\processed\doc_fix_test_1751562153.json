{"id": "doc_fix_test_1751562153", "timestamp": "2025-07-04T01:02:33.345465", "document": {"title": "GitHub同步修复测试", "content": "这是一个用于验证GitHub同步修复的测试文档。\n\n## 修复内容\n1. 重新初始化知识库数据库\n2. 修复.gitignore文件\n3. 清理不必要的文件\n4. 确保数据库正确同步到GitHub\n\n## 系统状态\n- 知识库数据库: 已修复\n- GitHub同步: 准备就绪\n- 文件管理: 已优化\n\n修复时间: 2025-01-03\n", "file_path": "github_sync_fix_test.md", "file_hash": "fix_test_12345", "type": "markdown", "size": 1024, "tags": ["修复", "GitHub", "同步", "测试"], "keywords": ["GitHub", "同步", "修复", "数据库"], "summary": "GitHub同步功能修复验证文档", "importance_score": 0.9}}