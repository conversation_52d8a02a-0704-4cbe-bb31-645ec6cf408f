{"test_results": {"timestamp": "2025-07-04T00:55:12.608640", "base_path": "E:\\Ewandata", "tests": {"github_integration": {"success": false, "timestamp": "2025-07-04T00:55:13.647307", "details": {}, "error": "GitHub集成测试失败: No module named 'schedule'"}, "database_sync": {"success": true, "timestamp": "2025-07-04T00:55:15.560464", "details": {"local_stats": {"total_documents": 3, "by_type": {"markdown": 3}, "total_size_mb": 0.03542900085449219, "recent_documents": 3, "top_tags": {"演示": 1, "知识管理": 1, "AI": 1}}, "markdown_files": 3, "json_files": 3, "test_doc_id": "doc_25c38159_1751561715", "markdown_generated": true, "json_generated": true, "database_verified": true}, "error": null}, "end_to_end_workflow": {"success": false, "timestamp": "2025-07-04T00:55:16.566298", "details": {}, "error": "端到端工作流测试失败: No module named 'schedule'"}}, "errors": [{"test": "github_integration", "error": "GitHub集成测试失败: No module named 'schedule'", "timestamp": "2025-07-04T00:55:13.647323"}, {"test": "end_to_end_workflow", "error": "端到端工作流测试失败: No module named 'schedule'", "timestamp": "2025-07-04T00:55:16.566315"}], "recommendations": []}, "diagnosis": {"git_status": {"is_repo": true, "remotes": "origin\thttps://github.com/EwanCosmos/Ewandata.git (fetch)\norigin\thttps://github.com/EwanCosmos/Ewandata.git (push)\n", "working_tree": " M README.md\n?? .augment/\n?? .gitignore\n?? \"Ewandata\\345\\256\\236\\346\\226\\275\\346\\226\\271\\346\\241\\210.md\"\n?? comprehensive_test_suite.py\n?? data/\n?? demo_system.py\n?? ewandata_env/\n?? ewandata_system/\n?? quick_start.bat\n?? requirements.txt\n?? test_document_processor.py\n?? test_github_sync.py\n?? test_torch.py\n?? \"\\346\\241\\214\\351\\235\\242\\346\\226\\207\\344\\273\\266\\345\\214\\205/\"\n"}, "file_status": {"knowledge_base": {"exists": true, "file_count": 4, "files": ["doc_25c38159_1751561715.md", "doc_482a783c_1751558955.md", "doc_995d32f8_1751560197.md", "doc_demo_has_1751560180.md"]}, "processed": {"exists": true, "file_count": 4, "files": ["doc_25c38159_1751561715.json", "doc_482a783c_1751558955.json", "doc_995d32f8_1751560197.json", "doc_demo_has_1751560180.json"]}, "vectors": {"exists": true, "file_count": 0, "files": []}}, "database_status": {"knowledge_base.db": {"exists": false}, "projects.db": {"exists": true, "size": 278528, "readable": true, "tables": ["projects", "project_relations", "sqlite_sequence", "scan_history"]}}, "issues": ["knowledge_base.db 不存在"], "recommendations": ["重新初始化knowledge_base.db数据库"]}}