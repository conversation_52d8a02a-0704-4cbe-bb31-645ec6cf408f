"""
混合AI管理器
协调本地多模型和外部AI资源的智能调度系统
"""

import asyncio
import logging
import torch
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
from pathlib import Path
import psutil
import gc

logger = logging.getLogger(__name__)

class HybridAIManager:
    """混合AI管理器 - 核心协调组件"""
    
    def __init__(self, config_path: str = None):
        """
        初始化混合AI管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        
        # 初始化各个组件
        self.model_manager = None
        self.task_classifier = None
        self.complexity_evaluator = None
        self.route_decider = None
        self.external_ai_integrator = None
        self.cost_optimizer = None
        
        # 性能监控
        self.performance_stats = {
            "total_requests": 0,
            "local_processed": 0,
            "external_processed": 0,
            "average_response_time": 0,
            "cost_saved": 0
        }
        
        self._initialize_components()
        
        logger.info("混合AI管理器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            "local_models": {
                "primary_model": {
                    "name": "Qwen/Qwen2-7B-Instruct",
                    "type": "general",
                    "memory_resident": True,
                    "quantization": "4bit"
                },
                "secondary_models": [
                    {
                        "name": "codellama/CodeLlama-7b-Instruct-hf",
                        "type": "code",
                        "memory_resident": False,
                        "quantization": "4bit"
                    },
                    {
                        "name": "microsoft/Phi-3-mini-4k-instruct",
                        "type": "fast",
                        "memory_resident": False,
                        "quantization": "8bit"
                    }
                ]
            },
            "external_ai": {
                "enabled": True,
                "complexity_threshold": 0.8,
                "cost_threshold": 10.0,  # 每日成本限制（美元）
                "providers": [
                    {
                        "name": "browser_automation",
                        "type": "free",
                        "priority": 1,
                        "daily_limit": 50
                    },
                    {
                        "name": "n8n_workflow",
                        "type": "api",
                        "priority": 2,
                        "daily_limit": 100
                    }
                ]
            },
            "routing": {
                "task_types": {
                    "document_analysis": "primary",
                    "code_analysis": "code",
                    "quick_response": "fast",
                    "creative_generation": "external",
                    "complex_reasoning": "external"
                },
                "fallback_strategy": "external"
            }
        }
        
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"配置加载失败，使用默认配置: {e}")
        
        return default_config
    
    def _initialize_components(self):
        """初始化各个组件"""
        try:
            # 初始化模型管理器
            from .multi_model_manager import MultiModelManager
            self.model_manager = MultiModelManager(self.config['local_models'])
            
            # 初始化任务分类器
            from .task_classifier import TaskClassifier
            self.task_classifier = TaskClassifier()
            
            # 初始化复杂度评估器
            from .complexity_evaluator import ComplexityEvaluator
            self.complexity_evaluator = ComplexityEvaluator()
            
            # 初始化路由决策器
            from .route_decider import RouteDecider
            self.route_decider = RouteDecider(self.config['routing'])
            
            # 初始化外部AI集成器
            from .external_ai_integrator import ExternalAIIntegrator
            self.external_ai_integrator = ExternalAIIntegrator(self.config['external_ai'])
            
            # 初始化成本优化器
            from .cost_optimizer import CostOptimizer
            self.cost_optimizer = CostOptimizer(self.config['external_ai'])
            
            logger.info("✅ 所有组件初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 组件初始化失败: {e}")
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理AI请求
        
        Args:
            request: 包含任务内容和元数据的请求
            
        Returns:
            处理结果
        """
        start_time = datetime.now()
        self.performance_stats["total_requests"] += 1
        
        try:
            # 1. 任务分类
            task_type = await self.task_classifier.classify(request)
            logger.info(f"任务分类: {task_type}")
            
            # 2. 复杂度评估
            complexity_score = await self.complexity_evaluator.evaluate(request)
            logger.info(f"复杂度评分: {complexity_score:.2f}")
            
            # 3. 路由决策
            route_decision = await self.route_decider.decide(
                task_type, complexity_score, request
            )
            logger.info(f"路由决策: {route_decision}")
            
            # 4. 执行处理
            if route_decision['target'] == 'local':
                result = await self._process_local(request, route_decision)
                self.performance_stats["local_processed"] += 1
            else:
                result = await self._process_external(request, route_decision)
                self.performance_stats["external_processed"] += 1
            
            # 5. 结果后处理
            final_result = await self._post_process_result(result, request)
            
            # 6. 更新性能统计
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            self._update_performance_stats(response_time)
            
            return {
                "success": True,
                "result": final_result,
                "metadata": {
                    "task_type": task_type,
                    "complexity_score": complexity_score,
                    "route_decision": route_decision,
                    "response_time": response_time,
                    "processed_by": route_decision['target']
                }
            }
            
        except Exception as e:
            logger.error(f"请求处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "metadata": {
                    "response_time": (datetime.now() - start_time).total_seconds()
                }
            }
    
    async def _process_local(self, request: Dict[str, Any], 
                           route_decision: Dict[str, Any]) -> Dict[str, Any]:
        """使用本地模型处理"""
        model_type = route_decision.get('model_type', 'primary')
        
        # 确保模型已加载
        await self.model_manager.ensure_model_loaded(model_type)
        
        # 获取模型实例
        model_instance = self.model_manager.get_model(model_type)
        
        if not model_instance:
            raise Exception(f"无法获取模型: {model_type}")
        
        # 执行推理
        result = await model_instance.generate_response(
            request['content'],
            max_new_tokens=request.get('max_tokens', 512),
            temperature=request.get('temperature', 0.7)
        )
        
        return {
            "content": result,
            "source": "local",
            "model": model_type,
            "cost": 0.0
        }
    
    async def _process_external(self, request: Dict[str, Any], 
                              route_decision: Dict[str, Any]) -> Dict[str, Any]:
        """使用外部AI处理"""
        provider = route_decision.get('provider', 'browser_automation')
        
        # 检查成本限制
        if not await self.cost_optimizer.can_process(provider, request):
            # 降级到本地处理
            logger.warning("成本限制，降级到本地处理")
            route_decision['target'] = 'local'
            route_decision['model_type'] = 'primary'
            return await self._process_local(request, route_decision)
        
        # 使用外部AI处理
        result = await self.external_ai_integrator.process(provider, request)
        
        # 更新成本统计
        await self.cost_optimizer.record_usage(provider, result.get('cost', 0))
        
        return result
    
    async def _post_process_result(self, result: Dict[str, Any], 
                                 request: Dict[str, Any]) -> Dict[str, Any]:
        """结果后处理"""
        # 质量评估
        quality_score = await self._assess_quality(result, request)
        
        # 如果质量不佳且是本地处理，考虑重试外部AI
        if (quality_score < 0.6 and 
            result.get('source') == 'local' and 
            await self.cost_optimizer.can_retry()):
            
            logger.info("质量不佳，尝试外部AI重试")
            retry_decision = {
                'target': 'external',
                'provider': 'browser_automation'
            }
            result = await self._process_external(request, retry_decision)
        
        result['quality_score'] = quality_score
        return result
    
    async def _assess_quality(self, result: Dict[str, Any], 
                            request: Dict[str, Any]) -> float:
        """评估结果质量"""
        # 简单的质量评估逻辑
        content = result.get('content', '')
        
        if not content:
            return 0.0
        
        # 基于长度和完整性的简单评估
        min_length = request.get('min_response_length', 50)
        if len(content) < min_length:
            return 0.3
        
        # 检查是否包含错误信息
        error_indicators = ['错误', '失败', '无法', '不能', 'error', 'failed']
        if any(indicator in content.lower() for indicator in error_indicators):
            return 0.4
        
        # 基本质量评分
        return 0.8
    
    def _update_performance_stats(self, response_time: float):
        """更新性能统计"""
        total = self.performance_stats["total_requests"]
        current_avg = self.performance_stats["average_response_time"]
        
        # 计算新的平均响应时间
        new_avg = (current_avg * (total - 1) + response_time) / total
        self.performance_stats["average_response_time"] = new_avg
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        # GPU状态
        gpu_status = {}
        if torch.cuda.is_available():
            gpu_status = {
                "available": True,
                "device_name": torch.cuda.get_device_name(0),
                "memory_allocated": torch.cuda.memory_allocated(0) / 1024**3,
                "memory_reserved": torch.cuda.memory_reserved(0) / 1024**3,
                "memory_total": torch.cuda.get_device_properties(0).total_memory / 1024**3
            }
        
        # 系统资源
        system_status = {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent
        }
        
        # 模型状态
        model_status = {}
        if self.model_manager:
            model_status = await self.model_manager.get_status()
        
        # 成本状态
        cost_status = {}
        if self.cost_optimizer:
            cost_status = await self.cost_optimizer.get_status()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "gpu_status": gpu_status,
            "system_status": system_status,
            "model_status": model_status,
            "cost_status": cost_status,
            "performance_stats": self.performance_stats
        }
    
    async def optimize_system(self):
        """系统优化"""
        logger.info("开始系统优化...")
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 清理Python内存
        gc.collect()
        
        # 优化模型加载
        if self.model_manager:
            await self.model_manager.optimize_memory()
        
        logger.info("系统优化完成")
    
    async def shutdown(self):
        """关闭系统"""
        logger.info("关闭混合AI系统...")
        
        if self.model_manager:
            await self.model_manager.shutdown()
        
        if self.external_ai_integrator:
            await self.external_ai_integrator.shutdown()
        
        logger.info("混合AI系统已关闭")


# 全局实例
_hybrid_ai_manager = None

def get_hybrid_ai_manager() -> HybridAIManager:
    """获取混合AI管理器实例"""
    global _hybrid_ai_manager
    if _hybrid_ai_manager is None:
        _hybrid_ai_manager = HybridAIManager()
    return _hybrid_ai_manager
