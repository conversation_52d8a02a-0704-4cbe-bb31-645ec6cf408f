"""
智能分析和价值预测模块
基于Qwen2-7B进行深度信息分析
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import numpy as np
from pathlib import Path
import asyncio

logger = logging.getLogger(__name__)

class IntelligentAnalyzer:
    """智能分析器"""
    
    def __init__(self):
        """初始化智能分析器"""
        self.ai_service = None
        self.analysis_history = []
        self.value_trends = {}
        
        # 初始化AI服务
        try:
            from .qwen_ai_service import get_qwen_ai_service
            self.ai_service = get_qwen_ai_service()
            logger.info("✅ 智能分析器初始化成功")
        except ImportError:
            logger.error("❌ 无法导入Qwen AI服务")
    
    async def analyze_information_batch(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量分析信息"""
        if not self.ai_service:
            logger.error("AI服务未初始化")
            return items
        
        logger.info(f"开始批量分析 {len(items)} 条信息...")
        
        analyzed_items = []
        
        for i, item in enumerate(items):
            try:
                logger.info(f"分析进度: {i+1}/{len(items)} - {item.get('title', '')[:50]}...")
                
                # 深度分析单条信息
                analysis_result = await self.analyze_single_item(item)
                
                # 合并分析结果
                enhanced_item = {**item, **analysis_result}
                analyzed_items.append(enhanced_item)
                
                # 添加延迟避免过载
                if i % 5 == 0 and i > 0:
                    await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"分析失败 {item.get('title', '')}: {e}")
                analyzed_items.append(item)
        
        logger.info(f"批量分析完成: {len(analyzed_items)} 条")
        return analyzed_items
    
    async def analyze_single_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """深度分析单条信息"""
        content = self._extract_content(item)
        
        if not content:
            return {"analysis_error": "内容为空"}
        
        # 并发执行多个分析任务
        tasks = [
            self._extract_keywords_async(content),
            self._generate_summary_async(content),
            self._classify_content_async(content),
            self._predict_value_async(content, item),
            self._analyze_sentiment_async(content),
            self._extract_entities_async(content)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整合分析结果
        analysis_result = {
            "ai_keywords": results[0] if not isinstance(results[0], Exception) else [],
            "ai_summary": results[1] if not isinstance(results[1], Exception) else "",
            "ai_classification": results[2] if not isinstance(results[2], Exception) else {},
            "value_prediction": results[3] if not isinstance(results[3], Exception) else {},
            "sentiment_analysis": results[4] if not isinstance(results[4], Exception) else {},
            "named_entities": results[5] if not isinstance(results[5], Exception) else [],
            "analysis_timestamp": datetime.now().isoformat(),
            "ai_enhanced": True
        }
        
        return analysis_result
    
    def _extract_content(self, item: Dict[str, Any]) -> str:
        """提取项目的文本内容"""
        content_parts = []
        
        if item.get('title'):
            content_parts.append(item['title'])
        
        if item.get('summary'):
            content_parts.append(item['summary'])
        
        if item.get('description'):
            content_parts.append(item['description'])
        
        return ' '.join(content_parts)
    
    async def _extract_keywords_async(self, content: str) -> List[str]:
        """异步关键词提取"""
        try:
            return self.ai_service.extract_keywords(content, max_keywords=8)
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
    
    async def _generate_summary_async(self, content: str) -> str:
        """异步摘要生成"""
        try:
            return self.ai_service.generate_summary(content, max_length=150)
        except Exception as e:
            logger.error(f"摘要生成失败: {e}")
            return ""
    
    async def _classify_content_async(self, content: str) -> Dict[str, Any]:
        """异步内容分类"""
        try:
            return self.ai_service.classify_content(content)
        except Exception as e:
            logger.error(f"内容分类失败: {e}")
            return {}
    
    async def _predict_value_async(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """异步价值预测"""
        try:
            return self.ai_service.predict_information_value(content, metadata)
        except Exception as e:
            logger.error(f"价值预测失败: {e}")
            return {}
    
    async def _analyze_sentiment_async(self, content: str) -> Dict[str, Any]:
        """异步情感分析"""
        try:
            prompt = f"""请分析以下文本的情感倾向和语调。

文本内容：
{content[:1000]}

请以JSON格式返回分析结果：
{{
    "sentiment": "情感倾向（正面/中性/负面）",
    "confidence": "置信度（0-1）",
    "emotion": "主要情绪（如：兴奋、担忧、平静等）",
    "tone": "语调（如：正式、轻松、严肃等）"
}}

JSON："""
            
            response = self.ai_service.generate_response(prompt, max_new_tokens=200)
            
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            return {"sentiment": "中性", "confidence": 0.5, "emotion": "平静", "tone": "中性"}
            
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return {}
    
    async def _extract_entities_async(self, content: str) -> List[Dict[str, Any]]:
        """异步实体提取"""
        try:
            prompt = f"""请从以下文本中提取重要的命名实体。

文本内容：
{content[:1500]}

请识别以下类型的实体：
- 人名
- 公司/组织名
- 地名
- 产品名
- 技术名词
- 时间

请以JSON格式返回：
{{
    "entities": [
        {{"text": "实体文本", "type": "实体类型", "confidence": "置信度"}},
        ...
    ]
}}

JSON："""
            
            response = self.ai_service.generate_response(prompt, max_new_tokens=300)
            
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                return result.get('entities', [])
            
            return []
            
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return []
    
    def analyze_trends(self, analyzed_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析信息趋势"""
        try:
            # 统计分析
            total_items = len(analyzed_items)
            
            # 来源分布
            source_distribution = {}
            for item in analyzed_items:
                source = item.get('source', '未知')
                source_distribution[source] = source_distribution.get(source, 0) + 1
            
            # 类别分布
            category_distribution = {}
            for item in analyzed_items:
                classification = item.get('ai_classification', {})
                category = classification.get('category', '未分类')
                category_distribution[category] = category_distribution.get(category, 0) + 1
            
            # 价值分布
            value_scores = []
            for item in analyzed_items:
                value_pred = item.get('value_prediction', {})
                score = value_pred.get('overall_score', 5.0)
                if isinstance(score, (int, float)):
                    value_scores.append(score)
            
            avg_value_score = np.mean(value_scores) if value_scores else 5.0
            
            # 热门关键词
            all_keywords = []
            for item in analyzed_items:
                keywords = item.get('ai_keywords', [])
                all_keywords.extend(keywords)
            
            keyword_freq = {}
            for keyword in all_keywords:
                keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
            
            top_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:10]
            
            # 情感分布
            sentiment_distribution = {"正面": 0, "中性": 0, "负面": 0}
            for item in analyzed_items:
                sentiment = item.get('sentiment_analysis', {}).get('sentiment', '中性')
                if sentiment in sentiment_distribution:
                    sentiment_distribution[sentiment] += 1
            
            trend_analysis = {
                "analysis_time": datetime.now().isoformat(),
                "total_items": total_items,
                "source_distribution": source_distribution,
                "category_distribution": category_distribution,
                "average_value_score": round(avg_value_score, 2),
                "top_keywords": top_keywords,
                "sentiment_distribution": sentiment_distribution,
                "high_value_items": len([item for item in analyzed_items 
                                       if item.get('value_prediction', {}).get('overall_score', 0) >= 7]),
                "recommendations": self._generate_recommendations(analyzed_items)
            }
            
            return trend_analysis
            
        except Exception as e:
            logger.error(f"趋势分析失败: {e}")
            return {"error": str(e)}
    
    def _generate_recommendations(self, analyzed_items: List[Dict[str, Any]]) -> List[str]:
        """生成推荐建议"""
        recommendations = []
        
        # 高价值信息推荐
        high_value_items = [item for item in analyzed_items 
                           if item.get('value_prediction', {}).get('overall_score', 0) >= 8]
        
        if high_value_items:
            recommendations.append(f"发现 {len(high_value_items)} 条高价值信息，建议优先关注")
        
        # 趋势性信息推荐
        trend_items = [item for item in analyzed_items 
                      if item.get('value_prediction', {}).get('trend', 0) >= 7]
        
        if trend_items:
            recommendations.append(f"发现 {len(trend_items)} 条趋势性信息，可能代表未来发展方向")
        
        # 技术类信息推荐
        tech_items = [item for item in analyzed_items 
                     if item.get('ai_classification', {}).get('domain', '') in ['人工智能', '软件开发', '科技']]
        
        if tech_items:
            recommendations.append(f"技术类信息占比 {len(tech_items)/len(analyzed_items)*100:.1f}%，建议关注技术发展动态")
        
        return recommendations
    
    def save_analysis_results(self, analyzed_items: List[Dict[str, Any]], 
                            trend_analysis: Dict[str, Any], 
                            output_path: str = None):
        """保存分析结果"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"data/analysis_results_{timestamp}.json"
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        results = {
            "analysis_metadata": {
                "timestamp": datetime.now().isoformat(),
                "total_items": len(analyzed_items),
                "analyzer_version": "1.0.0"
            },
            "analyzed_items": analyzed_items,
            "trend_analysis": trend_analysis
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"分析结果已保存: {output_file}")


async def main():
    """主函数 - 用于测试"""
    analyzer = IntelligentAnalyzer()
    
    # 测试数据
    test_items = [
        {
            "source": "测试",
            "title": "人工智能在医疗领域的最新突破",
            "summary": "研究人员开发了一种新的AI算法，能够更准确地诊断癌症，准确率达到95%以上。",
            "collected_time": datetime.now().isoformat()
        },
        {
            "source": "测试",
            "title": "量子计算技术获得重大进展",
            "summary": "科学家成功构建了100量子比特的量子计算机，为未来的量子优势奠定了基础。",
            "collected_time": datetime.now().isoformat()
        }
    ]
    
    try:
        # 批量分析
        analyzed_items = await analyzer.analyze_information_batch(test_items)
        
        # 趋势分析
        trend_analysis = analyzer.analyze_trends(analyzed_items)
        
        # 保存结果
        analyzer.save_analysis_results(analyzed_items, trend_analysis)
        
        print("智能分析测试完成")
        print(f"分析了 {len(analyzed_items)} 条信息")
        print(f"平均价值评分: {trend_analysis.get('average_value_score', 0)}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
