"""
简化的AI功能测试
使用已安装的transformers库测试基础AI能力
"""

import sys
import os
from pathlib import Path

# 添加系统路径
sys.path.append('ewandata_system')

def print_header(title):
    print(f"\n{'='*60}")
    print(f"  {title}")
    print('='*60)

def test_transformers_installation():
    """测试transformers库安装"""
    print_header("📦 测试依赖库安装")
    
    try:
        import transformers
        import torch
        print(f"✅ transformers版本: {transformers.__version__}")
        print(f"✅ torch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"✅ GPU设备: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️ 使用CPU模式")
        
        return True
    except ImportError as e:
        print(f"❌ 依赖库导入失败: {e}")
        return False

def test_simple_ai_model():
    """测试简单的AI模型"""
    print_header("🧠 测试简单AI模型")
    
    try:
        from transformers import pipeline
        
        print("正在加载文本生成模型...")
        
        # 使用更轻量的模型进行测试
        try:
            # 尝试使用GPT-2作为测试模型
            generator = pipeline('text-generation', model='gpt2', max_length=100)
            print("✅ GPT-2模型加载成功")
            
            # 测试文本生成
            test_prompt = "Artificial intelligence is"
            result = generator(test_prompt, max_length=50, num_return_sequences=1)
            
            print(f"测试输入: {test_prompt}")
            print(f"生成结果: {result[0]['generated_text']}")
            
            return True
            
        except Exception as e:
            print(f"⚠️ GPT-2模型测试失败: {e}")
            
            # 尝试使用更简单的分类模型
            try:
                classifier = pipeline('sentiment-analysis')
                print("✅ 情感分析模型加载成功")
                
                test_text = "This is a great AI system for document processing."
                result = classifier(test_text)
                
                print(f"测试文本: {test_text}")
                print(f"分析结果: {result}")
                
                return True
                
            except Exception as e2:
                print(f"❌ 简单模型测试也失败: {e2}")
                return False
    
    except ImportError as e:
        print(f"❌ transformers导入失败: {e}")
        return False

def create_simple_ai_service():
    """创建简化的AI服务"""
    print_header("🔧 创建简化AI服务")
    
    simple_ai_code = '''"""
简化的AI服务
使用基础的transformers功能
"""

import logging
from typing import List, Dict, Any
import re

logger = logging.getLogger(__name__)

class SimpleAIService:
    """简化的AI服务类"""
    
    def __init__(self):
        self.is_initialized = False
        self.model = None
        
    def initialize_model(self):
        """初始化简单模型"""
        try:
            from transformers import pipeline
            
            # 使用情感分析作为基础功能
            self.model = pipeline('sentiment-analysis')
            self.is_initialized = True
            logger.info("✅ 简化AI模型初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 简化AI模型初始化失败: {e}")
            return False
    
    def extract_keywords(self, content: str, max_keywords: int = 10) -> List[str]:
        """简单的关键词提取（基于规则）"""
        if not content:
            return []
        
        # 简单的关键词提取逻辑
        import re
        
        # 移除标点符号，转换为小写
        clean_content = re.sub(r'[^\\w\\s]', ' ', content.lower())
        words = clean_content.split()
        
        # 过滤停用词
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '着', '过',
                     'the', 'is', 'at', 'which', 'on', 'and', 'or', 'but', 'in', 'with'}
        
        # 统计词频
        word_freq = {}
        for word in words:
            if len(word) > 2 and word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in keywords[:max_keywords]]
    
    def generate_summary(self, content: str, max_length: int = 200) -> str:
        """简单的摘要生成（提取前几句）"""
        if not content:
            return ""
        
        # 简单的摘要：取前两句话
        sentences = re.split(r'[.!?。！？]', content)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        summary = '. '.join(sentences[:2])
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
    
    def classify_content(self, content: str) -> Dict[str, Any]:
        """简单的内容分类"""
        if not content:
            return {"category": "空文档", "topics": [], "importance": 1, "tags": []}
        
        # 基于关键词的简单分类
        content_lower = content.lower()
        
        category = "其他"
        topics = []
        tags = []
        
        # 技术文档
        if any(word in content_lower for word in ['python', 'java', 'javascript', 'api', '代码', '算法']):
            category = "技术文档"
            topics.append("编程")
            tags.append("技术")
        
        # 项目管理
        elif any(word in content_lower for word in ['项目', '计划', '任务', '目标', '进度']):
            category = "项目管理"
            topics.append("管理")
            tags.append("项目")
        
        # 学习笔记
        elif any(word in content_lower for word in ['学习', '笔记', '总结', '知识', '概念']):
            category = "学习笔记"
            topics.append("学习")
            tags.append("笔记")
        
        # AI相关
        if any(word in content_lower for word in ['ai', '人工智能', '机器学习', '深度学习', 'ml', 'dl']):
            topics.append("人工智能")
            tags.append("AI")
        
        # 计算重要性（基于长度和关键词密度）
        importance = min(10, max(1, len(content) // 100 + len(topics)))
        
        return {
            "category": category,
            "topics": topics,
            "importance": importance,
            "tags": tags
        }
    
    def enhance_document_data(self, content: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """增强文档数据"""
        if not content or len(content.strip()) < 10:
            return {
                "keywords": [],
                "summary": "",
                "classification": {"category": "空文档", "topics": [], "importance": 1, "tags": []},
                "ai_enhanced": False
            }
        
        try:
            # 提取关键词
            keywords = self.extract_keywords(content)
            
            # 生成摘要
            summary = self.generate_summary(content)
            
            # 内容分类
            classification = self.classify_content(content)
            
            return {
                "keywords": keywords,
                "summary": summary,
                "classification": classification,
                "ai_enhanced": True,
                "ai_type": "simple_rule_based"
            }
            
        except Exception as e:
            logger.error(f"简化AI处理失败: {e}")
            return {
                "keywords": [],
                "summary": f"处理失败: {str(e)}",
                "classification": {"category": "处理失败", "topics": [], "importance": 1, "tags": []},
                "ai_enhanced": False
            }


# 全局实例
_simple_ai_service = None

def get_simple_ai_service() -> SimpleAIService:
    """获取简化AI服务实例"""
    global _simple_ai_service
    if _simple_ai_service is None:
        _simple_ai_service = SimpleAIService()
    return _simple_ai_service
'''
    
    # 保存简化AI服务
    simple_ai_file = Path("ewandata_system/services/simple_ai_service.py")
    with open(simple_ai_file, 'w', encoding='utf-8') as f:
        f.write(simple_ai_code)
    
    print(f"✅ 简化AI服务已创建: {simple_ai_file}")
    return True

def test_simple_ai_service():
    """测试简化AI服务"""
    print_header("🧪 测试简化AI服务")
    
    try:
        from services.simple_ai_service import get_simple_ai_service
        
        ai_service = get_simple_ai_service()
        
        # 测试文本
        test_content = """
        Ewandata智能知识管理系统开发文档
        
        本项目使用Python开发，采用FastAPI框架构建Web API。
        系统集成了机器学习算法，提供智能文档分析功能。
        
        主要技术栈包括：
        - Python 3.9+
        - FastAPI Web框架  
        - SQLite数据库
        - 机器学习算法
        
        项目目标是构建一个智能化的个人知识管理系统。
        """
        
        print("测试内容:", test_content[:100] + "...")
        
        # 测试关键词提取
        keywords = ai_service.extract_keywords(test_content)
        print(f"✅ 关键词提取: {keywords}")
        
        # 测试摘要生成
        summary = ai_service.generate_summary(test_content)
        print(f"✅ 摘要生成: {summary}")
        
        # 测试内容分类
        classification = ai_service.classify_content(test_content)
        print(f"✅ 内容分类: {classification}")
        
        # 测试完整增强
        enhanced_data = ai_service.enhance_document_data(test_content, {})
        print(f"✅ 完整增强: AI处理={enhanced_data.get('ai_enhanced', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化AI服务测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print_header("🚀 简化AI功能测试")
    
    print("由于复杂AI模型安装遇到问题，我们先测试基础功能")
    
    tests = [
        ("依赖库安装", test_transformers_installation),
        ("简单AI模型", test_simple_ai_model),
        ("创建简化AI服务", create_simple_ai_service),
        ("测试简化AI服务", test_simple_ai_service)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n⏳ 测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
            status = "✅ 通过" if success else "❌ 失败"
            print(f"结果: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ 异常: {str(e)}")
    
    # 显示结果
    print_header("📋 测试结果")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    
    print(f"总测试: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}")
    
    if passed_tests >= 3:
        print(f"\n🎉 基础AI功能可用！")
        print("✅ 可以使用简化的AI服务进行文档处理")
        print("✅ 系统具备基本的智能化能力")
    else:
        print(f"\n⚠️ 需要进一步配置AI功能")

if __name__ == "__main__":
    main()
