"""
深度诊断Git配置和GitHub连接问题
"""

import os
import subprocess
import json
from pathlib import Path
import urllib.parse

def print_section(title):
    print(f"\n{'='*60}")
    print(f"  {title}")
    print('='*60)

def run_git_command(cmd, capture_output=True, timeout=30):
    """安全执行Git命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=capture_output, 
                              text=True, timeout=timeout, cwd="E:/Ewandata")
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)

def check_git_config():
    """检查Git配置"""
    print_section("🔧 Git配置检查")
    
    configs = [
        ("用户名", "git config user.name"),
        ("邮箱", "git config user.email"),
        ("远程仓库", "git remote -v"),
        ("当前分支", "git branch --show-current"),
        ("远程分支", "git branch -r"),
        ("Git版本", "git --version")
    ]
    
    for name, cmd in configs:
        success, stdout, stderr = run_git_command(cmd)
        if success:
            print(f"✅ {name}: {stdout}")
        else:
            print(f"❌ {name}: {stderr}")

def check_remote_url():
    """检查远程仓库URL"""
    print_section("🌐 远程仓库URL分析")
    
    success, stdout, stderr = run_git_command("git remote get-url origin")
    if success:
        url = stdout.strip()
        print(f"原始URL: {url}")
        
        # 解析URL
        if url.startswith("https://"):
            parsed = urllib.parse.urlparse(url)
            print(f"协议: {parsed.scheme}")
            print(f"主机: {parsed.netloc}")
            print(f"路径: {parsed.path}")
            
            # 检查URL格式
            if "github.com" in parsed.netloc:
                print("✅ GitHub URL格式正确")
                return url
            else:
                print("❌ 不是GitHub URL")
        elif url.startswith("git@"):
            print("📝 使用SSH协议")
            return url
        else:
            print("❌ 未知URL格式")
    else:
        print(f"❌ 获取远程URL失败: {stderr}")
    
    return None

def test_github_connectivity():
    """测试GitHub连接性"""
    print_section("🔗 GitHub连接测试")
    
    # 测试基本连接
    print("1. 测试GitHub基本连接...")
    success, stdout, stderr = run_git_command("curl -I https://github.com", timeout=10)
    if success:
        print("✅ GitHub基本连接正常")
    else:
        print(f"❌ GitHub连接失败: {stderr}")
    
    # 测试Git协议连接
    print("\n2. 测试Git协议连接...")
    success, stdout, stderr = run_git_command("git ls-remote --heads origin", timeout=30)
    if success:
        print("✅ Git协议连接正常")
        print(f"远程分支: {stdout}")
        return True
    else:
        print(f"❌ Git协议连接失败: {stderr}")
        return False

def check_authentication():
    """检查认证配置"""
    print_section("🔐 认证配置检查")
    
    # 检查凭据存储
    success, stdout, stderr = run_git_command("git config credential.helper")
    if success and stdout:
        print(f"✅ 凭据助手: {stdout}")
    else:
        print("⚠️ 未配置凭据助手")
    
    # 检查是否有存储的凭据
    print("\n检查Windows凭据管理器...")
    success, stdout, stderr = run_git_command('cmdkey /list | findstr github.com')
    if success and stdout:
        print("✅ 找到GitHub凭据")
    else:
        print("⚠️ 未找到GitHub凭据")

def check_repository_status():
    """检查仓库状态"""
    print_section("📊 仓库状态检查")
    
    # 检查工作区状态
    success, stdout, stderr = run_git_command("git status --porcelain")
    if success:
        if stdout:
            lines = stdout.split('\n')
            print(f"📝 工作区变更: {len(lines)} 个文件")
            for line in lines[:5]:  # 只显示前5个
                print(f"   {line}")
            if len(lines) > 5:
                print(f"   ... 还有 {len(lines) - 5} 个文件")
        else:
            print("✅ 工作区干净")
    
    # 检查提交历史
    success, stdout, stderr = run_git_command("git log --oneline -5")
    if success:
        print(f"\n📚 最近提交:")
        for line in stdout.split('\n'):
            if line:
                print(f"   {line}")
    
    # 检查远程跟踪
    success, stdout, stderr = run_git_command("git status -b")
    if success:
        print(f"\n🔄 分支状态:")
        for line in stdout.split('\n')[:3]:  # 前3行通常包含分支信息
            if line:
                print(f"   {line}")

def test_push_with_verbose():
    """详细模式测试推送"""
    print_section("🚀 详细推送测试")
    
    print("尝试详细模式推送...")
    success, stdout, stderr = run_git_command("git push -v origin main", timeout=60)
    
    print(f"返回码: {'成功' if success else '失败'}")
    if stdout:
        print(f"标准输出:\n{stdout}")
    if stderr:
        print(f"错误输出:\n{stderr}")
    
    return success

def try_alternative_push_methods():
    """尝试替代推送方法"""
    print_section("🔄 替代推送方法")
    
    methods = [
        ("强制推送", "git push --force-with-lease origin main"),
        ("设置上游", "git push --set-upstream origin main"),
        ("推送所有分支", "git push --all origin"),
    ]
    
    for name, cmd in methods:
        print(f"\n尝试: {name}")
        success, stdout, stderr = run_git_command(cmd, timeout=60)
        
        if success:
            print(f"✅ {name} 成功!")
            if stdout:
                print(f"输出: {stdout}")
            return True
        else:
            print(f"❌ {name} 失败: {stderr}")
    
    return False

def suggest_fixes():
    """提供修复建议"""
    print_section("🔧 修复建议")
    
    print("基于诊断结果，建议尝试以下修复方法:")
    print()
    print("1. 重新配置远程仓库:")
    print("   git remote remove origin")
    print("   git remote add origin https://github.com/EwanCosmos/Ewandata.git")
    print()
    print("2. 清除并重新设置凭据:")
    print("   git config --global credential.helper manager-core")
    print("   git push origin main  # 会提示重新输入凭据")
    print()
    print("3. 使用SSH方式 (如果有SSH密钥):")
    print("   git remote set-<NAME_EMAIL>:EwanCosmos/Ewandata.git")
    print()
    print("4. 检查代理设置:")
    print("   git config --global --unset http.proxy")
    print("   git config --global --unset https.proxy")
    print()
    print("5. 重置本地分支到远程状态:")
    print("   git fetch origin")
    print("   git reset --hard origin/main")
    print("   git push origin main")

def main():
    """主诊断函数"""
    print("🔍 Ewandata Git推送问题深度诊断")
    print("=" * 60)
    
    os.chdir("E:/Ewandata")
    
    # 1. 基础配置检查
    check_git_config()
    
    # 2. 远程URL检查
    remote_url = check_remote_url()
    
    # 3. GitHub连接测试
    github_connected = test_github_connectivity()
    
    # 4. 认证检查
    check_authentication()
    
    # 5. 仓库状态检查
    check_repository_status()
    
    # 6. 详细推送测试
    push_success = test_push_with_verbose()
    
    # 7. 如果推送失败，尝试替代方法
    if not push_success:
        print("\n主推送失败，尝试替代方法...")
        alt_success = try_alternative_push_methods()
        
        if not alt_success:
            # 8. 提供修复建议
            suggest_fixes()
    else:
        print("\n🎉 推送成功!")
    
    # 生成诊断报告
    print_section("📋 诊断报告")
    print(f"远程URL配置: {'✅' if remote_url else '❌'}")
    print(f"GitHub连接: {'✅' if github_connected else '❌'}")
    print(f"推送测试: {'✅' if push_success else '❌'}")

if __name__ == "__main__":
    main()
