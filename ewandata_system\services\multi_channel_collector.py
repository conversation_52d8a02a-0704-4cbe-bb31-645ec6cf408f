"""
多渠道信息采集系统
自动从多个信息源获取热点资讯
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import re
from pathlib import Path
import feedparser
import time

logger = logging.getLogger(__name__)

class MultiChannelCollector:
    """多渠道信息采集器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化多渠道采集器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.session = None
        self.collected_data = []
        
        # API配置
        self.api_keys = self.config.get('api_keys', {})
        self.rate_limits = self.config.get('rate_limits', {})
        
        logger.info("多渠道信息采集器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "channels": {
                "tencent_news": {
                    "enabled": True,
                    "url": "https://news.qq.com/",
                    "rss_url": "https://news.qq.com/newsgn/rss_newsgn.xml",
                    "max_items": 20
                },
                "feishu_community": {
                    "enabled": True,
                    "url": "https://www.feishu.cn/community",
                    "max_items": 15
                },
                "youtube": {
                    "enabled": False,  # 需要API key
                    "api_key": "",
                    "channels": ["UC_x5XG1OV2P6uZZ5FSM9Ttw"],  # 示例频道
                    "max_items": 10
                },
                "twitter": {
                    "enabled": False,  # 需要API key
                    "api_key": "",
                    "keywords": ["AI", "人工智能", "科技"],
                    "max_items": 15
                }
            },
            "rate_limits": {
                "requests_per_minute": 30,
                "delay_between_requests": 2
            },
            "filters": {
                "min_content_length": 50,
                "exclude_keywords": ["广告", "推广"],
                "include_keywords": ["AI", "人工智能", "科技", "创新"]
            }
        }
        
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config
    
    async def start_session(self):
        """启动HTTP会话"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def collect_tencent_news(self) -> List[Dict[str, Any]]:
        """采集腾讯新闻"""
        try:
            config = self.config['channels']['tencent_news']
            if not config['enabled']:
                return []
            
            logger.info("开始采集腾讯新闻...")
            
            # 使用RSS源获取新闻
            feed = feedparser.parse(config['rss_url'])
            news_items = []
            
            for entry in feed.entries[:config['max_items']]:
                item = {
                    "source": "腾讯新闻",
                    "title": entry.title,
                    "link": entry.link,
                    "summary": getattr(entry, 'summary', ''),
                    "published": getattr(entry, 'published', ''),
                    "collected_time": datetime.now().isoformat(),
                    "content_type": "news"
                }
                
                # 应用过滤器
                if self._apply_filters(item):
                    news_items.append(item)
            
            logger.info(f"腾讯新闻采集完成: {len(news_items)}条")
            return news_items
            
        except Exception as e:
            logger.error(f"腾讯新闻采集失败: {e}")
            return []
    
    async def collect_feishu_community(self) -> List[Dict[str, Any]]:
        """采集飞书社区信息"""
        try:
            config = self.config['channels']['feishu_community']
            if not config['enabled']:
                return []
            
            logger.info("开始采集飞书社区...")
            
            # 注意：这里需要根据飞书社区的实际API或网页结构调整
            # 目前提供一个基础框架
            
            await self.start_session()
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            async with self.session.get(config['url'], headers=headers) as response:
                if response.status == 200:
                    html_content = await response.text()
                    
                    # 这里需要根据实际页面结构解析
                    # 示例：提取标题和链接
                    items = []
                    
                    # 简单的正则表达式示例（需要根据实际情况调整）
                    title_pattern = r'<h[1-6][^>]*>(.*?)</h[1-6]>'
                    titles = re.findall(title_pattern, html_content, re.IGNORECASE)
                    
                    for i, title in enumerate(titles[:config['max_items']]):
                        item = {
                            "source": "飞书社区",
                            "title": title.strip(),
                            "link": config['url'],
                            "summary": "",
                            "collected_time": datetime.now().isoformat(),
                            "content_type": "community"
                        }
                        
                        if self._apply_filters(item):
                            items.append(item)
                    
                    logger.info(f"飞书社区采集完成: {len(items)}条")
                    return items
            
            return []
            
        except Exception as e:
            logger.error(f"飞书社区采集失败: {e}")
            return []
    
    async def collect_youtube_data(self) -> List[Dict[str, Any]]:
        """采集YouTube数据"""
        try:
            config = self.config['channels']['youtube']
            if not config['enabled'] or not config.get('api_key'):
                logger.info("YouTube采集未启用或缺少API密钥")
                return []
            
            logger.info("开始采集YouTube数据...")
            
            # YouTube Data API v3调用示例
            api_key = config['api_key']
            items = []
            
            for channel_id in config['channels']:
                url = f"https://www.googleapis.com/youtube/v3/search"
                params = {
                    'part': 'snippet',
                    'channelId': channel_id,
                    'maxResults': config['max_items'],
                    'order': 'date',
                    'type': 'video',
                    'key': api_key
                }
                
                await self.start_session()
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for video in data.get('items', []):
                            snippet = video['snippet']
                            item = {
                                "source": "YouTube",
                                "title": snippet['title'],
                                "link": f"https://www.youtube.com/watch?v={video['id']['videoId']}",
                                "summary": snippet['description'][:200],
                                "published": snippet['publishedAt'],
                                "collected_time": datetime.now().isoformat(),
                                "content_type": "video",
                                "channel": snippet['channelTitle']
                            }
                            
                            if self._apply_filters(item):
                                items.append(item)
                
                # 添加延迟避免API限制
                await asyncio.sleep(1)
            
            logger.info(f"YouTube采集完成: {len(items)}条")
            return items
            
        except Exception as e:
            logger.error(f"YouTube采集失败: {e}")
            return []
    
    async def collect_twitter_data(self) -> List[Dict[str, Any]]:
        """采集Twitter数据"""
        try:
            config = self.config['channels']['twitter']
            if not config['enabled'] or not config.get('api_key'):
                logger.info("Twitter采集未启用或缺少API密钥")
                return []
            
            logger.info("开始采集Twitter数据...")
            
            # Twitter API v2调用示例
            # 注意：需要正确的认证和API调用
            items = []
            
            # 这里需要实现Twitter API调用
            # 由于API复杂性，提供基础框架
            
            logger.info(f"Twitter采集完成: {len(items)}条")
            return items
            
        except Exception as e:
            logger.error(f"Twitter采集失败: {e}")
            return []
    
    def _apply_filters(self, item: Dict[str, Any]) -> bool:
        """应用内容过滤器"""
        filters = self.config.get('filters', {})
        
        # 检查内容长度
        content = item.get('title', '') + item.get('summary', '')
        if len(content) < filters.get('min_content_length', 0):
            return False
        
        # 检查排除关键词
        exclude_keywords = filters.get('exclude_keywords', [])
        for keyword in exclude_keywords:
            if keyword.lower() in content.lower():
                return False
        
        # 检查包含关键词（可选）
        include_keywords = filters.get('include_keywords', [])
        if include_keywords:
            has_keyword = any(keyword.lower() in content.lower() for keyword in include_keywords)
            if not has_keyword:
                return False
        
        return True
    
    async def collect_all_channels(self) -> List[Dict[str, Any]]:
        """采集所有渠道的信息"""
        logger.info("开始多渠道信息采集...")
        
        all_items = []
        
        # 并发采集各个渠道
        tasks = [
            self.collect_tencent_news(),
            self.collect_feishu_community(),
            self.collect_youtube_data(),
            self.collect_twitter_data()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, list):
                all_items.extend(result)
            elif isinstance(result, Exception):
                logger.error(f"采集任务失败: {result}")
        
        # 去重和排序
        all_items = self._deduplicate_items(all_items)
        all_items.sort(key=lambda x: x.get('collected_time', ''), reverse=True)
        
        logger.info(f"多渠道采集完成，共获取 {len(all_items)} 条信息")
        
        await self.close_session()
        return all_items
    
    def _deduplicate_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重处理"""
        seen_titles = set()
        unique_items = []
        
        for item in items:
            title = item.get('title', '').strip().lower()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_items.append(item)
        
        return unique_items
    
    def save_collected_data(self, items: List[Dict[str, Any]], output_path: str = None):
        """保存采集的数据"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"data/collected_info_{timestamp}.json"
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(items, f, ensure_ascii=False, indent=2)
        
        logger.info(f"采集数据已保存: {output_file}")


async def main():
    """主函数 - 用于测试"""
    collector = MultiChannelCollector()
    
    try:
        # 采集所有渠道信息
        items = await collector.collect_all_channels()
        
        # 保存数据
        collector.save_collected_data(items)
        
        # 显示结果
        print(f"采集完成，共获取 {len(items)} 条信息")
        for item in items[:5]:  # 显示前5条
            print(f"- {item['source']}: {item['title']}")
    
    except Exception as e:
        logger.error(f"采集过程出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
