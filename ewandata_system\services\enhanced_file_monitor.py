"""
增强版文件监控服务
支持递归扫描、多格式处理、混合AI分析、工作流集成
"""

import os
import sys
import asyncio
import time
import threading
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Set
import logging

# 添加系统路径
sys.path.append('.')

logger = logging.getLogger(__name__)

class EnhancedFileMonitor:
    """增强版文件监控器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化增强文件监控器
        
        Args:
            config: 监控配置
        """
        self.config = config
        self.watch_folder = Path(config.get('watch_folder', r'C:\Users\<USER>\Desktop\临时记'))
        self.output_folder = Path(config.get('output_folder', 'data/processed'))
        self.knowledge_base_folder = Path(config.get('knowledge_base_folder', 'data/knowledge_base'))
        
        # 创建必要的文件夹
        self.output_folder.mkdir(parents=True, exist_ok=True)
        self.knowledge_base_folder.mkdir(parents=True, exist_ok=True)
        self.watch_folder.mkdir(parents=True, exist_ok=True)
        
        # 支持的文件格式
        self.supported_formats = {
            'text': ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv'],
            'office': ['.doc', '.docx', '.pdf', '.ppt', '.pptx', '.xls', '.xlsx'],
            'image': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff'],
            'archive': ['.zip', '.rar', '.7z', '.tar', '.gz']
        }
        
        # 处理状态
        self.processed_files: Set[str] = set()
        self.processing_queue: List[Path] = []
        self.is_running = False
        
        # AI服务和组件
        self.hybrid_ai_manager = None
        self.document_processor = None
        self.knowledge_graph = None
        self.n8n_workflow = None
        
        logger.info(f"增强文件监控器初始化完成: {self.watch_folder}")
    
    async def initialize_services(self):
        """初始化AI服务和组件"""
        try:
            # 初始化混合AI管理器
            from services.hybrid_ai_manager import get_hybrid_ai_manager
            self.hybrid_ai_manager = get_hybrid_ai_manager()
            
            # 初始化文档处理器
            from processors.enhanced_document_processor import EnhancedDocumentProcessor
            self.document_processor = EnhancedDocumentProcessor()
            
            # 初始化知识图谱
            from services.knowledge_graph import KnowledgeGraph
            self.knowledge_graph = KnowledgeGraph()
            
            # 初始化n8n工作流
            from services.n8n_workflow_manager import N8nWorkflowManager
            self.n8n_workflow = N8nWorkflowManager(self.config.get('n8n_config', {}))
            
            logger.info("所有服务初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"服务初始化失败: {e}")
            return False
    
    def recursive_scan_files(self) -> List[Path]:
        """递归扫描所有文件"""
        all_files = []
        
        try:
            # 使用rglob递归扫描所有子目录
            for file_path in self.watch_folder.rglob("*"):
                if file_path.is_file() and self._should_process_file(file_path):
                    all_files.append(file_path)
            
            logger.info(f"递归扫描完成，发现 {len(all_files)} 个待处理文件")
            
            # 按文件类型分组统计
            type_stats = {}
            for file_path in all_files:
                file_type = self._get_file_type(file_path)
                type_stats[file_type] = type_stats.get(file_type, 0) + 1
            
            logger.info(f"文件类型统计: {type_stats}")
            
            return all_files
            
        except Exception as e:
            logger.error(f"递归扫描失败: {e}")
            return []
    
    def _should_process_file(self, file_path: Path) -> bool:
        """判断是否应该处理文件"""
        # 跳过隐藏文件和临时文件
        if file_path.name.startswith('.') or file_path.name.startswith('~'):
            return False
        
        # 跳过系统文件
        if file_path.name.lower() in ['thumbs.db', 'desktop.ini']:
            return False
        
        # 检查文件扩展名
        file_ext = file_path.suffix.lower()
        for format_type, extensions in self.supported_formats.items():
            if file_ext in extensions:
                return True
        
        return False
    
    def _get_file_type(self, file_path: Path) -> str:
        """获取文件类型"""
        file_ext = file_path.suffix.lower()
        
        for format_type, extensions in self.supported_formats.items():
            if file_ext in extensions:
                return format_type
        
        return 'unknown'
    
    async def process_file_with_hybrid_ai(self, file_path: Path) -> Dict[str, Any]:
        """使用混合AI处理文件"""
        try:
            logger.info(f"开始处理文件: {file_path.name}")
            
            # 1. 基础文档处理
            doc_data = await self.document_processor.process_file(file_path)
            
            if not doc_data.get('success', False):
                return {'success': False, 'error': doc_data.get('error', '处理失败')}
            
            # 2. 确定AI处理策略
            file_type = self._get_file_type(file_path)
            content = doc_data.get('content', '')
            
            # 构建AI请求
            ai_request = {
                'content': content,
                'file_info': {
                    'name': file_path.name,
                    'type': file_type,
                    'path': str(file_path),
                    'size': len(content)
                },
                'task_type': self._determine_task_type(file_path, content),
                'max_tokens': 1000
            }
            
            # 3. 混合AI分析
            ai_result = await self.hybrid_ai_manager.process_request(ai_request)
            
            if not ai_result.get('success', False):
                return {'success': False, 'error': f"AI分析失败: {ai_result.get('error', '')}"}
            
            # 4. 知识图谱更新
            await self.knowledge_graph.add_document(doc_data, ai_result)
            
            # 5. 生成输出
            output_data = {
                'file_info': doc_data,
                'ai_analysis': ai_result,
                'processing_metadata': {
                    'processed_time': datetime.now().isoformat(),
                    'ai_model_used': ai_result.get('metadata', {}).get('processed_by', 'unknown'),
                    'processing_time': ai_result.get('metadata', {}).get('response_time', 0)
                }
            }
            
            # 6. 保存结果
            await self._save_processing_results(file_path, output_data)
            
            # 7. 触发n8n工作流
            if self.n8n_workflow:
                await self.n8n_workflow.trigger_file_processed(output_data)
            
            logger.info(f"文件处理完成: {file_path.name}")
            return {'success': True, 'data': output_data}
            
        except Exception as e:
            logger.error(f"文件处理失败 {file_path.name}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _determine_task_type(self, file_path: Path, content: str) -> str:
        """确定任务类型以选择合适的AI模型"""
        file_ext = file_path.suffix.lower()
        
        # 代码文件 -> CodeLlama-7B
        if file_ext in ['.py', '.js', '.html', '.css', '.json']:
            return 'code_analysis'
        
        # 快速响应文件 -> Phi-3-mini
        if len(content) < 500:
            return 'quick_response'
        
        # 创意内容 -> 外部AI
        if any(keyword in content.lower() for keyword in ['创意', '故事', '设计', '想象']):
            return 'creative_generation'
        
        # 复杂分析 -> 外部AI
        if len(content) > 5000 or any(keyword in content.lower() for keyword in ['深入分析', '复杂', '详细研究']):
            return 'complex_reasoning'
        
        # 默认文档分析 -> Qwen2-7B
        return 'document_analysis'
    
    async def _save_processing_results(self, file_path: Path, output_data: Dict[str, Any]):
        """保存处理结果"""
        try:
            # 生成输出文件名
            base_name = file_path.stem
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存JSON格式
            json_output = self.output_folder / f"{base_name}_{timestamp}.json"
            with open(json_output, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            # 保存Markdown格式
            md_output = self.output_folder / f"{base_name}_{timestamp}.md"
            md_content = self._generate_markdown_report(file_path, output_data)
            with open(md_output, 'w', encoding='utf-8') as f:
                f.write(md_content)
            
            logger.info(f"结果已保存: {json_output.name}, {md_output.name}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def _generate_markdown_report(self, file_path: Path, output_data: Dict[str, Any]) -> str:
        """生成Markdown报告"""
        ai_analysis = output_data.get('ai_analysis', {}).get('result', {})
        metadata = output_data.get('processing_metadata', {})
        
        return f"""# {file_path.name} - 混合AI处理报告

## 📁 文件信息
- **文件名**: {file_path.name}
- **文件路径**: {file_path.parent}
- **文件类型**: {self._get_file_type(file_path)}
- **处理时间**: {metadata.get('processed_time', 'N/A')}
- **AI模型**: {metadata.get('ai_model_used', 'N/A')}
- **处理耗时**: {metadata.get('processing_time', 0):.2f}秒

## 🧠 AI分析结果

### 关键词
{', '.join(ai_analysis.get('keywords', [])[:10])}

### 摘要
{ai_analysis.get('summary', 'N/A')}

### 分类信息
- **类别**: {ai_analysis.get('classification', {}).get('category', 'N/A')}
- **主题**: {', '.join(ai_analysis.get('classification', {}).get('topics', []))}
- **重要性**: {ai_analysis.get('classification', {}).get('importance', 0)}/10
- **标签**: {', '.join(ai_analysis.get('classification', {}).get('tags', []))}

## 🔗 知识关联
{ai_analysis.get('knowledge_links', '暂无关联信息')}

---
*由Ewandata混合AI系统自动生成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    async def start_monitoring(self):
        """开始监控和处理"""
        if self.is_running:
            logger.info("文件监控已在运行")
            return
        
        # 初始化服务
        if not await self.initialize_services():
            logger.error("服务初始化失败，无法启动监控")
            return
        
        self.is_running = True
        logger.info("开始增强文件监控...")
        
        try:
            # 1. 递归扫描现有文件
            existing_files = self.recursive_scan_files()
            
            # 2. 处理现有文件
            for file_path in existing_files:
                if str(file_path) not in self.processed_files:
                    result = await self.process_file_with_hybrid_ai(file_path)
                    
                    if result.get('success', False):
                        self.processed_files.add(str(file_path))
                        
                        # 模拟文件清理（实际项目中会移动到已处理文件夹）
                        logger.info(f"文件处理完成，标记为已处理: {file_path.name}")
                    
                    # 短暂延迟避免系统过载
                    await asyncio.sleep(0.5)
            
            # 3. 生成知识库索引
            await self._generate_knowledge_base_index()
            
            logger.info(f"文件监控完成，共处理 {len(self.processed_files)} 个文件")
            
        except Exception as e:
            logger.error(f"文件监控过程中出错: {e}")
        finally:
            self.is_running = False
    
    async def _generate_knowledge_base_index(self):
        """生成知识库索引"""
        try:
            if self.knowledge_graph:
                index_data = await self.knowledge_graph.generate_index()
                
                # 保存知识库索引
                index_file = self.knowledge_base_folder / "knowledge_index.json"
                with open(index_file, 'w', encoding='utf-8') as f:
                    json.dump(index_data, f, ensure_ascii=False, indent=2)
                
                # 生成知识库目录
                catalog_file = self.knowledge_base_folder / "knowledge_catalog.md"
                catalog_content = self._generate_knowledge_catalog(index_data)
                with open(catalog_file, 'w', encoding='utf-8') as f:
                    f.write(catalog_content)
                
                logger.info(f"知识库索引已生成: {index_file}")
                
        except Exception as e:
            logger.error(f"生成知识库索引失败: {e}")
    
    def _generate_knowledge_catalog(self, index_data: Dict[str, Any]) -> str:
        """生成知识库目录"""
        return f"""# 📚 Ewandata知识库目录

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 统计信息
- 总文档数: {index_data.get('total_documents', 0)}
- 主题分类: {len(index_data.get('categories', {}))}
- 关键词数: {len(index_data.get('keywords', {}))}

## 🗂️ 分类目录
{self._format_categories(index_data.get('categories', {}))}

## 🔗 知识关联图
{self._format_knowledge_links(index_data.get('knowledge_links', []))}

---
*由Ewandata混合AI系统自动生成*
"""
    
    def _format_categories(self, categories: Dict[str, Any]) -> str:
        """格式化分类信息"""
        result = []
        for category, docs in categories.items():
            result.append(f"### {category}")
            for doc in docs[:5]:  # 显示前5个文档
                result.append(f"- {doc.get('title', 'N/A')}")
            if len(docs) > 5:
                result.append(f"- ... 还有 {len(docs) - 5} 个文档")
            result.append("")
        return "\n".join(result)
    
    def _format_knowledge_links(self, links: List[Dict[str, Any]]) -> str:
        """格式化知识关联"""
        result = []
        for link in links[:10]:  # 显示前10个关联
            result.append(f"- {link.get('source', 'N/A')} ↔ {link.get('target', 'N/A')}")
        return "\n".join(result)


async def main():
    """测试函数"""
    config = {
        'watch_folder': r'C:\Users\<USER>\Desktop\临时记',
        'output_folder': 'data/processed',
        'knowledge_base_folder': 'data/knowledge_base',
        'n8n_config': {
            'base_url': 'http://localhost:5678',
            'webhook_url': '/webhook/file-processed'
        }
    }
    
    monitor = EnhancedFileMonitor(config)
    await monitor.start_monitoring()


if __name__ == "__main__":
    asyncio.run(main())
