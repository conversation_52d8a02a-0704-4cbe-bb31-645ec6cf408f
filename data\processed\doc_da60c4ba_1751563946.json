{"id": "doc_da60c4ba_1751563946", "timestamp": "2025-07-04T01:32:26.906108", "document": {"file_name": "test_document_1751563945.txt", "file_path": "C:\\Users\\<USER>\\Desktop\\临时记\\test_document_1751563945.txt", "file_extension": ".txt", "size": 587, "size_mb": 0.0005598068237304688, "created_time": "2025-07-04T01:32:25.987670", "modified_time": "2025-07-04T01:32:25.987670", "file_hash": "da60c4baff6f4b1483d0ba5a75d24d74", "mime_type": "text/plain", "type": "text", "content": "Ewandata系统测试文档\n\n创建时间: 2025-07-04 01:32:25\n测试目的: 验证系统自动处理文本文件的能力\n\n## 测试内容\n这是一个用于测试Ewandata系统文件处理能力的文档。\n\n### 关键信息\n- 项目名称: Ewandata知识管理系统\n- 技术栈: Python, FastAPI, ChromaDB\n- 目标: 自动化知识管理和项目协同\n\n### 测试要求\n1. 自动检测文件变化\n2. 提取关键信息和摘要\n3. 生成结构化数据\n4. 上传到GitHub\n5. 清理原文件\n\n这个文件应该被系统自动处理并转换为知识库条目。\n", "encoding": "utf-8", "line_count": 21, "char_count": 280, "word_count": 35, "processing_time": "2025-07-04T01:32:26.865211", "processor_version": "1.0.0"}}